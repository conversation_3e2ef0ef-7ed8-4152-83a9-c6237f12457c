{"name": "coveyor-poc", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "node prisma/seed.js", "db:studio": "prisma studio", "postinstall": "prisma generate"}, "dependencies": {"next": "14.0.4", "react": "^18", "react-dom": "^18", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.22.4", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "lucide-react": "^0.303.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "date-fns": "^3.0.6", "uuid": "^9.0.1"}, "devDependencies": {"tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8", "eslint": "^8", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9"}, "engines": {"node": ">=18.0.0"}}