{"name": "coveyor-poc", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "node prisma/seed.js", "db:studio": "prisma studio", "postinstall": "prisma generate"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@prisma/client": "^6.9.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-form": "^0.0.3", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^3.0.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.303.0", "next": "14.0.4", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.2.0", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "prisma": "^6.9.0", "tailwindcss": "^3.3.0"}, "engines": {"node": ">=18.0.0"}}