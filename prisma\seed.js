const { PrismaClient } = require("@prisma/client");
const bcrypt = require("bcryptjs");

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Starting database seed...");

  // Create admin user
  const adminPassword = await bcrypt.hash("admin123", 12);
  const admin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      password: adminPassword,
      firstName: "Admin",
      lastName: "User",
      role: "ADMIN",
      status: "ACTIVE",
    },
  });

  console.log("✅ Created admin user:", admin.email);

  // Create manager user
  const managerPassword = await bcrypt.hash("manager123", 12);
  const manager = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      password: manager<PERSON>assword,
      firstName: "Manager",
      lastName: "User",
      role: "MANAGER",
      status: "ACTIVE",
      invitedBy: admin.id,
    },
  });

  console.log("✅ Created manager user:", manager.email);

  // Create regular users
  const users = [
    {
      email: "<EMAIL>",
      firstName: "John",
      lastName: "Doe",
      role: "USER",
    },
    {
      email: "<EMAIL>",
      firstName: "Jane",
      lastName: "Smith",
      role: "USER",
    },
    {
      email: "<EMAIL>",
      firstName: "Bob",
      lastName: "Wilson",
      role: "USER",
      status: "INACTIVE",
    },
  ];

  const userPassword = await bcrypt.hash("user123", 12);
  const createdUsers = [];

  for (const userData of users) {
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        ...userData,
        password: userPassword,
        status: userData.status || "ACTIVE",
        invitedBy: manager.id,
      },
    });
    createdUsers.push(user);
    console.log("✅ Created user:", user.email);
  }

  // Create some audit logs
  await prisma.auditLog.createMany({
    data: [
      {
        userId: admin.id,
        action: "LOGIN",
        resource: "AUTH",
        details: { method: "email" },
        ipAddress: "127.0.0.1",
      },
      {
        userId: admin.id,
        action: "CREATE",
        resource: "USER",
        resourceId: manager.id,
        details: { role: "MANAGER" },
        ipAddress: "127.0.0.1",
      },
      {
        userId: manager.id,
        action: "LOGIN",
        resource: "AUTH",
        details: { method: "email" },
        ipAddress: "127.0.0.1",
      },
    ],
  });

  console.log("✅ Created audit logs");

  // Create Equipment Categories
  const categories = [
    { name: "Belt Conveyors", description: "Standard belt conveyor systems" },
    {
      name: "Roller Conveyors",
      description: "Gravity and powered roller systems",
    },
    { name: "Chain Conveyors", description: "Heavy duty chain driven systems" },
    { name: "Screw Conveyors", description: "Bulk material handling systems" },
    { name: "Pneumatic Systems", description: "Air powered conveyor systems" },
  ];

  const createdCategories = [];
  for (const categoryData of categories) {
    const category = await prisma.equipmentCategory.upsert({
      where: { name: categoryData.name },
      update: {},
      create: categoryData,
    });
    createdCategories.push(category);
    console.log("✅ Created equipment category:", category.name);
  }

  // Create Equipment Master data
  const equipmentData = [
    {
      name: "Standard Belt Conveyor 500mm",
      code: "BELT_500_STD",
      categoryId: createdCategories[0].id,
      subCategory: "Standard Width",
      description:
        "Standard 500mm width belt conveyor for light to medium loads",
      specifications: {
        width: "500mm",
        maxLoad: "50kg/m",
        speed: "0.5-2.0 m/s",
        material: "PVC Belt",
      },
      uom: "meter",
      baseRate: 15000.0,
      regionRates: {
        "North India": 15000,
        "South India": 14500,
        "West India": 15500,
        "East India": 14000,
      },
      effectiveFrom: new Date("2024-01-01"),
      costBreakup: {
        material: 8000,
        labor: 3000,
        overhead: 2000,
        profit: 2000,
      },
    },
    {
      name: "Heavy Duty Belt Conveyor 800mm",
      code: "BELT_800_HD",
      categoryId: createdCategories[0].id,
      subCategory: "Heavy Duty",
      description: "Heavy duty 800mm width belt conveyor for heavy loads",
      specifications: {
        width: "800mm",
        maxLoad: "200kg/m",
        speed: "0.3-1.5 m/s",
        material: "Rubber Belt",
      },
      uom: "meter",
      baseRate: 25000.0,
      regionRates: {
        "North India": 25000,
        "South India": 24000,
        "West India": 26000,
        "East India": 23500,
      },
      effectiveFrom: new Date("2024-01-01"),
      costBreakup: {
        material: 15000,
        labor: 4000,
        overhead: 3000,
        profit: 3000,
      },
    },
    {
      name: "Gravity Roller Conveyor",
      code: "ROLLER_GRAV_STD",
      categoryId: createdCategories[1].id,
      subCategory: "Gravity Fed",
      description: "Standard gravity roller conveyor for package handling",
      specifications: {
        rollerDiameter: "50mm",
        rollerSpacing: "150mm",
        maxLoad: "30kg/m",
        material: "Steel Rollers",
      },
      uom: "meter",
      baseRate: 8000.0,
      effectiveFrom: new Date("2024-01-01"),
      costBreakup: {
        material: 5000,
        labor: 1500,
        overhead: 800,
        profit: 700,
      },
    },
    {
      name: "Powered Roller Conveyor",
      code: "ROLLER_PWR_STD",
      categoryId: createdCategories[1].id,
      subCategory: "Motorized",
      description: "Motorized roller conveyor with variable speed control",
      specifications: {
        rollerDiameter: "60mm",
        rollerSpacing: "200mm",
        maxLoad: "100kg/m",
        motor: "1HP Variable Speed",
      },
      uom: "meter",
      baseRate: 18000.0,
      effectiveFrom: new Date("2024-01-01"),
      costBreakup: {
        material: 12000,
        labor: 3000,
        overhead: 1500,
        profit: 1500,
      },
    },
    {
      name: "Chain Conveyor Heavy Duty",
      code: "CHAIN_HD_STD",
      categoryId: createdCategories[2].id,
      subCategory: "Heavy Duty",
      description: "Heavy duty chain conveyor for industrial applications",
      specifications: {
        chainType: "Double Strand",
        maxLoad: "500kg/m",
        speed: "0.1-0.8 m/s",
        material: "Hardened Steel Chain",
      },
      uom: "meter",
      baseRate: 35000.0,
      effectiveFrom: new Date("2024-01-01"),
      costBreakup: {
        material: 22000,
        labor: 6000,
        overhead: 3500,
        profit: 3500,
      },
    },
  ];

  const createdEquipment = [];
  for (const equipment of equipmentData) {
    const created = await prisma.equipmentMaster.upsert({
      where: { code: equipment.code },
      update: {},
      create: {
        ...equipment,
        createdBy: admin.id,
      },
    });
    createdEquipment.push(created);
    console.log("✅ Created equipment:", created.name);
  }

  // Create Component Master data
  const componentData = [
    {
      name: "PVC Conveyor Belt 500mm",
      code: "BELT_PVC_500",
      category: "Rubber",
      equipmentId: createdEquipment[0].id,
      description: "High quality PVC conveyor belt for food grade applications",
      uom: "meter",
      baseRate: 850.0,
      rateEffectiveFrom: new Date("2024-01-01"),
      vendorInfo: "ABC Belting Solutions Pvt Ltd",
    },
    {
      name: "Steel Roller 50mm Diameter",
      code: "ROLLER_STEEL_50",
      category: "Steel",
      equipmentId: createdEquipment[2].id,
      description: "Precision steel roller with ball bearings",
      uom: "piece",
      baseRate: 450.0,
      rateEffectiveFrom: new Date("2024-01-01"),
      vendorInfo: "XYZ Steel Components",
    },
    {
      name: "Variable Speed Motor 1HP",
      code: "MOTOR_VAR_1HP",
      category: "Motors",
      equipmentId: createdEquipment[3].id,
      description: "Variable frequency drive motor for conveyor applications",
      uom: "piece",
      baseRate: 12500.0,
      rateEffectiveFrom: new Date("2024-01-01"),
      vendorInfo: "Motor Tech Industries",
    },
    {
      name: "Hardened Steel Chain",
      code: "CHAIN_STEEL_HD",
      category: "Steel",
      equipmentId: createdEquipment[4].id,
      description: "Heavy duty hardened steel chain for industrial use",
      uom: "meter",
      baseRate: 2800.0,
      rateEffectiveFrom: new Date("2024-01-01"),
      vendorInfo: "Industrial Chain Corp",
    },
    {
      name: "Support Structure Frame",
      code: "FRAME_SUPPORT_STD",
      category: "Steel",
      equipmentId: createdEquipment[0].id,
      description: "Galvanized steel support frame for conveyor mounting",
      uom: "meter",
      baseRate: 1200.0,
      rateEffectiveFrom: new Date("2024-01-01"),
      vendorInfo: "Steel Fab Solutions",
    },
  ];

  const createdComponents = [];
  for (const component of componentData) {
    const created = await prisma.componentMaster.upsert({
      where: { code: component.code },
      update: {},
      create: {
        ...component,
        createdBy: admin.id,
      },
    });
    createdComponents.push(created);
    console.log("✅ Created component:", created.name);
  }

  // Create Conveyor Plans
  const planData = [
    {
      planName: "Warehouse Package Sorting Line",
      conveyorType: "Belt Conveyor",
      totalLengthM: 50.0,
      loadType: "LIGHT",
      capacityTph: 25.0,
      inclinationAngle: 0.0,
      driveType: "Motorized - AC Motor",
      environment: "Indoor - Clean",
      region: "North India",
      siteConditions:
        "Standard warehouse environment with concrete flooring. Adequate space for installation and maintenance.",
      specialRequirements:
        "Food grade belt required for package handling. Emergency stop stations every 10 meters.",
      status: "Approved",
      createdBy: manager.id,
    },
    {
      planName: "Mining Material Transport System",
      conveyorType: "Chain Conveyor",
      totalLengthM: 200.0,
      loadType: "HEAVY",
      capacityTph: 150.0,
      inclinationAngle: 15.0,
      driveType: "Motorized - AC Motor",
      environment: "Outdoor - Harsh Weather",
      region: "East India",
      siteConditions:
        "Outdoor mining site with dusty conditions. Requires weather protection and heavy duty construction.",
      specialRequirements:
        "Explosion proof motors required. Dust collection system integration needed.",
      status: "Submitted",
      createdBy: createdUsers[0]?.id || admin.id,
    },
    {
      planName: "Assembly Line Feeder",
      conveyorType: "Roller Conveyor",
      totalLengthM: 25.0,
      loadType: "MEDIUM",
      capacityTph: 40.0,
      inclinationAngle: 0.0,
      driveType: "Motorized - Servo Motor",
      environment: "Indoor - Clean",
      region: "West India",
      siteConditions:
        "Clean manufacturing environment with precise positioning requirements.",
      specialRequirements:
        "Variable speed control with position feedback. Integration with PLC system.",
      status: "Draft",
      createdBy: createdUsers[1]?.id || admin.id,
    },
  ];

  const createdPlans = [];
  for (const plan of planData) {
    const created = await prisma.conveyorPlan.create({
      data: plan,
    });
    createdPlans.push(created);
    console.log("✅ Created conveyor plan:", created.planName);
  }

  console.log("🎉 Database seeded successfully!");
  console.log("\n📋 Test Accounts:");
  console.log("Admin: <EMAIL> / admin123");
  console.log("Manager: <EMAIL> / manager123");
  console.log("User: <EMAIL> / user123");
  console.log("\n📊 Sample Data Created:");
  console.log(`- ${createdCategories.length} Equipment Categories`);
  console.log(`- ${createdEquipment.length} Equipment Items`);
  console.log(`- ${createdComponents.length} Components`);
  console.log(`- ${createdPlans.length} Conveyor Plans`);
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error("❌ Seed failed:", e);
    await prisma.$disconnect();
    process.exit(1);
  });
