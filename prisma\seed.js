const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: 'ADMIN',
      status: 'ACTIVE',
    },
  })

  console.log('✅ Created admin user:', admin.email)

  // Create manager user
  const managerPassword = await bcrypt.hash('manager123', 12)
  const manager = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: managerPassword,
      firstName: 'Manager',
      lastName: 'User',
      role: 'MANAGER',
      status: 'ACTIVE',
      invitedBy: admin.id,
    },
  })

  console.log('✅ Created manager user:', manager.email)

  // Create regular users
  const users = [
    {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      role: 'USER',
    },
    {
      email: '<EMAIL>',
      firstName: 'Jane',
      lastName: 'Smith',
      role: 'USER',
    },
    {
      email: '<EMAIL>',
      firstName: 'Bob',
      lastName: 'Wilson',
      role: 'USER',
      status: 'INACTIVE',
    },
  ]

  const userPassword = await bcrypt.hash('user123', 12)

  for (const userData of users) {
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        ...userData,
        password: userPassword,
        status: userData.status || 'ACTIVE',
        invitedBy: manager.id,
      },
    })
    console.log('✅ Created user:', user.email)
  }

  // Create some audit logs
  await prisma.auditLog.createMany({
    data: [
      {
        userId: admin.id,
        action: 'LOGIN',
        resource: 'AUTH',
        details: { method: 'email' },
        ipAddress: '127.0.0.1',
      },
      {
        userId: admin.id,
        action: 'CREATE',
        resource: 'USER',
        resourceId: manager.id,
        details: { role: 'MANAGER' },
        ipAddress: '127.0.0.1',
      },
      {
        userId: manager.id,
        action: 'LOGIN',
        resource: 'AUTH',
        details: { method: 'email' },
        ipAddress: '127.0.0.1',
      },
    ],
  })

  console.log('✅ Created audit logs')
  console.log('🎉 Database seeded successfully!')
  console.log('\n📋 Test Accounts:')
  console.log('Admin: <EMAIL> / admin123')
  console.log('Manager: <EMAIL> / manager123')
  console.log('User: <EMAIL> / user123')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ Seed failed:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
