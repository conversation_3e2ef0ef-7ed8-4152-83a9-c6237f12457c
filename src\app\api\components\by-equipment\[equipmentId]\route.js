import { getCurrentUser } from '@/lib/auth/session'
import { getComponentsByEquipment } from '@/features/components/services/componentService'
import { successResponse, unauthorizedResponse, forbiddenResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view components')
    }

    const equipmentId = params.equipmentId
    const components = await getComponentsByEquipment(equipmentId)
    
    return successResponse(components, 'Components retrieved successfully')
    
  } catch (error) {
    return handleApiError(error)
  }
}
