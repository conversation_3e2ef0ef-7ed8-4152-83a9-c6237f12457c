import { getCurrentUser } from '@/lib/auth/session'
import { approveConveyorPlan } from '@/features/conveyor/services/conveyorService'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission - only managers and admins can approve
    if (!hasPermission(user.role, PERMISSIONS.USER_UPDATE) || user.role === 'USER') {
      return forbiddenResponse('Insufficient permissions to approve conveyor plan')
    }

    const planId = params.id
    
    // Approve plan
    const approvedPlan = await approveConveyorPlan(planId, user.id)
    
    return successResponse(approvedPlan, 'Conveyor plan approved successfully')
    
  } catch (error) {
    if (error.message === 'Conveyor plan not found') {
      return notFoundResponse('Conveyor plan not found')
    }
    return handleApiError(error)
  }
}
