"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { LoadingSpinner } from "@/components/common/LoadingSpinner";
import { hasPermission, hasRoleLevel } from "@/lib/auth/permissions";

export function AuthGuard({
  children,
  requireAuth = true,
  requiredRole = null,
  requiredPermission = null,
  fallback = null,
}) {
  const { user, loading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && requireAuth && !isAuthenticated) {
      router.push("/login");
    }
  }, [loading, requireAuth, isAuthenticated, router]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Checking authentication...</p>
          <p className="mt-2 text-sm text-gray-500">
            If this takes too long, please refresh the page
          </p>
        </div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return (
      fallback || (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <p className="text-gray-600">Redirecting to login...</p>
          </div>
        </div>
      )
    );
  }

  // If user is authenticated, check role and permission requirements
  if (isAuthenticated) {
    // Check role requirement
    if (requiredRole && !hasRoleLevel(user.role, requiredRole)) {
      return (
        fallback || (
          <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Access Denied
              </h2>
              <p className="text-gray-600">
                You don't have permission to access this page.
              </p>
            </div>
          </div>
        )
      );
    }

    // Check permission requirement
    if (requiredPermission && !hasPermission(user.role, requiredPermission)) {
      return (
        fallback || (
          <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Access Denied
              </h2>
              <p className="text-gray-600">
                You don't have the required permission to access this page.
              </p>
            </div>
          </div>
        )
      );
    }
  }

  return children;
}

// Higher-order component for protecting pages
export function withAuth(Component, options = {}) {
  return function AuthenticatedComponent(props) {
    return (
      <AuthGuard {...options}>
        <Component {...props} />
      </AuthGuard>
    );
  };
}

// Role-based guards
export function AdminGuard({ children, fallback }) {
  return (
    <AuthGuard requiredRole="ADMIN" fallback={fallback}>
      {children}
    </AuthGuard>
  );
}

export function ManagerGuard({ children, fallback }) {
  return (
    <AuthGuard requiredRole="MANAGER" fallback={fallback}>
      {children}
    </AuthGuard>
  );
}

// Permission-based guard
export function PermissionGuard({ children, permission, fallback }) {
  return (
    <AuthGuard requiredPermission={permission} fallback={fallback}>
      {children}
    </AuthGuard>
  );
}
