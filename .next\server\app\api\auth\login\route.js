"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/login/route";
exports.ids = ["app/api/auth/login/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "./action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_MobioProjects_coveyor_poc_src_app_api_auth_login_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/login/route.js */ \"(rsc)/./src/app/api/auth/login/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/login/route\",\n        pathname: \"/api/auth/login\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/login/route\"\n    },\n    resolvedPagePath: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\api\\\\auth\\\\login\\\\route.js\",\n    nextConfigOutput,\n    userland: D_MobioProjects_coveyor_poc_src_app_api_auth_login_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/login/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/login/route.js":
/*!*****************************************!*\
  !*** ./src/app/api/auth/login/route.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-request */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-request.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _features_auth_services_authService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/auth/services/authService */ \"(rsc)/./src/features/auth/services/authService.js\");\n/* harmony import */ var _features_auth_validation_authSchemas__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/auth/validation/authSchemas */ \"(rsc)/./src/features/auth/validation/authSchemas.js\");\n/* harmony import */ var _lib_api_response__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/response */ \"(rsc)/./src/lib/api/response.js\");\n\n\n\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate request body\n        const validatedData = _features_auth_validation_authSchemas__WEBPACK_IMPORTED_MODULE_3__.loginSchema.parse(body);\n        // Get client info\n        const userAgent = request.headers.get(\"user-agent\");\n        const forwarded = request.headers.get(\"x-forwarded-for\");\n        const ipAddress = forwarded ? forwarded.split(\",\")[0] : request.headers.get(\"x-real-ip\") || \"unknown\";\n        // Authenticate user\n        const result = await (0,_features_auth_services_authService__WEBPACK_IMPORTED_MODULE_2__.authenticateUser)(validatedData.email, validatedData.password, userAgent, ipAddress);\n        // Set auth cookie\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        cookieStore.set(\"auth-token\", result.token, {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"lax\",\n            maxAge: 60 * 60 * 24 * 7,\n            path: \"/\"\n        });\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_4__.successResponse)({\n            user: result.user,\n            session: {\n                id: result.session.id,\n                expiresAt: result.session.expiresAt\n            }\n        }, \"Login successful\");\n    } catch (error) {\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_4__.handleApiError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/login/route.js\n");

/***/ }),

/***/ "(rsc)/./src/features/auth/services/authService.js":
/*!***************************************************!*\
  !*** ./src/features/auth/services/authService.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authenticateUser: () => (/* binding */ authenticateUser),\n/* harmony export */   changeUserPassword: () => (/* binding */ changeUserPassword),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   logoutUser: () => (/* binding */ logoutUser),\n/* harmony export */   logoutUserFromAllDevices: () => (/* binding */ logoutUserFromAllDevices),\n/* harmony export */   updateUserProfile: () => (/* binding */ updateUserProfile)\n/* harmony export */ });\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n/* harmony import */ var _lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/encryption */ \"(rsc)/./src/lib/utils/encryption.js\");\n/* harmony import */ var _lib_auth_session__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth/session */ \"(rsc)/./src/lib/auth/session.js\");\n\n\n\n/**\n * Authenticate user with email and password\n */ async function authenticateUser(email, password, userAgent = null, ipAddress = null) {\n    // Find user by email\n    const user = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.findUnique({\n        where: {\n            email: email.toLowerCase()\n        },\n        select: {\n            id: true,\n            email: true,\n            password: true,\n            firstName: true,\n            lastName: true,\n            role: true,\n            status: true,\n            avatar: true\n        }\n    });\n    if (!user) {\n        throw new Error(\"Invalid credentials\");\n    }\n    // Check if user is active\n    if (user.status !== \"ACTIVE\") {\n        throw new Error(\"Account is not active\");\n    }\n    // Verify password\n    const isValidPassword = await (0,_lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__.verifyPassword)(password, user.password);\n    if (!isValidPassword) {\n        throw new Error(\"Invalid credentials\");\n    }\n    // Create session\n    const { session, token } = await (0,_lib_auth_session__WEBPACK_IMPORTED_MODULE_2__.createSession)(user.id, userAgent, ipAddress);\n    // Update last login time\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.update({\n        where: {\n            id: user.id\n        },\n        data: {\n            lastLoginAt: new Date()\n        }\n    });\n    // Log authentication\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auditLog.create({\n        data: {\n            userId: user.id,\n            action: \"LOGIN\",\n            resource: \"AUTH\",\n            details: {\n                method: \"email\"\n            },\n            ipAddress,\n            userAgent\n        }\n    });\n    // Return user data without password\n    const { password: _, ...userWithoutPassword } = user;\n    return {\n        user: userWithoutPassword,\n        session,\n        token\n    };\n}\n/**\n * Create new user account\n */ async function createUser(userData) {\n    const { email, password, firstName, lastName, role = \"USER\", invitedBy = null } = userData;\n    // Check if user already exists\n    const existingUser = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.findUnique({\n        where: {\n            email: email.toLowerCase()\n        }\n    });\n    if (existingUser) {\n        throw new Error(\"User already exists\");\n    }\n    // Hash password\n    const hashedPassword = await (0,_lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__.hashPassword)(password);\n    // Create user\n    const user = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.create({\n        data: {\n            email: email.toLowerCase(),\n            password: hashedPassword,\n            firstName,\n            lastName,\n            role,\n            status: \"ACTIVE\",\n            invitedBy\n        },\n        select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n            role: true,\n            status: true,\n            avatar: true,\n            createdAt: true\n        }\n    });\n    // Log user creation\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auditLog.create({\n        data: {\n            userId: invitedBy,\n            action: \"CREATE\",\n            resource: \"USER\",\n            resourceId: user.id,\n            details: {\n                email: user.email,\n                role: user.role\n            }\n        }\n    });\n    return user;\n}\n/**\n * Logout user\n */ async function logoutUser(token, userId = null) {\n    // Delete session\n    await (0,_lib_auth_session__WEBPACK_IMPORTED_MODULE_2__.deleteSession)(token);\n    // Log logout\n    if (userId) {\n        await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auditLog.create({\n            data: {\n                userId,\n                action: \"LOGOUT\",\n                resource: \"AUTH\",\n                details: {\n                    method: \"manual\"\n                }\n            }\n        });\n    }\n}\n/**\n * Logout user from all devices\n */ async function logoutUserFromAllDevices(userId) {\n    // Delete all user sessions\n    await (0,_lib_auth_session__WEBPACK_IMPORTED_MODULE_2__.deleteAllUserSessions)(userId);\n    // Log logout from all devices\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auditLog.create({\n        data: {\n            userId,\n            action: \"LOGOUT_ALL\",\n            resource: \"AUTH\",\n            details: {\n                method: \"all_devices\"\n            }\n        }\n    });\n}\n/**\n * Change user password\n */ async function changeUserPassword(userId, currentPassword, newPassword) {\n    // Get user with current password\n    const user = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.findUnique({\n        where: {\n            id: userId\n        },\n        select: {\n            id: true,\n            password: true\n        }\n    });\n    if (!user) {\n        throw new Error(\"User not found\");\n    }\n    // Verify current password\n    const isValidPassword = await (0,_lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__.verifyPassword)(currentPassword, user.password);\n    if (!isValidPassword) {\n        throw new Error(\"Current password is incorrect\");\n    }\n    // Hash new password\n    const hashedNewPassword = await (0,_lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__.hashPassword)(newPassword);\n    // Update password\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.update({\n        where: {\n            id: userId\n        },\n        data: {\n            password: hashedNewPassword\n        }\n    });\n    // Log password change\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auditLog.create({\n        data: {\n            userId,\n            action: \"PASSWORD_CHANGE\",\n            resource: \"AUTH\",\n            details: {\n                method: \"self\"\n            }\n        }\n    });\n    // Logout from all other devices for security\n    await logoutUserFromAllDevices(userId);\n}\n/**\n * Update user profile\n */ async function updateUserProfile(userId, profileData) {\n    const { firstName, lastName, email } = profileData;\n    // Check if email is already taken by another user\n    if (email) {\n        const existingUser = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.findFirst({\n            where: {\n                email: email.toLowerCase(),\n                NOT: {\n                    id: userId\n                }\n            }\n        });\n        if (existingUser) {\n            throw new Error(\"Email is already taken\");\n        }\n    }\n    // Update user profile\n    const updatedUser = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.update({\n        where: {\n            id: userId\n        },\n        data: {\n            firstName,\n            lastName,\n            email: email?.toLowerCase()\n        },\n        select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n            role: true,\n            status: true,\n            avatar: true\n        }\n    });\n    // Log profile update\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auditLog.create({\n        data: {\n            userId,\n            action: \"PROFILE_UPDATE\",\n            resource: \"USER\",\n            resourceId: userId,\n            details: {\n                fields: Object.keys(profileData)\n            }\n        }\n    });\n    return updatedUser;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/auth/services/authService.js\n");

/***/ }),

/***/ "(rsc)/./src/features/auth/validation/authSchemas.js":
/*!*****************************************************!*\
  !*** ./src/features/auth/validation/authSchemas.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   changePasswordSchema: () => (/* binding */ changePasswordSchema),\n/* harmony export */   forgotPasswordSchema: () => (/* binding */ forgotPasswordSchema),\n/* harmony export */   loginSchema: () => (/* binding */ loginSchema),\n/* harmony export */   profileUpdateSchema: () => (/* binding */ profileUpdateSchema),\n/* harmony export */   registerSchema: () => (/* binding */ registerSchema),\n/* harmony export */   resetPasswordSchema: () => (/* binding */ resetPasswordSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n/**\n * Login form validation schema\n */ const loginSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Email is required\").email(\"Please enter a valid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Password is required\").min(6, \"Password must be at least 6 characters\")\n});\n/**\n * Register form validation schema\n */ const registerSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Email is required\").email(\"Please enter a valid email address\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(8, \"Password must be at least 8 characters\").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, \"Password must contain at least one uppercase letter, one lowercase letter, and one number\"),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Please confirm your password\"),\n    firstName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"First name is required\").min(2, \"First name must be at least 2 characters\"),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Last name is required\").min(2, \"Last name must be at least 2 characters\")\n}).refine((data)=>data.password === data.confirmPassword, {\n    message: \"Passwords don't match\",\n    path: [\n        \"confirmPassword\"\n    ]\n});\n/**\n * Change password validation schema\n */ const changePasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    currentPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Current password is required\"),\n    newPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(8, \"Password must be at least 8 characters\").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, \"Password must contain at least one uppercase letter, one lowercase letter, and one number\"),\n    confirmNewPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Please confirm your new password\")\n}).refine((data)=>data.newPassword === data.confirmNewPassword, {\n    message: \"New passwords don't match\",\n    path: [\n        \"confirmNewPassword\"\n    ]\n});\n/**\n * Forgot password validation schema\n */ const forgotPasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Email is required\").email(\"Please enter a valid email address\")\n});\n/**\n * Reset password validation schema\n */ const resetPasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    token: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Reset token is required\"),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(8, \"Password must be at least 8 characters\").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, \"Password must contain at least one uppercase letter, one lowercase letter, and one number\"),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Please confirm your password\")\n}).refine((data)=>data.password === data.confirmPassword, {\n    message: \"Passwords don't match\",\n    path: [\n        \"confirmPassword\"\n    ]\n});\n/**\n * Profile update validation schema\n */ const profileUpdateSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    firstName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"First name is required\").min(2, \"First name must be at least 2 characters\"),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Last name is required\").min(2, \"Last name must be at least 2 characters\"),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Email is required\").email(\"Please enter a valid email address\")\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/auth/validation/authSchemas.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/response.js":
/*!*********************************!*\
  !*** ./src/lib/api/response.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiResponse: () => (/* binding */ ApiResponse),\n/* harmony export */   createdResponse: () => (/* binding */ createdResponse),\n/* harmony export */   errorResponse: () => (/* binding */ errorResponse),\n/* harmony export */   forbiddenResponse: () => (/* binding */ forbiddenResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   noContentResponse: () => (/* binding */ noContentResponse),\n/* harmony export */   notFoundResponse: () => (/* binding */ notFoundResponse),\n/* harmony export */   paginatedResponse: () => (/* binding */ paginatedResponse),\n/* harmony export */   serverErrorResponse: () => (/* binding */ serverErrorResponse),\n/* harmony export */   successResponse: () => (/* binding */ successResponse),\n/* harmony export */   unauthorizedResponse: () => (/* binding */ unauthorizedResponse),\n/* harmony export */   validationErrorResponse: () => (/* binding */ validationErrorResponse),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\n/**\n * Standard API response structure\n */ class ApiResponse {\n    constructor(success = true, data = null, message = null, errors = null, meta = null){\n        this.success = success;\n        this.data = data;\n        this.message = message;\n        this.errors = errors;\n        this.meta = meta;\n        this.timestamp = new Date().toISOString();\n    }\n}\n/**\n * Success response helper\n */ function successResponse(data = null, message = null, meta = null, status = 200) {\n    const response = new ApiResponse(true, data, message, null, meta);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Error response helper\n */ function errorResponse(message, errors = null, status = 400) {\n    const response = new ApiResponse(false, null, message, errors);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Validation error response\n */ function validationErrorResponse(errors, message = \"Validation failed\") {\n    return errorResponse(message, errors, 422);\n}\n/**\n * Unauthorized response\n */ function unauthorizedResponse(message = \"Unauthorized\") {\n    return errorResponse(message, null, 401);\n}\n/**\n * Forbidden response\n */ function forbiddenResponse(message = \"Forbidden\") {\n    return errorResponse(message, null, 403);\n}\n/**\n * Not found response\n */ function notFoundResponse(message = \"Resource not found\") {\n    return errorResponse(message, null, 404);\n}\n/**\n * Internal server error response\n */ function serverErrorResponse(message = \"Internal server error\") {\n    return errorResponse(message, null, 500);\n}\n/**\n * Created response\n */ function createdResponse(data, message = \"Resource created successfully\") {\n    return successResponse(data, message, null, 201);\n}\n/**\n * No content response\n */ function noContentResponse() {\n    return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](null, {\n        status: 204\n    });\n}\n/**\n * Paginated response helper\n */ function paginatedResponse(data, pagination, message = null) {\n    const meta = {\n        pagination: {\n            page: pagination.page,\n            limit: pagination.limit,\n            total: pagination.total,\n            totalPages: Math.ceil(pagination.total / pagination.limit),\n            hasNext: pagination.page < Math.ceil(pagination.total / pagination.limit),\n            hasPrev: pagination.page > 1\n        }\n    };\n    return successResponse(data, message, meta);\n}\n/**\n * Handle API errors consistently\n */ function handleApiError(error) {\n    console.error(\"API Error:\", error);\n    // Prisma errors\n    if (error.code === \"P2002\") {\n        return errorResponse(\"Resource already exists\", null, 409);\n    }\n    if (error.code === \"P2025\") {\n        return notFoundResponse(\"Resource not found\");\n    }\n    // Validation errors (Zod)\n    if (error.name === \"ZodError\") {\n        const validationErrors = error.errors.map((err)=>({\n                field: err.path.join(\".\"),\n                message: err.message,\n                code: err.code\n            }));\n        return validationErrorResponse(validationErrors);\n    }\n    // JWT errors\n    if (error.name === \"JsonWebTokenError\") {\n        return unauthorizedResponse(\"Invalid token\");\n    }\n    if (error.name === \"TokenExpiredError\") {\n        return unauthorizedResponse(\"Token expired\");\n    }\n    // Custom application errors\n    if (error.message === \"Invalid credentials\") {\n        return unauthorizedResponse(\"Invalid email or password\");\n    }\n    if (error.message === \"User not found\") {\n        return notFoundResponse(\"User not found\");\n    }\n    if (error.message === \"Insufficient permissions\") {\n        return forbiddenResponse(\"Insufficient permissions\");\n    }\n    // Default server error\n    return serverErrorResponse();\n}\n/**\n * Async error handler wrapper\n */ function withErrorHandling(handler) {\n    return async (request, context)=>{\n        try {\n            return await handler(request, context);\n        } catch (error) {\n            return handleApiError(error);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/response.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/session.js":
/*!*********************************!*\
  !*** ./src/lib/auth/session.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   clearAuthCookie: () => (/* binding */ clearAuthCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   deleteAllUserSessions: () => (/* binding */ deleteAllUserSessions),\n/* harmony export */   deleteSession: () => (/* binding */ deleteSession),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   setAuthCookie: () => (/* binding */ setAuthCookie),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n/* harmony import */ var _lib_config_env__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/config/env */ \"(rsc)/./src/lib/config/env.js\");\n\n\n\n\nconst JWT_SECRET = _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].JWT_SECRET;\nconst TOKEN_EXPIRY = \"7d\" // 7 days\n;\nconst COOKIE_NAME = \"auth-token\";\n/**\n * Generate JWT token for user\n */ function generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: TOKEN_EXPIRY,\n        issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n        audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n    });\n}\n/**\n * Verify JWT token\n */ function verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET, {\n            issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n            audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n        });\n    } catch (error) {\n        throw new Error(\"Invalid token\");\n    }\n}\n/**\n * Create session for user\n */ async function createSession(userId, userAgent = null, ipAddress = null) {\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now\n    ;\n    // Generate JWT token\n    const tokenPayload = {\n        userId,\n        sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    };\n    const token = generateToken(tokenPayload);\n    // Store session in database\n    const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.create({\n        data: {\n            userId,\n            token,\n            expiresAt,\n            userAgent,\n            ipAddress\n        }\n    });\n    return {\n        session,\n        token\n    };\n}\n/**\n * Get session from token\n */ async function getSession(token) {\n    if (!token) return null;\n    try {\n        // Verify JWT token\n        const payload = verifyToken(token);\n        // Get session from database\n        const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.findUnique({\n            where: {\n                token\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        role: true,\n                        status: true,\n                        avatar: true\n                    }\n                }\n            }\n        });\n        if (!session || session.expiresAt < new Date()) {\n            // Session expired or not found\n            if (session) {\n                await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n                    where: {\n                        id: session.id\n                    }\n                });\n            }\n            return null;\n        }\n        return session;\n    } catch (error) {\n        return null;\n    }\n}\n/**\n * Delete session\n */ async function deleteSession(token) {\n    if (!token) return;\n    try {\n        await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n            where: {\n                token\n            }\n        });\n    } catch (error) {\n    // Session might not exist, ignore error\n    }\n}\n/**\n * Delete all sessions for user\n */ async function deleteAllUserSessions(userId) {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            userId\n        }\n    });\n}\n/**\n * Clean up expired sessions\n */ async function cleanupExpiredSessions() {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            expiresAt: {\n                lt: new Date()\n            }\n        }\n    });\n}\n/**\n * Set auth cookie\n */ function setAuthCookie(token) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.set(COOKIE_NAME, token, {\n        httpOnly: true,\n        secure: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].NODE_ENV === \"production\",\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\n/**\n * Clear auth cookie\n */ function clearAuthCookie() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.delete(COOKIE_NAME);\n}\n/**\n * Get auth token from cookies\n */ function getAuthToken() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return cookieStore.get(COOKIE_NAME)?.value;\n}\n/**\n * Get current user from session\n */ async function getCurrentUser() {\n    const token = getAuthToken();\n    if (!token) return null;\n    const session = await getSession(token);\n    return session?.user || null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/session.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/config/env.js":
/*!*******************************!*\
  !*** ./src/lib/config/env.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   env: () => (/* binding */ env)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\nconst envSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"development\",\n        \"production\",\n        \"test\"\n    ]).default(\"development\"),\n    DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Database URL is required\"),\n    NEXTAUTH_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"NextAuth secret is required\"),\n    NEXTAUTH_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    JWT_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"JWT secret is required\"),\n    APP_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().default(\"http://localhost:3000\"),\n    APP_NAME: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default(\"Coveyor POC\"),\n    // Email configuration (optional)\n    SMTP_HOST: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_USER: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PASS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    FROM_EMAIL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email().optional()\n});\nfunction validateEnv() {\n    try {\n        return envSchema.parse(process.env);\n    } catch (error) {\n        console.error(\"❌ Invalid environment variables:\");\n        console.error(error.errors);\n        throw new Error(\"Environment validation failed\");\n    }\n}\nconst env = validateEnv();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (env);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2NvbmZpZy9lbnYuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVCO0FBRXZCLE1BQU1DLFlBQVlELHlDQUFRLENBQUM7SUFDekJHLFVBQVVILDBDQUFNLENBQUM7UUFBQztRQUFlO1FBQWM7S0FBTyxFQUFFSyxPQUFPLENBQUM7SUFDaEVDLGNBQWNOLHlDQUFRLEdBQUdRLEdBQUcsQ0FBQyxHQUFHO0lBQ2hDQyxpQkFBaUJULHlDQUFRLEdBQUdRLEdBQUcsQ0FBQyxHQUFHO0lBQ25DRSxjQUFjVix5Q0FBUSxHQUFHVyxHQUFHLEdBQUdDLFFBQVE7SUFDdkNDLFlBQVliLHlDQUFRLEdBQUdRLEdBQUcsQ0FBQyxHQUFHO0lBQzlCTSxTQUFTZCx5Q0FBUSxHQUFHVyxHQUFHLEdBQUdOLE9BQU8sQ0FBQztJQUNsQ1UsVUFBVWYseUNBQVEsR0FBR0ssT0FBTyxDQUFDO0lBRTdCLGlDQUFpQztJQUNqQ1csV0FBV2hCLHlDQUFRLEdBQUdZLFFBQVE7SUFDOUJLLFdBQVdqQix5Q0FBUSxHQUFHWSxRQUFRO0lBQzlCTSxXQUFXbEIseUNBQVEsR0FBR1ksUUFBUTtJQUM5Qk8sV0FBV25CLHlDQUFRLEdBQUdZLFFBQVE7SUFDOUJRLFlBQVlwQix5Q0FBUSxHQUFHcUIsS0FBSyxHQUFHVCxRQUFRO0FBQ3pDO0FBRUEsU0FBU1U7SUFDUCxJQUFJO1FBQ0YsT0FBT3JCLFVBQVVzQixLQUFLLENBQUNDLFFBQVFDLEdBQUc7SUFDcEMsRUFBRSxPQUFPQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQztRQUNkQyxRQUFRRCxLQUFLLENBQUNBLE1BQU1FLE1BQU07UUFDMUIsTUFBTSxJQUFJQyxNQUFNO0lBQ2xCO0FBQ0Y7QUFFTyxNQUFNSixNQUFNSCxjQUFhO0FBRWhDLGlFQUFlRyxHQUFHQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY292ZXlvci1wb2MvLi9zcmMvbGliL2NvbmZpZy9lbnYuanM/ZmYxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB6IH0gZnJvbSAnem9kJ1xuXG5jb25zdCBlbnZTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIE5PREVfRU5WOiB6LmVudW0oWydkZXZlbG9wbWVudCcsICdwcm9kdWN0aW9uJywgJ3Rlc3QnXSkuZGVmYXVsdCgnZGV2ZWxvcG1lbnQnKSxcbiAgREFUQUJBU0VfVVJMOiB6LnN0cmluZygpLm1pbigxLCAnRGF0YWJhc2UgVVJMIGlzIHJlcXVpcmVkJyksXG4gIE5FWFRBVVRIX1NFQ1JFVDogei5zdHJpbmcoKS5taW4oMSwgJ05leHRBdXRoIHNlY3JldCBpcyByZXF1aXJlZCcpLFxuICBORVhUQVVUSF9VUkw6IHouc3RyaW5nKCkudXJsKCkub3B0aW9uYWwoKSxcbiAgSldUX1NFQ1JFVDogei5zdHJpbmcoKS5taW4oMSwgJ0pXVCBzZWNyZXQgaXMgcmVxdWlyZWQnKSxcbiAgQVBQX1VSTDogei5zdHJpbmcoKS51cmwoKS5kZWZhdWx0KCdodHRwOi8vbG9jYWxob3N0OjMwMDAnKSxcbiAgQVBQX05BTUU6IHouc3RyaW5nKCkuZGVmYXVsdCgnQ292ZXlvciBQT0MnKSxcbiAgXG4gIC8vIEVtYWlsIGNvbmZpZ3VyYXRpb24gKG9wdGlvbmFsKVxuICBTTVRQX0hPU1Q6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgU01UUF9QT1JUOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIFNNVFBfVVNFUjogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBTTVRQX1BBU1M6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgRlJPTV9FTUFJTDogei5zdHJpbmcoKS5lbWFpbCgpLm9wdGlvbmFsKCksXG59KVxuXG5mdW5jdGlvbiB2YWxpZGF0ZUVudigpIHtcbiAgdHJ5IHtcbiAgICByZXR1cm4gZW52U2NoZW1hLnBhcnNlKHByb2Nlc3MuZW52KVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBJbnZhbGlkIGVudmlyb25tZW50IHZhcmlhYmxlczonKVxuICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IuZXJyb3JzKVxuICAgIHRocm93IG5ldyBFcnJvcignRW52aXJvbm1lbnQgdmFsaWRhdGlvbiBmYWlsZWQnKVxuICB9XG59XG5cbmV4cG9ydCBjb25zdCBlbnYgPSB2YWxpZGF0ZUVudigpXG5cbmV4cG9ydCBkZWZhdWx0IGVudlxuIl0sIm5hbWVzIjpbInoiLCJlbnZTY2hlbWEiLCJvYmplY3QiLCJOT0RFX0VOViIsImVudW0iLCJkZWZhdWx0IiwiREFUQUJBU0VfVVJMIiwic3RyaW5nIiwibWluIiwiTkVYVEFVVEhfU0VDUkVUIiwiTkVYVEFVVEhfVVJMIiwidXJsIiwib3B0aW9uYWwiLCJKV1RfU0VDUkVUIiwiQVBQX1VSTCIsIkFQUF9OQU1FIiwiU01UUF9IT1NUIiwiU01UUF9QT1JUIiwiU01UUF9VU0VSIiwiU01UUF9QQVNTIiwiRlJPTV9FTUFJTCIsImVtYWlsIiwidmFsaWRhdGVFbnYiLCJwYXJzZSIsInByb2Nlc3MiLCJlbnYiLCJlcnJvciIsImNvbnNvbGUiLCJlcnJvcnMiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config/env.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/prisma.js":
/*!******************************!*\
  !*** ./src/lib/db/prisma.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3ByaXNtYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUU7QUFFbEUsSUFBSUksSUFBeUIsRUFBYztJQUN6Q0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBQzNCO0FBRUEsaUVBQWVBLE1BQU1BLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3ZleW9yLXBvYy8uL3NyYy9saWIvZGIvcHJpc21hLmpzP2QyZTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXNcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbn1cblxuZXhwb3J0IGRlZmF1bHQgcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/prisma.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/encryption.js":
/*!*************************************!*\
  !*** ./src/lib/utils/encryption.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateInvitationToken: () => (/* binding */ generateInvitationToken),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   generateSecureToken: () => (/* binding */ generateSecureToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Hash password using bcrypt\n */ async function hashPassword(password) {\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, saltRounds);\n}\n/**\n * Verify password against hash\n */ async function verifyPassword(password, hash) {\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n}\n/**\n * Generate random string\n */ function generateRandomString(length = 32) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * Generate secure token\n */ function generateSecureToken() {\n    return generateRandomString(64);\n}\n/**\n * Generate invitation token\n */ function generateInvitationToken() {\n    return generateRandomString(48);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/encryption.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/semver","vendor-chunks/next","vendor-chunks/jsonwebtoken","vendor-chunks/zod","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/safe-buffer","vendor-chunks/ms","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Flogin%2Froute&page=%2Fapi%2Fauth%2Flogin%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Flogin%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();