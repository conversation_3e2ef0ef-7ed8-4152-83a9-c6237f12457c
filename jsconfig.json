{"compilerOptions": {"paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/features/*": ["./src/features/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/context/*": ["./src/context/*"], "@/styles/*": ["./src/styles/*"]}, "baseUrl": ".", "target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.js", "**/*.jsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}