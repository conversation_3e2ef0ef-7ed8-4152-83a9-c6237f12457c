"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/users/stats/route";
exports.ids = ["app/api/users/stats/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2Fstats%2Froute&page=%2Fapi%2Fusers%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Fstats%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2Fstats%2Froute&page=%2Fapi%2Fusers%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Fstats%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_MobioProjects_coveyor_poc_src_app_api_users_stats_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/users/stats/route.js */ \"(rsc)/./src/app/api/users/stats/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/users/stats/route\",\n        pathname: \"/api/users/stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/stats/route\"\n    },\n    resolvedPagePath: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\api\\\\users\\\\stats\\\\route.js\",\n    nextConfigOutput,\n    userland: D_MobioProjects_coveyor_poc_src_app_api_users_stats_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/users/stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2Fstats%2Froute&page=%2Fapi%2Fusers%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Fstats%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/users/stats/route.js":
/*!******************************************!*\
  !*** ./src/app/api/users/stats/route.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_session__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth/session */ \"(rsc)/./src/lib/auth/session.js\");\n/* harmony import */ var _features_users_services_userService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/users/services/userService */ \"(rsc)/./src/features/users/services/userService.js\");\n/* harmony import */ var _lib_api_response__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/response */ \"(rsc)/./src/lib/api/response.js\");\n/* harmony import */ var _lib_auth_permissions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth/permissions */ \"(rsc)/./src/lib/auth/permissions.js\");\n\n\n\n\nasync function GET(request) {\n    try {\n        const user = await (0,_lib_auth_session__WEBPACK_IMPORTED_MODULE_0__.getCurrentUser)();\n        if (!user) {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.unauthorizedResponse)(\"Authentication required\");\n        }\n        // Check permission - only managers and admins can view stats\n        if (!(0,_lib_auth_permissions__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(user.role, _lib_auth_permissions__WEBPACK_IMPORTED_MODULE_3__.PERMISSIONS.USER_READ)) {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.forbiddenResponse)(\"Insufficient permissions to view user statistics\");\n        }\n        // Get user statistics\n        const stats = await (0,_features_users_services_userService__WEBPACK_IMPORTED_MODULE_1__.getUserStats)();\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.successResponse)(stats, \"User statistics retrieved successfully\");\n    } catch (error) {\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.handleApiError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS91c2Vycy9zdGF0cy9yb3V0ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFtRDtBQUNpQjtBQUN5QztBQUMxQztBQUU1RCxlQUFlUSxJQUFJQyxPQUFPO0lBQy9CLElBQUk7UUFDRixNQUFNQyxPQUFPLE1BQU1WLGlFQUFjQTtRQUVqQyxJQUFJLENBQUNVLE1BQU07WUFDVCxPQUFPUCx1RUFBb0JBLENBQUM7UUFDOUI7UUFFQSw2REFBNkQ7UUFDN0QsSUFBSSxDQUFDRyxvRUFBYUEsQ0FBQ0ksS0FBS0MsSUFBSSxFQUFFSiw4REFBV0EsQ0FBQ0ssU0FBUyxHQUFHO1lBQ3BELE9BQU9SLG9FQUFpQkEsQ0FBQztRQUMzQjtRQUVBLHNCQUFzQjtRQUN0QixNQUFNUyxRQUFRLE1BQU1aLGtGQUFZQTtRQUVoQyxPQUFPQyxrRUFBZUEsQ0FBQ1csT0FBTztJQUVoQyxFQUFFLE9BQU9DLE9BQU87UUFDZCxPQUFPVCxpRUFBY0EsQ0FBQ1M7SUFDeEI7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvdmV5b3ItcG9jLy4vc3JjL2FwcC9hcGkvdXNlcnMvc3RhdHMvcm91dGUuanM/YzI0NyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBnZXRDdXJyZW50VXNlciB9IGZyb20gJ0AvbGliL2F1dGgvc2Vzc2lvbidcbmltcG9ydCB7IGdldFVzZXJTdGF0cyB9IGZyb20gJ0AvZmVhdHVyZXMvdXNlcnMvc2VydmljZXMvdXNlclNlcnZpY2UnXG5pbXBvcnQgeyBzdWNjZXNzUmVzcG9uc2UsIHVuYXV0aG9yaXplZFJlc3BvbnNlLCBmb3JiaWRkZW5SZXNwb25zZSwgaGFuZGxlQXBpRXJyb3IgfSBmcm9tICdAL2xpYi9hcGkvcmVzcG9uc2UnXG5pbXBvcnQgeyBoYXNQZXJtaXNzaW9uLCBQRVJNSVNTSU9OUyB9IGZyb20gJ0AvbGliL2F1dGgvcGVybWlzc2lvbnMnXG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHVzZXIgPSBhd2FpdCBnZXRDdXJyZW50VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICByZXR1cm4gdW5hdXRob3JpemVkUmVzcG9uc2UoJ0F1dGhlbnRpY2F0aW9uIHJlcXVpcmVkJylcbiAgICB9XG5cbiAgICAvLyBDaGVjayBwZXJtaXNzaW9uIC0gb25seSBtYW5hZ2VycyBhbmQgYWRtaW5zIGNhbiB2aWV3IHN0YXRzXG4gICAgaWYgKCFoYXNQZXJtaXNzaW9uKHVzZXIucm9sZSwgUEVSTUlTU0lPTlMuVVNFUl9SRUFEKSkge1xuICAgICAgcmV0dXJuIGZvcmJpZGRlblJlc3BvbnNlKCdJbnN1ZmZpY2llbnQgcGVybWlzc2lvbnMgdG8gdmlldyB1c2VyIHN0YXRpc3RpY3MnKVxuICAgIH1cblxuICAgIC8vIEdldCB1c2VyIHN0YXRpc3RpY3NcbiAgICBjb25zdCBzdGF0cyA9IGF3YWl0IGdldFVzZXJTdGF0cygpXG4gICAgXG4gICAgcmV0dXJuIHN1Y2Nlc3NSZXNwb25zZShzdGF0cywgJ1VzZXIgc3RhdGlzdGljcyByZXRyaWV2ZWQgc3VjY2Vzc2Z1bGx5JylcbiAgICBcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICByZXR1cm4gaGFuZGxlQXBpRXJyb3IoZXJyb3IpXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJnZXRDdXJyZW50VXNlciIsImdldFVzZXJTdGF0cyIsInN1Y2Nlc3NSZXNwb25zZSIsInVuYXV0aG9yaXplZFJlc3BvbnNlIiwiZm9yYmlkZGVuUmVzcG9uc2UiLCJoYW5kbGVBcGlFcnJvciIsImhhc1Blcm1pc3Npb24iLCJQRVJNSVNTSU9OUyIsIkdFVCIsInJlcXVlc3QiLCJ1c2VyIiwicm9sZSIsIlVTRVJfUkVBRCIsInN0YXRzIiwiZXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/users/stats/route.js\n");

/***/ }),

/***/ "(rsc)/./src/features/users/services/userService.js":
/*!****************************************************!*\
  !*** ./src/features/users/services/userService.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activateUser: () => (/* binding */ activateUser),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   deactivateUser: () => (/* binding */ deactivateUser),\n/* harmony export */   deleteUser: () => (/* binding */ deleteUser),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   getUserStats: () => (/* binding */ getUserStats),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   updateUser: () => (/* binding */ updateUser)\n/* harmony export */ });\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n/* harmony import */ var _lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/encryption */ \"(rsc)/./src/lib/utils/encryption.js\");\n/* harmony import */ var _lib_auth_permissions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth/permissions */ \"(rsc)/./src/lib/auth/permissions.js\");\n\n\n\n/**\n * Get paginated users list with filters\n */ async function getUsers(filters = {}) {\n    const { search = \"\", role, status, page = 1, limit = 10, sortBy = \"createdAt\", sortOrder = \"desc\" } = filters;\n    const skip = (page - 1) * limit;\n    // Build where clause\n    const where = {\n        AND: [\n            // Search filter\n            search ? {\n                OR: [\n                    {\n                        firstName: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        lastName: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        email: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                ]\n            } : {},\n            // Role filter\n            role ? {\n                role\n            } : {},\n            // Status filter\n            status ? {\n                status\n            } : {}\n        ]\n    };\n    // Get users with pagination\n    const [users, total] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.findMany({\n            where,\n            select: {\n                id: true,\n                email: true,\n                firstName: true,\n                lastName: true,\n                role: true,\n                status: true,\n                avatar: true,\n                createdAt: true,\n                updatedAt: true,\n                lastLoginAt: true,\n                inviter: {\n                    select: {\n                        firstName: true,\n                        lastName: true,\n                        email: true\n                    }\n                }\n            },\n            orderBy: {\n                [sortBy]: sortOrder\n            },\n            skip,\n            take: limit\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.count({\n            where\n        })\n    ]);\n    return {\n        users,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit)\n        }\n    };\n}\n/**\n * Get user by ID\n */ async function getUserById(id) {\n    const user = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.findUnique({\n        where: {\n            id\n        },\n        select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n            role: true,\n            status: true,\n            avatar: true,\n            createdAt: true,\n            updatedAt: true,\n            lastLoginAt: true,\n            inviter: {\n                select: {\n                    id: true,\n                    firstName: true,\n                    lastName: true,\n                    email: true\n                }\n            },\n            invitees: {\n                select: {\n                    id: true,\n                    firstName: true,\n                    lastName: true,\n                    email: true,\n                    role: true,\n                    status: true,\n                    createdAt: true\n                }\n            }\n        }\n    });\n    if (!user) {\n        throw new Error(\"User not found\");\n    }\n    return user;\n}\n/**\n * Create new user\n */ async function createUser(userData, createdBy) {\n    const { email, firstName, lastName, role, password } = userData;\n    // Check if user already exists\n    const existingUser = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.findUnique({\n        where: {\n            email: email.toLowerCase()\n        }\n    });\n    if (existingUser) {\n        throw new Error(\"User with this email already exists\");\n    }\n    // Hash password\n    const hashedPassword = await (0,_lib_utils_encryption__WEBPACK_IMPORTED_MODULE_1__.hashPassword)(password);\n    // Create user\n    const user = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.create({\n        data: {\n            email: email.toLowerCase(),\n            firstName,\n            lastName,\n            role,\n            password: hashedPassword,\n            status: \"ACTIVE\",\n            invitedBy: createdBy\n        },\n        select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n            role: true,\n            status: true,\n            avatar: true,\n            createdAt: true\n        }\n    });\n    // Log user creation\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auditLog.create({\n        data: {\n            userId: createdBy,\n            action: \"CREATE\",\n            resource: \"USER\",\n            resourceId: user.id,\n            details: {\n                email: user.email,\n                role: user.role,\n                firstName: user.firstName,\n                lastName: user.lastName\n            }\n        }\n    });\n    return user;\n}\n/**\n * Update user\n */ async function updateUser(id, updateData, updatedBy) {\n    const { email, firstName, lastName, role, status } = updateData;\n    // Get current user\n    const currentUser = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.findUnique({\n        where: {\n            id\n        },\n        select: {\n            id: true,\n            email: true,\n            role: true,\n            status: true\n        }\n    });\n    if (!currentUser) {\n        throw new Error(\"User not found\");\n    }\n    // Check if email is already taken by another user\n    if (email && email !== currentUser.email) {\n        const existingUser = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.findFirst({\n            where: {\n                email: email.toLowerCase(),\n                NOT: {\n                    id\n                }\n            }\n        });\n        if (existingUser) {\n            throw new Error(\"Email is already taken by another user\");\n        }\n    }\n    // Update user\n    const updatedUser = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.update({\n        where: {\n            id\n        },\n        data: {\n            ...email && {\n                email: email.toLowerCase()\n            },\n            ...firstName && {\n                firstName\n            },\n            ...lastName && {\n                lastName\n            },\n            ...role && {\n                role\n            },\n            ...status && {\n                status\n            }\n        },\n        select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n            role: true,\n            status: true,\n            avatar: true,\n            createdAt: true,\n            updatedAt: true\n        }\n    });\n    // Log user update\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auditLog.create({\n        data: {\n            userId: updatedBy,\n            action: \"UPDATE\",\n            resource: \"USER\",\n            resourceId: id,\n            details: {\n                changes: updateData,\n                previousRole: currentUser.role,\n                previousStatus: currentUser.status\n            }\n        }\n    });\n    return updatedUser;\n}\n/**\n * Delete user\n */ async function deleteUser(id, deletedBy, managerRole) {\n    // Get user to delete\n    const userToDelete = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.findUnique({\n        where: {\n            id\n        },\n        select: {\n            id: true,\n            email: true,\n            role: true,\n            firstName: true,\n            lastName: true\n        }\n    });\n    if (!userToDelete) {\n        throw new Error(\"User not found\");\n    }\n    // Check if manager can delete this user\n    if (!(0,_lib_auth_permissions__WEBPACK_IMPORTED_MODULE_2__.canManageUser)(managerRole, userToDelete.role)) {\n        throw new Error(\"Insufficient permissions to delete this user\");\n    }\n    // Delete user (this will cascade delete sessions due to schema)\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.delete({\n        where: {\n            id\n        }\n    });\n    // Log user deletion\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auditLog.create({\n        data: {\n            userId: deletedBy,\n            action: \"DELETE\",\n            resource: \"USER\",\n            resourceId: id,\n            details: {\n                email: userToDelete.email,\n                role: userToDelete.role,\n                firstName: userToDelete.firstName,\n                lastName: userToDelete.lastName\n            }\n        }\n    });\n    return {\n        success: true\n    };\n}\n/**\n * Activate user\n */ async function activateUser(id, activatedBy) {\n    const user = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.update({\n        where: {\n            id\n        },\n        data: {\n            status: \"ACTIVE\"\n        },\n        select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n            role: true,\n            status: true\n        }\n    });\n    // Log user activation\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auditLog.create({\n        data: {\n            userId: activatedBy,\n            action: \"ACTIVATE\",\n            resource: \"USER\",\n            resourceId: id,\n            details: {\n                status: \"ACTIVE\"\n            }\n        }\n    });\n    return user;\n}\n/**\n * Deactivate user\n */ async function deactivateUser(id, deactivatedBy) {\n    const user = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.update({\n        where: {\n            id\n        },\n        data: {\n            status: \"INACTIVE\"\n        },\n        select: {\n            id: true,\n            email: true,\n            firstName: true,\n            lastName: true,\n            role: true,\n            status: true\n        }\n    });\n    // Delete all user sessions when deactivating\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].session.deleteMany({\n        where: {\n            userId: id\n        }\n    });\n    // Log user deactivation\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].auditLog.create({\n        data: {\n            userId: deactivatedBy,\n            action: \"DEACTIVATE\",\n            resource: \"USER\",\n            resourceId: id,\n            details: {\n                status: \"INACTIVE\"\n            }\n        }\n    });\n    return user;\n}\n/**\n * Get user statistics\n */ async function getUserStats() {\n    const [totalUsers, activeUsers, inactiveUsers, pendingUsers, adminUsers, managerUsers, regularUsers, recentUsers] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.count(),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.count({\n            where: {\n                status: \"ACTIVE\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.count({\n            where: {\n                status: \"INACTIVE\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.count({\n            where: {\n                status: \"PENDING\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.count({\n            where: {\n                role: \"ADMIN\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.count({\n            where: {\n                role: \"MANAGER\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.count({\n            where: {\n                role: \"USER\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].user.count({\n            where: {\n                createdAt: {\n                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                }\n            }\n        })\n    ]);\n    return {\n        total: totalUsers,\n        byStatus: {\n            active: activeUsers,\n            inactive: inactiveUsers,\n            pending: pendingUsers\n        },\n        byRole: {\n            admin: adminUsers,\n            manager: managerUsers,\n            user: regularUsers\n        },\n        recent: recentUsers\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/users/services/userService.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/response.js":
/*!*********************************!*\
  !*** ./src/lib/api/response.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiResponse: () => (/* binding */ ApiResponse),\n/* harmony export */   createdResponse: () => (/* binding */ createdResponse),\n/* harmony export */   errorResponse: () => (/* binding */ errorResponse),\n/* harmony export */   forbiddenResponse: () => (/* binding */ forbiddenResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   noContentResponse: () => (/* binding */ noContentResponse),\n/* harmony export */   notFoundResponse: () => (/* binding */ notFoundResponse),\n/* harmony export */   paginatedResponse: () => (/* binding */ paginatedResponse),\n/* harmony export */   serverErrorResponse: () => (/* binding */ serverErrorResponse),\n/* harmony export */   successResponse: () => (/* binding */ successResponse),\n/* harmony export */   unauthorizedResponse: () => (/* binding */ unauthorizedResponse),\n/* harmony export */   validationErrorResponse: () => (/* binding */ validationErrorResponse),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\n/**\n * Standard API response structure\n */ class ApiResponse {\n    constructor(success = true, data = null, message = null, errors = null, meta = null){\n        this.success = success;\n        this.data = data;\n        this.message = message;\n        this.errors = errors;\n        this.meta = meta;\n        this.timestamp = new Date().toISOString();\n    }\n}\n/**\n * Success response helper\n */ function successResponse(data = null, message = null, meta = null, status = 200) {\n    const response = new ApiResponse(true, data, message, null, meta);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Error response helper\n */ function errorResponse(message, errors = null, status = 400) {\n    const response = new ApiResponse(false, null, message, errors);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Validation error response\n */ function validationErrorResponse(errors, message = \"Validation failed\") {\n    return errorResponse(message, errors, 422);\n}\n/**\n * Unauthorized response\n */ function unauthorizedResponse(message = \"Unauthorized\") {\n    return errorResponse(message, null, 401);\n}\n/**\n * Forbidden response\n */ function forbiddenResponse(message = \"Forbidden\") {\n    return errorResponse(message, null, 403);\n}\n/**\n * Not found response\n */ function notFoundResponse(message = \"Resource not found\") {\n    return errorResponse(message, null, 404);\n}\n/**\n * Internal server error response\n */ function serverErrorResponse(message = \"Internal server error\") {\n    return errorResponse(message, null, 500);\n}\n/**\n * Created response\n */ function createdResponse(data, message = \"Resource created successfully\") {\n    return successResponse(data, message, null, 201);\n}\n/**\n * No content response\n */ function noContentResponse() {\n    return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](null, {\n        status: 204\n    });\n}\n/**\n * Paginated response helper\n */ function paginatedResponse(data, pagination, message = null) {\n    const meta = {\n        pagination: {\n            page: pagination.page,\n            limit: pagination.limit,\n            total: pagination.total,\n            totalPages: Math.ceil(pagination.total / pagination.limit),\n            hasNext: pagination.page < Math.ceil(pagination.total / pagination.limit),\n            hasPrev: pagination.page > 1\n        }\n    };\n    return successResponse(data, message, meta);\n}\n/**\n * Handle API errors consistently\n */ function handleApiError(error) {\n    console.error(\"API Error:\", error);\n    // Prisma errors\n    if (error.code === \"P2002\") {\n        return errorResponse(\"Resource already exists\", null, 409);\n    }\n    if (error.code === \"P2025\") {\n        return notFoundResponse(\"Resource not found\");\n    }\n    // Validation errors (Zod)\n    if (error.name === \"ZodError\") {\n        const validationErrors = error.errors.map((err)=>({\n                field: err.path.join(\".\"),\n                message: err.message,\n                code: err.code\n            }));\n        return validationErrorResponse(validationErrors);\n    }\n    // JWT errors\n    if (error.name === \"JsonWebTokenError\") {\n        return unauthorizedResponse(\"Invalid token\");\n    }\n    if (error.name === \"TokenExpiredError\") {\n        return unauthorizedResponse(\"Token expired\");\n    }\n    // Custom application errors\n    if (error.message === \"Invalid credentials\") {\n        return unauthorizedResponse(\"Invalid email or password\");\n    }\n    if (error.message === \"User not found\") {\n        return notFoundResponse(\"User not found\");\n    }\n    if (error.message === \"Insufficient permissions\") {\n        return forbiddenResponse(\"Insufficient permissions\");\n    }\n    // Default server error\n    return serverErrorResponse();\n}\n/**\n * Async error handler wrapper\n */ function withErrorHandling(handler) {\n    return async (request, context)=>{\n        try {\n            return await handler(request, context);\n        } catch (error) {\n            return handleApiError(error);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/response.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/permissions.js":
/*!*************************************!*\
  !*** ./src/lib/auth/permissions.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES),\n/* harmony export */   canAccessResource: () => (/* binding */ canAccessResource),\n/* harmony export */   canManageUser: () => (/* binding */ canManageUser),\n/* harmony export */   filterByPermissions: () => (/* binding */ filterByPermissions),\n/* harmony export */   getRolePermissions: () => (/* binding */ getRolePermissions),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRoleLevel: () => (/* binding */ hasRoleLevel),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole)\n/* harmony export */ });\n/**\n * User roles hierarchy\n */ const USER_ROLES = {\n    ADMIN: \"ADMIN\",\n    MANAGER: \"MANAGER\",\n    USER: \"USER\"\n};\n/**\n * Role hierarchy levels (higher number = more permissions)\n */ const ROLE_LEVELS = {\n    [USER_ROLES.USER]: 1,\n    [USER_ROLES.MANAGER]: 2,\n    [USER_ROLES.ADMIN]: 3\n};\n/**\n * Permissions for different actions\n */ const PERMISSIONS = {\n    // User management\n    USER_CREATE: \"user:create\",\n    USER_READ: \"user:read\",\n    USER_UPDATE: \"user:update\",\n    USER_DELETE: \"user:delete\",\n    USER_INVITE: \"user:invite\",\n    USER_ACTIVATE: \"user:activate\",\n    USER_DEACTIVATE: \"user:deactivate\",\n    // Role management\n    ROLE_ASSIGN: \"role:assign\",\n    ROLE_VIEW: \"role:view\",\n    // System\n    AUDIT_VIEW: \"audit:view\",\n    SYSTEM_CONFIG: \"system:config\"\n};\n/**\n * Role-based permissions mapping\n */ const ROLE_PERMISSIONS = {\n    [USER_ROLES.ADMIN]: [\n        PERMISSIONS.USER_CREATE,\n        PERMISSIONS.USER_READ,\n        PERMISSIONS.USER_UPDATE,\n        PERMISSIONS.USER_DELETE,\n        PERMISSIONS.USER_INVITE,\n        PERMISSIONS.USER_ACTIVATE,\n        PERMISSIONS.USER_DEACTIVATE,\n        PERMISSIONS.ROLE_ASSIGN,\n        PERMISSIONS.ROLE_VIEW,\n        PERMISSIONS.AUDIT_VIEW,\n        PERMISSIONS.SYSTEM_CONFIG\n    ],\n    [USER_ROLES.MANAGER]: [\n        PERMISSIONS.USER_CREATE,\n        PERMISSIONS.USER_READ,\n        PERMISSIONS.USER_UPDATE,\n        PERMISSIONS.USER_INVITE,\n        PERMISSIONS.USER_ACTIVATE,\n        PERMISSIONS.USER_DEACTIVATE,\n        PERMISSIONS.ROLE_VIEW,\n        PERMISSIONS.AUDIT_VIEW\n    ],\n    [USER_ROLES.USER]: [\n        PERMISSIONS.USER_READ\n    ]\n};\n/**\n * Check if user has specific permission\n */ function hasPermission(userRole, permission) {\n    if (!userRole || !permission) return false;\n    const rolePermissions = ROLE_PERMISSIONS[userRole] || [];\n    return rolePermissions.includes(permission);\n}\n/**\n * Check if user has any of the specified permissions\n */ function hasAnyPermission(userRole, permissions) {\n    if (!userRole || !permissions || !Array.isArray(permissions)) return false;\n    return permissions.some((permission)=>hasPermission(userRole, permission));\n}\n/**\n * Check if user has all of the specified permissions\n */ function hasAllPermissions(userRole, permissions) {\n    if (!userRole || !permissions || !Array.isArray(permissions)) return false;\n    return permissions.every((permission)=>hasPermission(userRole, permission));\n}\n/**\n * Check if user role is higher than or equal to required role\n */ function hasRoleLevel(userRole, requiredRole) {\n    if (!userRole || !requiredRole) return false;\n    const userLevel = ROLE_LEVELS[userRole] || 0;\n    const requiredLevel = ROLE_LEVELS[requiredRole] || 0;\n    return userLevel >= requiredLevel;\n}\n/**\n * Check if user can manage another user (based on role hierarchy)\n */ function canManageUser(managerRole, targetRole) {\n    if (!managerRole || !targetRole) return false;\n    const managerLevel = ROLE_LEVELS[managerRole] || 0;\n    const targetLevel = ROLE_LEVELS[targetRole] || 0;\n    // Can manage users with lower or equal role level\n    return managerLevel >= targetLevel;\n}\n/**\n * Get all permissions for a role\n */ function getRolePermissions(role) {\n    return ROLE_PERMISSIONS[role] || [];\n}\n/**\n * Check if user can access resource\n */ function canAccessResource(userRole, resource, action = \"read\") {\n    const permission = `${resource}:${action}`;\n    return hasPermission(userRole, permission);\n}\n/**\n * Filter data based on user permissions\n */ function filterByPermissions(data, userRole, filterFn) {\n    if (!Array.isArray(data)) return data;\n    return data.filter((item)=>filterFn(item, userRole));\n}\n/**\n * Permission middleware helper\n */ function requirePermission(permission) {\n    return (userRole)=>{\n        if (!hasPermission(userRole, permission)) {\n            throw new Error(`Insufficient permissions. Required: ${permission}`);\n        }\n        return true;\n    };\n}\n/**\n * Role middleware helper\n */ function requireRole(requiredRole) {\n    return (userRole)=>{\n        if (!hasRoleLevel(userRole, requiredRole)) {\n            throw new Error(`Insufficient role level. Required: ${requiredRole}`);\n        }\n        return true;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/permissions.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/session.js":
/*!*********************************!*\
  !*** ./src/lib/auth/session.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   clearAuthCookie: () => (/* binding */ clearAuthCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   deleteAllUserSessions: () => (/* binding */ deleteAllUserSessions),\n/* harmony export */   deleteSession: () => (/* binding */ deleteSession),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   setAuthCookie: () => (/* binding */ setAuthCookie),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n/* harmony import */ var _lib_config_env__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/config/env */ \"(rsc)/./src/lib/config/env.js\");\n\n\n\n\nconst JWT_SECRET = _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].JWT_SECRET;\nconst TOKEN_EXPIRY = \"7d\" // 7 days\n;\nconst COOKIE_NAME = \"auth-token\";\n/**\n * Generate JWT token for user\n */ function generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: TOKEN_EXPIRY,\n        issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n        audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n    });\n}\n/**\n * Verify JWT token\n */ function verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET, {\n            issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n            audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n        });\n    } catch (error) {\n        throw new Error(\"Invalid token\");\n    }\n}\n/**\n * Create session for user\n */ async function createSession(userId, userAgent = null, ipAddress = null) {\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now\n    ;\n    // Generate JWT token\n    const tokenPayload = {\n        userId,\n        sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    };\n    const token = generateToken(tokenPayload);\n    // Store session in database\n    const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.create({\n        data: {\n            userId,\n            token,\n            expiresAt,\n            userAgent,\n            ipAddress\n        }\n    });\n    return {\n        session,\n        token\n    };\n}\n/**\n * Get session from token\n */ async function getSession(token) {\n    if (!token) return null;\n    try {\n        // Verify JWT token\n        const payload = verifyToken(token);\n        // Get session from database\n        const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.findUnique({\n            where: {\n                token\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        role: true,\n                        status: true,\n                        avatar: true\n                    }\n                }\n            }\n        });\n        if (!session || session.expiresAt < new Date()) {\n            // Session expired or not found\n            if (session) {\n                await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n                    where: {\n                        id: session.id\n                    }\n                });\n            }\n            return null;\n        }\n        return session;\n    } catch (error) {\n        return null;\n    }\n}\n/**\n * Delete session\n */ async function deleteSession(token) {\n    if (!token) return;\n    try {\n        await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n            where: {\n                token\n            }\n        });\n    } catch (error) {\n    // Session might not exist, ignore error\n    }\n}\n/**\n * Delete all sessions for user\n */ async function deleteAllUserSessions(userId) {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            userId\n        }\n    });\n}\n/**\n * Clean up expired sessions\n */ async function cleanupExpiredSessions() {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            expiresAt: {\n                lt: new Date()\n            }\n        }\n    });\n}\n/**\n * Set auth cookie\n */ function setAuthCookie(token) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.set(COOKIE_NAME, token, {\n        httpOnly: true,\n        secure: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].NODE_ENV === \"production\",\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\n/**\n * Clear auth cookie\n */ function clearAuthCookie() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.delete(COOKIE_NAME);\n}\n/**\n * Get auth token from cookies\n */ function getAuthToken() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return cookieStore.get(COOKIE_NAME)?.value;\n}\n/**\n * Get current user from session\n */ async function getCurrentUser() {\n    const token = getAuthToken();\n    if (!token) return null;\n    const session = await getSession(token);\n    return session?.user || null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/session.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/config/env.js":
/*!*******************************!*\
  !*** ./src/lib/config/env.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   env: () => (/* binding */ env)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\nconst envSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"development\",\n        \"production\",\n        \"test\"\n    ]).default(\"development\"),\n    DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Database URL is required\"),\n    NEXTAUTH_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"NextAuth secret is required\"),\n    NEXTAUTH_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    JWT_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"JWT secret is required\"),\n    APP_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().default(\"http://localhost:3000\"),\n    APP_NAME: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default(\"Coveyor POC\"),\n    // Email configuration (optional)\n    SMTP_HOST: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_USER: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PASS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    FROM_EMAIL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email().optional()\n});\nfunction validateEnv() {\n    try {\n        return envSchema.parse(process.env);\n    } catch (error) {\n        console.error(\"❌ Invalid environment variables:\");\n        console.error(error.errors);\n        throw new Error(\"Environment validation failed\");\n    }\n}\nconst env = validateEnv();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (env);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config/env.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/prisma.js":
/*!******************************!*\
  !*** ./src/lib/db/prisma.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3ByaXNtYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUU7QUFFbEUsSUFBSUksSUFBeUIsRUFBYztJQUN6Q0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBQzNCO0FBRUEsaUVBQWVBLE1BQU1BLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3ZleW9yLXBvYy8uL3NyYy9saWIvZGIvcHJpc21hLmpzP2QyZTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXNcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbn1cblxuZXhwb3J0IGRlZmF1bHQgcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/prisma.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/encryption.js":
/*!*************************************!*\
  !*** ./src/lib/utils/encryption.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateInvitationToken: () => (/* binding */ generateInvitationToken),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   generateSecureToken: () => (/* binding */ generateSecureToken),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Hash password using bcrypt\n */ async function hashPassword(password) {\n    const saltRounds = 12;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, saltRounds);\n}\n/**\n * Verify password against hash\n */ async function verifyPassword(password, hash) {\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n}\n/**\n * Generate random string\n */ function generateRandomString(length = 32) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n/**\n * Generate secure token\n */ function generateSecureToken() {\n    return generateRandomString(64);\n}\n/**\n * Generate invitation token\n */ function generateInvitationToken() {\n    return generateRandomString(48);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/encryption.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fusers%2Fstats%2Froute&page=%2Fapi%2Fusers%2Fstats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Fstats%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();