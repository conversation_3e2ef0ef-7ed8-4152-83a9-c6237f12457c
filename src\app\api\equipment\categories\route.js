import { getCurrentUser } from '@/lib/auth/session'
import { getEquipmentCategories, createEquipmentCategory } from '@/features/equipment/services/equipmentService'
import { equipmentCategoryFilterSchema, createEquipmentCategorySchema } from '@/features/equipment/validation/equipmentSchemas'
import { successResponse, unauthorizedResponse, forbiddenResponse, handleApiError, paginatedResponse } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission - using USER_READ for now, can be customized later
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view equipment categories')
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const filters = equipmentCategoryFilterSchema.parse({
      search: searchParams.get('search') || '',
      status: searchParams.get('status') ? searchParams.get('status') === 'true' : undefined,
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: searchParams.get('sortOrder') || 'desc',
    })

    // Get categories
    const result = await getEquipmentCategories(filters)
    
    return paginatedResponse(
      result.categories,
      result.pagination,
      'Equipment categories retrieved successfully'
    )
    
  } catch (error) {
    return handleApiError(error)
  }
}

export async function POST(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission - using USER_CREATE for now, can be customized later
    if (!hasPermission(user.role, PERMISSIONS.USER_CREATE)) {
      return forbiddenResponse('Insufficient permissions to create equipment categories')
    }

    const body = await request.json()
    
    // Validate request body
    const validatedData = createEquipmentCategorySchema.parse(body)
    
    // Create category
    const newCategory = await createEquipmentCategory(validatedData, user.id)
    
    return successResponse(newCategory, 'Equipment category created successfully', null, 201)
    
  } catch (error) {
    return handleApiError(error)
  }
}
