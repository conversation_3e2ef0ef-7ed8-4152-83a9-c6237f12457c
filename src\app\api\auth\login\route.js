import { NextRequest } from 'next/server'
import { cookies } from 'next/headers'
import { authenticateUser } from '@/features/auth/services/authService'
import { loginSchema } from '@/features/auth/validation/authSchemas'
import { successResponse, handleApiError } from '@/lib/api/response'

export async function POST(request) {
  try {
    const body = await request.json()
    
    // Validate request body
    const validatedData = loginSchema.parse(body)
    
    // Get client info
    const userAgent = request.headers.get('user-agent')
    const forwarded = request.headers.get('x-forwarded-for')
    const ipAddress = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown'
    
    // Authenticate user
    const result = await authenticateUser(
      validatedData.email,
      validatedData.password,
      userAgent,
      ipAddress
    )
    
    // Set auth cookie
    const cookieStore = cookies()
    cookieStore.set('auth-token', result.token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7, // 7 days
      path: '/',
    })
    
    return successResponse({
      user: result.user,
      session: {
        id: result.session.id,
        expiresAt: result.session.expiresAt,
      }
    }, 'Login successful')
    
  } catch (error) {
    return handleApiError(error)
  }
}
