import { getCurrentUser } from '@/lib/auth/session'
import { getUserById, updateUser, deleteUser } from '@/features/users/services/userService'
import { updateUserSchema } from '@/features/users/validation/userSchemas'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, canManageUser, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view user')
    }

    const userId = params.id
    const targetUser = await getUserById(userId)

    return successResponse(targetUser, 'User retrieved successfully')

  } catch (error) {
    if (error.message === 'User not found') {
      return notFoundResponse('User not found')
    }
    return handleApiError(error)
  }
}

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_UPDATE)) {
      return forbiddenResponse('Insufficient permissions to update user')
    }

    const userId = params.id
    const body = await request.json()
    
    // Validate request body
    const validatedData = updateUserSchema.parse(body)
    
    // Get target user to check if current user can manage them
    const targetUser = await getUserById(userId)
    
    if (!canManageUser(user.role, targetUser.role)) {
      return forbiddenResponse('Insufficient permissions to update this user')
    }
    
    // Update user
    const updatedUser = await updateUser(userId, validatedData, user.id)
    
    return successResponse(updatedUser, 'User updated successfully')
    
  } catch (error) {
    if (error.message === 'User not found') {
      return notFoundResponse('User not found')
    }
    return handleApiError(error)
  }
}

export async function DELETE(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_DELETE)) {
      return forbiddenResponse('Insufficient permissions to delete user')
    }

    const userId = params.id
    
    // Prevent self-deletion
    if (userId === user.id) {
      return forbiddenResponse('Cannot delete your own account')
    }
    
    // Delete user
    await deleteUser(userId, user.id, user.role)
    
    return successResponse(null, 'User deleted successfully')
    
  } catch (error) {
    if (error.message === 'User not found') {
      return notFoundResponse('User not found')
    }
    return handleApiError(error)
  }
}
