import { getCurrentUser } from '@/lib/auth/session'
import { getActiveEquipmentCategories } from '@/features/equipment/services/equipmentService'
import { successResponse, unauthorizedResponse, forbiddenResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view equipment categories')
    }

    // Get active categories
    const categories = await getActiveEquipmentCategories()
    
    return successResponse(categories, 'Active equipment categories retrieved successfully')
    
  } catch (error) {
    return handleApiError(error)
  }
}
