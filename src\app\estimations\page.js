'use client'

import { useState } from 'react'
import { useAuth } from '@/features/auth/hooks/useAuth'
import EstimationList from '@/features/estimation/components/EstimationList'
import EstimationForm from '@/features/estimation/components/EstimationForm'
import EstimationView from '@/features/estimation/components/EstimationView'
import PlanSelectionDialog from '@/features/estimation/components/PlanSelectionDialog'

export default function EstimationsPage() {
  const [currentView, setCurrentView] = useState('list') // 'list', 'create', 'edit', 'view', 'selectPlan'
  const [selectedEstimation, setSelectedEstimation] = useState(null)
  const { user } = useAuth()

  const handleCreateNew = () => {
    setSelectedEstimation(null)
    setCurrentView('create')
  }

  const handleEdit = (estimation) => {
    setSelectedEstimation(estimation)
    setCurrentView('edit')
  }

  const handleView = (estimation) => {
    setSelectedEstimation(estimation)
    setCurrentView('view')
  }

  const handleGenerateFromPlan = () => {
    setCurrentView('selectPlan')
  }

  const handlePlanSelected = async (planId) => {
    // Generate estimation from plan
    try {
      const response = await fetch(`/api/estimations/generate/${planId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (response.ok) {
        const result = await response.json()
        setSelectedEstimation(result.data)
        setCurrentView('view')
      } else {
        alert('Failed to generate estimation')
      }
    } catch (error) {
      console.error('Error generating estimation:', error)
      alert('Failed to generate estimation')
    }
  }

  const handleSave = (estimation) => {
    setCurrentView('list')
    setSelectedEstimation(null)
  }

  const handleCancel = () => {
    setCurrentView('list')
    setSelectedEstimation(null)
  }

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {currentView === 'list' && (
        <EstimationList
          onCreateNew={handleCreateNew}
          onEdit={handleEdit}
          onView={handleView}
          onGenerateFromPlan={handleGenerateFromPlan}
        />
      )}

      {(currentView === 'create' || currentView === 'edit') && (
        <EstimationForm
          estimation={selectedEstimation}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      )}

      {currentView === 'view' && (
        <EstimationView
          estimation={selectedEstimation}
          onEdit={() => handleEdit(selectedEstimation)}
          onClose={handleCancel}
        />
      )}

      {currentView === 'selectPlan' && (
        <PlanSelectionDialog
          onPlanSelected={handlePlanSelected}
          onCancel={handleCancel}
        />
      )}
    </div>
  )
}
