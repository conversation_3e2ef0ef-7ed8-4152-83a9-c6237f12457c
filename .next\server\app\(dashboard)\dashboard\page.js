/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(dashboard)/dashboard/page";
exports.ids = ["app/(dashboard)/dashboard/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(dashboard)',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/dashboard/page.js */ \"(rsc)/./src/app/(dashboard)/dashboard/page.js\")), \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/layout.js */ \"(rsc)/./src/app/(dashboard)/layout.js\")), \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/(dashboard)/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(dashboard)/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.js%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Cfeatures%5Cauth%5Chooks%5CuseAuth.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Chooks%5CuseToast.js&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.js%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Cfeatures%5Cauth%5Chooks%5CuseAuth.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Chooks%5CuseToast.js&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/auth/hooks/useAuth.js */ \"(ssr)/./src/features/auth/hooks/useAuth.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/hooks/useToast.js */ \"(ssr)/./src/hooks/useToast.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q01vYmlvUHJvamVjdHMlNUNjb3ZleW9yLXBvYyU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQuanMlMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCU3RCU1RCUyQyUyMnZhcmlhYmxlTmFtZSUyMiUzQSUyMmludGVyJTIyJTdEJm1vZHVsZXM9RCUzQSU1Q01vYmlvUHJvamVjdHMlNUNjb3ZleW9yLXBvYyU1Q3NyYyU1Q2FwcCU1Q2dsb2JhbHMuY3NzJm1vZHVsZXM9RCUzQSU1Q01vYmlvUHJvamVjdHMlNUNjb3ZleW9yLXBvYyU1Q3NyYyU1Q2ZlYXR1cmVzJTVDYXV0aCU1Q2hvb2tzJTVDdXNlQXV0aC5qcyZtb2R1bGVzPUQlM0ElNUNNb2Jpb1Byb2plY3RzJTVDY292ZXlvci1wb2MlNUNzcmMlNUNob29rcyU1Q3VzZVRvYXN0LmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvTEFBMkc7QUFDM0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3ZleW9yLXBvYy8/MDQ0NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXE1vYmlvUHJvamVjdHNcXFxcY292ZXlvci1wb2NcXFxcc3JjXFxcXGZlYXR1cmVzXFxcXGF1dGhcXFxcaG9va3NcXFxcdXNlQXV0aC5qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcTW9iaW9Qcm9qZWN0c1xcXFxjb3ZleW9yLXBvY1xcXFxzcmNcXFxcaG9va3NcXFxcdXNlVG9hc3QuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.js%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Cfeatures%5Cauth%5Chooks%5CuseAuth.js&modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Chooks%5CuseToast.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp%5C(dashboard)%5Cdashboard%5Cpage.js&server=true!":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp%5C(dashboard)%5Cdashboard%5Cpage.js&server=true! ***!
  \*************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/dashboard/page.js */ \"(ssr)/./src/app/(dashboard)/dashboard/page.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q01vYmlvUHJvamVjdHMlNUNjb3ZleW9yLXBvYyU1Q3NyYyU1Q2FwcCU1QyhkYXNoYm9hcmQpJTVDZGFzaGJvYXJkJTVDcGFnZS5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3ZleW9yLXBvYy8/NzUzNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXE1vYmlvUHJvamVjdHNcXFxcY292ZXlvci1wb2NcXFxcc3JjXFxcXGFwcFxcXFwoZGFzaGJvYXJkKVxcXFxkYXNoYm9hcmRcXFxccGFnZS5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp%5C(dashboard)%5Cdashboard%5Cpage.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp%5C(dashboard)%5Clayout.js&server=true!":
/*!***************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp%5C(dashboard)%5Clayout.js&server=true! ***!
  \***************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/layout.js */ \"(ssr)/./src/app/(dashboard)/layout.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q01vYmlvUHJvamVjdHMlNUNjb3ZleW9yLXBvYyU1Q3NyYyU1Q2FwcCU1QyhkYXNoYm9hcmQpJTVDbGF5b3V0LmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NvdmV5b3ItcG9jLz81M2I4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcTW9iaW9Qcm9qZWN0c1xcXFxjb3ZleW9yLXBvY1xcXFxzcmNcXFxcYXBwXFxcXChkYXNoYm9hcmQpXFxcXGxheW91dC5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp%5C(dashboard)%5Clayout.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(dashboard)/dashboard/page.js":
/*!***********************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calculator,FileText,Package,Package2,Settings2,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calculator,FileText,Package,Package2,Settings2,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calculator,FileText,Package,Package2,Settings2,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calculator,FileText,Package,Package2,Settings2,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calculator,FileText,Package,Package2,Settings2,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calculator,FileText,Package,Package2,Settings2,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calculator,FileText,Package,Package2,Settings2,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calculator,FileText,Package,Package2,Settings2,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Calculator,FileText,Package,Package2,Settings2,Shield,UserPlus,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings-2.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.js\");\n/* harmony import */ var _features_auth_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/auth/hooks/useAuth */ \"(ssr)/./src/features/auth/hooks/useAuth.js\");\n/* harmony import */ var _components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/common/LoadingSpinner */ \"(ssr)/./src/components/common/LoadingSpinner.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const { user, isManager } = (0,_features_auth_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [conveyorStats, setConveyorStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchStats = async ()=>{\n            try {\n                // Fetch user stats for managers\n                if (isManager) {\n                    const userResponse = await fetch(\"/api/users/stats\", {\n                        credentials: \"include\"\n                    });\n                    if (userResponse.ok) {\n                        const userData = await userResponse.json();\n                        setStats(userData.data);\n                    }\n                }\n                // Fetch conveyor system stats for all users\n                const conveyorResponse = await fetch(\"/api/dashboard/conveyor-stats\", {\n                    credentials: \"include\"\n                });\n                if (conveyorResponse.ok) {\n                    const conveyorData = await conveyorResponse.json();\n                    setConveyorStats(conveyorData.data);\n                }\n            } catch (error) {\n                console.error(\"Failed to fetch stats:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchStats();\n    }, [\n        isManager\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-brand-gradient rounded-xl p-6 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold mb-2\",\n                        children: [\n                            \"Welcome back, \",\n                            user?.firstName,\n                            \"! \\uD83D\\uDC4B\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80\",\n                        children: \"Here's what's happening with your team today.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            isManager && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: loading ? Array.from({\n                    length: 4\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"card-vibrant\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__.LoadingSpinner, {}, void 0, false, {\n                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                lineNumber: 84,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                            lineNumber: 83,\n                            columnNumber: 17\n                        }, this)\n                    }, i, false, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                        lineNumber: 82,\n                        columnNumber: 15\n                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"card-vibrant\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 92,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 text-brand-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 95,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 91,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-brand-700\",\n                                            children: stats?.total || 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 98,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: [\n                                                \"+\",\n                                                stats?.recent || 0,\n                                                \" this month\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 97,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                            lineNumber: 90,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"card-vibrant\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Active Users\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 109,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 text-success-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 112,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 108,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-success-700\",\n                                            children: stats?.byStatus?.active || 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: [\n                                                stats?.total ? Math.round(stats.byStatus.active / stats.total * 100) : 0,\n                                                \"% of total\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 118,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 114,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                            lineNumber: 107,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"card-vibrant\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Managers\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-700\",\n                                            children: stats?.byRole?.manager || 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 135,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: [\n                                                \"Including \",\n                                                stats?.byRole?.admin || 0,\n                                                \" admins\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 138,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 134,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                            lineNumber: 127,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"card-vibrant\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Pending\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 146,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-warning-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 145,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-warning-700\",\n                                            children: stats?.byStatus?.pending || 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 150,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: \"Awaiting activation\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 149,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                            lineNumber: 144,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                lineNumber: 79,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: loading ? Array.from({\n                    length: 4\n                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"card-vibrant\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_5__.LoadingSpinner, {}, void 0, false, {\n                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                lineNumber: 167,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                            lineNumber: 166,\n                            columnNumber: 15\n                        }, this)\n                    }, i, false, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"card-vibrant\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Equipment\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-700\",\n                                            children: conveyorStats?.totalEquipment || 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: [\n                                                conveyorStats?.activeEquipment || 0,\n                                                \" active\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"card-vibrant\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Components\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-700\",\n                                            children: conveyorStats?.totalComponents || 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: [\n                                                conveyorStats?.activeComponents || 0,\n                                                \" active\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"card-vibrant\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Plans\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-purple-700\",\n                                            children: conveyorStats?.totalPlans || 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: [\n                                                conveyorStats?.approvedPlans || 0,\n                                                \" approved\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"card-vibrant\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Estimations\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-orange-700\",\n                                            children: conveyorStats?.totalEstimations || 0\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600\",\n                                            children: [\n                                                conveyorStats?.finalEstimations || 0,\n                                                \" finalized\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 227,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"card-vibrant\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Quick Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Common tasks you can perform\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                        href: \"/equipment\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Manage Equipment\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                        lineNumber: 248,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                        href: \"/components\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Manage Components\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                        href: \"/conveyor-plans\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Create Conveyor Plan\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                        href: \"/estimations\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            className: \"w-full justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Generate Estimation\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this),\n                                    isManager && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                href: \"/users\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full justify-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Manage Users\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                                href: \"/equipment/categories\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full justify-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Calculator_FileText_Package_Package2_Settings2_Shield_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Equipment Categories\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                lineNumber: 247,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"card-vibrant\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Latest system activities\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-success-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"System running smoothly\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"All services operational\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-brand-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Welcome to Coveyor POC\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: \"Your internal tool is ready\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                lineNumber: 241,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(dashboard)/dashboard/page.js\n");

/***/ }),

/***/ "(ssr)/./src/app/(dashboard)/layout.js":
/*!***************************************!*\
  !*** ./src/app/(dashboard)/layout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/Sidebar */ \"(ssr)/./src/components/layout/Sidebar.js\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(ssr)/./src/components/layout/Header.js\");\n/* harmony import */ var _features_auth_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/auth/components/AuthGuard */ \"(ssr)/./src/features/auth/components/AuthGuard.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_auth_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requireAuth: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-screen bg-gray-50\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_1__.Sidebar, {}, void 0, false, {\n                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\layout.js\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex flex-col overflow-hidden lg:ml-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\layout.js\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"flex-1 overflow-auto\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\layout.js\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\layout.js\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\layout.js\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\(dashboard)\\\\layout.js\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwLyhkYXNoYm9hcmQpL2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRXFEO0FBQ0Y7QUFDYTtBQUVqRCxTQUFTRyxnQkFBZ0IsRUFBRUMsUUFBUSxFQUFFO0lBQ2xELHFCQUNFLDhEQUFDRiwwRUFBU0E7UUFBQ0csYUFBYTtrQkFDdEIsNEVBQUNDO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDUCwrREFBT0E7Ozs7OzhCQUNSLDhEQUFDTTtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNOLDZEQUFNQTs7Ozs7c0NBQ1AsOERBQUNPOzRCQUFLRCxXQUFVO3NDQUNiSDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNYiIsInNvdXJjZXMiOlsid2VicGFjazovL2NvdmV5b3ItcG9jLy4vc3JjL2FwcC8oZGFzaGJvYXJkKS9sYXlvdXQuanM/ZmNjMCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgU2lkZWJhciB9IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQvU2lkZWJhcidcbmltcG9ydCB7IEhlYWRlciB9IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQvSGVhZGVyJ1xuaW1wb3J0IHsgQXV0aEd1YXJkIH0gZnJvbSAnQC9mZWF0dXJlcy9hdXRoL2NvbXBvbmVudHMvQXV0aEd1YXJkJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmRMYXlvdXQoeyBjaGlsZHJlbiB9KSB7XG4gIHJldHVybiAoXG4gICAgPEF1dGhHdWFyZCByZXF1aXJlQXV0aD17dHJ1ZX0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgICA8U2lkZWJhciAvPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sIG92ZXJmbG93LWhpZGRlbiBsZzptbC0wXCI+XG4gICAgICAgICAgPEhlYWRlciAvPlxuICAgICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy1hdXRvXCI+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC9tYWluPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvQXV0aEd1YXJkPlxuICApXG59XG4iXSwibmFtZXMiOlsiU2lkZWJhciIsIkhlYWRlciIsIkF1dGhHdWFyZCIsIkRhc2hib2FyZExheW91dCIsImNoaWxkcmVuIiwicmVxdWlyZUF1dGgiLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(dashboard)/layout.js\n");

/***/ }),

/***/ "(ssr)/./src/components/common/LoadingSpinner.js":
/*!*************************************************!*\
  !*** ./src/components/common/LoadingSpinner.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingButton: () => (/* binding */ LoadingButton),\n/* harmony export */   LoadingCard: () => (/* binding */ LoadingCard),\n/* harmony export */   LoadingSpinner: () => (/* binding */ LoadingSpinner),\n/* harmony export */   LoadingTable: () => (/* binding */ LoadingTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils_cn__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/cn */ \"(ssr)/./src/lib/utils/cn.js\");\n\n\nfunction LoadingSpinner({ className, size = \"default\", ...props }) {\n    const sizeClasses = {\n        sm: \"h-4 w-4\",\n        default: \"h-6 w-6\",\n        lg: \"h-8 w-8\",\n        xl: \"h-12 w-12\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-spin rounded-full border-2 border-gray-300 border-t-brand-500\", sizeClasses[size], className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\LoadingSpinner.js\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingCard({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_1__.cn)(\"card-vibrant p-6 space-y-4\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"loading-pulse h-4 w-3/4 rounded\"\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\LoadingSpinner.js\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"loading-pulse h-4 w-1/2 rounded\"\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\LoadingSpinner.js\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"loading-pulse h-20 w-full rounded\"\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\LoadingSpinner.js\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\LoadingSpinner.js\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingTable({ rows = 5, cols = 4, className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_1__.cn)(\"space-y-3\", className),\n        ...props,\n        children: Array.from({\n            length: rows\n        }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex space-x-4\",\n                children: Array.from({\n                    length: cols\n                }).map((_, j)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"loading-pulse h-4 flex-1 rounded\"\n                    }, j, false, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\LoadingSpinner.js\",\n                        lineNumber: 45,\n                        columnNumber: 13\n                    }, this))\n            }, i, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\LoadingSpinner.js\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\LoadingSpinner.js\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\nfunction LoadingButton({ children, isLoading, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        disabled: isLoading,\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_1__.cn)(\"inline-flex items-center justify-center\", isLoading && \"opacity-50 cursor-not-allowed\"),\n        ...props,\n        children: [\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {\n                size: \"sm\",\n                className: \"mr-2\"\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\LoadingSpinner.js\",\n                lineNumber: 67,\n                columnNumber: 9\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\LoadingSpinner.js\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9jb21tb24vTG9hZGluZ1NwaW5uZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBbUM7QUFFNUIsU0FBU0MsZUFBZSxFQUFFQyxTQUFTLEVBQUVDLE9BQU8sU0FBUyxFQUFFLEdBQUdDLE9BQU87SUFDdEUsTUFBTUMsY0FBYztRQUNsQkMsSUFBSTtRQUNKQyxTQUFTO1FBQ1RDLElBQUk7UUFDSkMsSUFBSTtJQUNOO0lBRUEscUJBQ0UsOERBQUNDO1FBQ0NSLFdBQVdGLGlEQUFFQSxDQUNYLHlFQUNBSyxXQUFXLENBQUNGLEtBQUssRUFDakJEO1FBRUQsR0FBR0UsS0FBSzs7Ozs7O0FBR2Y7QUFFTyxTQUFTTyxZQUFZLEVBQUVULFNBQVMsRUFBRSxHQUFHRSxPQUFPO0lBQ2pELHFCQUNFLDhEQUFDTTtRQUNDUixXQUFXRixpREFBRUEsQ0FDWCw4QkFDQUU7UUFFRCxHQUFHRSxLQUFLOzswQkFFVCw4REFBQ007Z0JBQUlSLFdBQVU7Ozs7OzswQkFDZiw4REFBQ1E7Z0JBQUlSLFdBQVU7Ozs7OzswQkFDZiw4REFBQ1E7Z0JBQUlSLFdBQVU7Ozs7Ozs7Ozs7OztBQUdyQjtBQUVPLFNBQVNVLGFBQWEsRUFBRUMsT0FBTyxDQUFDLEVBQUVDLE9BQU8sQ0FBQyxFQUFFWixTQUFTLEVBQUUsR0FBR0UsT0FBTztJQUN0RSxxQkFDRSw4REFBQ007UUFBSVIsV0FBV0YsaURBQUVBLENBQUMsYUFBYUU7UUFBYSxHQUFHRSxLQUFLO2tCQUNsRFcsTUFBTUMsSUFBSSxDQUFDO1lBQUVDLFFBQVFKO1FBQUssR0FBR0ssR0FBRyxDQUFDLENBQUNDLEdBQUdDLGtCQUNwQyw4REFBQ1Y7Z0JBQVlSLFdBQVU7MEJBQ3BCYSxNQUFNQyxJQUFJLENBQUM7b0JBQUVDLFFBQVFIO2dCQUFLLEdBQUdJLEdBQUcsQ0FBQyxDQUFDQyxHQUFHRSxrQkFDcEMsOERBQUNYO3dCQUVDUixXQUFVO3VCQURMbUI7Ozs7O2VBSEREOzs7Ozs7Ozs7O0FBV2xCO0FBRU8sU0FBU0UsY0FBYyxFQUFFQyxRQUFRLEVBQUVDLFNBQVMsRUFBRSxHQUFHcEIsT0FBTztJQUM3RCxxQkFDRSw4REFBQ3FCO1FBQ0NDLFVBQVVGO1FBQ1Z0QixXQUFXRixpREFBRUEsQ0FDWCwyQ0FDQXdCLGFBQWE7UUFFZCxHQUFHcEIsS0FBSzs7WUFFUm9CLDJCQUNDLDhEQUFDdkI7Z0JBQWVFLE1BQUs7Z0JBQUtELFdBQVU7Ozs7OztZQUVyQ3FCOzs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3ZleW9yLXBvYy8uL3NyYy9jb21wb25lbnRzL2NvbW1vbi9Mb2FkaW5nU3Bpbm5lci5qcz85NDYxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzL2NuXCJcblxuZXhwb3J0IGZ1bmN0aW9uIExvYWRpbmdTcGlubmVyKHsgY2xhc3NOYW1lLCBzaXplID0gXCJkZWZhdWx0XCIsIC4uLnByb3BzIH0pIHtcbiAgY29uc3Qgc2l6ZUNsYXNzZXMgPSB7XG4gICAgc206IFwiaC00IHctNFwiLFxuICAgIGRlZmF1bHQ6IFwiaC02IHctNlwiLFxuICAgIGxnOiBcImgtOCB3LThcIixcbiAgICB4bDogXCJoLTEyIHctMTJcIixcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGJvcmRlci0yIGJvcmRlci1ncmF5LTMwMCBib3JkZXItdC1icmFuZC01MDBcIixcbiAgICAgICAgc2l6ZUNsYXNzZXNbc2l6ZV0sXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBMb2FkaW5nQ2FyZCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSkge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiY2FyZC12aWJyYW50IHAtNiBzcGFjZS15LTRcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibG9hZGluZy1wdWxzZSBoLTQgdy0zLzQgcm91bmRlZFwiPjwvZGl2PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2FkaW5nLXB1bHNlIGgtNCB3LTEvMiByb3VuZGVkXCI+PC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImxvYWRpbmctcHVsc2UgaC0yMCB3LWZ1bGwgcm91bmRlZFwiPjwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBMb2FkaW5nVGFibGUoeyByb3dzID0gNSwgY29scyA9IDQsIGNsYXNzTmFtZSwgLi4ucHJvcHMgfSkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbihcInNwYWNlLXktM1wiLCBjbGFzc05hbWUpfSB7Li4ucHJvcHN9PlxuICAgICAge0FycmF5LmZyb20oeyBsZW5ndGg6IHJvd3MgfSkubWFwKChfLCBpKSA9PiAoXG4gICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNFwiPlxuICAgICAgICAgIHtBcnJheS5mcm9tKHsgbGVuZ3RoOiBjb2xzIH0pLm1hcCgoXywgaikgPT4gKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBrZXk9e2p9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImxvYWRpbmctcHVsc2UgaC00IGZsZXgtMSByb3VuZGVkXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKSl9XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIExvYWRpbmdCdXR0b24oeyBjaGlsZHJlbiwgaXNMb2FkaW5nLCAuLi5wcm9wcyB9KSB7XG4gIHJldHVybiAoXG4gICAgPGJ1dHRvblxuICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ31cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCIsXG4gICAgICAgIGlzTG9hZGluZyAmJiBcIm9wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgPlxuICAgICAge2lzTG9hZGluZyAmJiAoXG4gICAgICAgIDxMb2FkaW5nU3Bpbm5lciBzaXplPVwic21cIiBjbGFzc05hbWU9XCJtci0yXCIgLz5cbiAgICAgICl9XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9idXR0b24+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJjbiIsIkxvYWRpbmdTcGlubmVyIiwiY2xhc3NOYW1lIiwic2l6ZSIsInByb3BzIiwic2l6ZUNsYXNzZXMiLCJzbSIsImRlZmF1bHQiLCJsZyIsInhsIiwiZGl2IiwiTG9hZGluZ0NhcmQiLCJMb2FkaW5nVGFibGUiLCJyb3dzIiwiY29scyIsIkFycmF5IiwiZnJvbSIsImxlbmd0aCIsIm1hcCIsIl8iLCJpIiwiaiIsIkxvYWRpbmdCdXR0b24iLCJjaGlsZHJlbiIsImlzTG9hZGluZyIsImJ1dHRvbiIsImRpc2FibGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/common/LoadingSpinner.js\n");

/***/ }),

/***/ "(ssr)/./src/components/common/StatusBadge.js":
/*!**********************************************!*\
  !*** ./src/components/common/StatusBadge.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoleBadge: () => (/* binding */ RoleBadge),\n/* harmony export */   StatusBadge: () => (/* binding */ StatusBadge)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./src/components/ui/badge.js\");\n/* harmony import */ var _lib_utils_cn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/cn */ \"(ssr)/./src/lib/utils/cn.js\");\n\n\n\nconst statusConfig = {\n    ACTIVE: {\n        variant: \"active\",\n        label: \"Active\",\n        className: \"bg-success-gradient text-success-900\"\n    },\n    INACTIVE: {\n        variant: \"inactive\",\n        label: \"Inactive\",\n        className: \"bg-gray-100 text-gray-700\"\n    },\n    PENDING: {\n        variant: \"pending\",\n        label: \"Pending\",\n        className: \"bg-warning-gradient text-warning-900\"\n    },\n    SUSPENDED: {\n        variant: \"destructive\",\n        label: \"Suspended\",\n        className: \"bg-danger-gradient text-danger-900\"\n    }\n};\nconst roleConfig = {\n    ADMIN: {\n        variant: \"default\",\n        label: \"Admin\",\n        className: \"bg-brand-gradient text-white\"\n    },\n    MANAGER: {\n        variant: \"secondary\",\n        label: \"Manager\",\n        className: \"bg-purple-100 text-purple-800\"\n    },\n    USER: {\n        variant: \"outline\",\n        label: \"User\",\n        className: \"bg-blue-50 text-blue-700 border-blue-200\"\n    }\n};\nfunction StatusBadge({ status, className, ...props }) {\n    const config = statusConfig[status] || statusConfig.INACTIVE;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n        variant: config.variant,\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)(config.className, className),\n        ...props,\n        children: config.label\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\StatusBadge.js\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\nfunction RoleBadge({ role, className, ...props }) {\n    const config = roleConfig[role] || roleConfig.USER;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n        variant: config.variant,\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)(config.className, className),\n        ...props,\n        children: config.label\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\common\\\\StatusBadge.js\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/common/StatusBadge.js\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.js":
/*!*****************************************!*\
  !*** ./src/components/layout/Header.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Bell_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.js\");\n/* harmony import */ var _features_auth_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/auth/hooks/useAuth */ \"(ssr)/./src/features/auth/hooks/useAuth.js\");\n/* harmony import */ var _components_common_StatusBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/common/StatusBadge */ \"(ssr)/./src/components/common/StatusBadge.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\nfunction Header({ title, subtitle }) {\n    const { user } = (0,_features_auth_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"header-gradient border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                lineNumber: 19,\n                                columnNumber: 15\n                            }, this),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mt-1\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                lineNumber: 21,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                        lineNumber: 18,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative hidden md:block\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                    type: \"search\",\n                                    placeholder: \"Search...\",\n                                    className: \"pl-10 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                    lineNumber: 41,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center\",\n                                    children: \"3\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden sm:block text-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: [\n                                                user?.firstName,\n                                                \" \",\n                                                user?.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-end space-x-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_StatusBadge__WEBPACK_IMPORTED_MODULE_4__.RoleBadge, {\n                                                role: user?.role\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                                lineNumber: 54,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                            lineNumber: 53,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-brand-gradient rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: [\n                                            user?.firstName?.[0],\n                                            user?.lastName?.[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Header.js\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.js\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.js":
/*!******************************************!*\
  !*** ./src/components/layout/Sidebar.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calculator,FileText,LayoutDashboard,LogOut,Menu,Package,Package2,Settings,Settings2,Shield,UserPlus,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.js\");\n/* harmony import */ var _features_auth_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/features/auth/hooks/useAuth */ \"(ssr)/./src/features/auth/hooks/useAuth.js\");\n/* harmony import */ var _lib_utils_cn__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils/cn */ \"(ssr)/./src/lib/utils/cn.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Equipment Master\",\n        href: \"/equipment\",\n        icon: _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        description: \"Manage conveyor equipment and pricing\"\n    },\n    {\n        name: \"Equipment Categories\",\n        href: \"/equipment/categories\",\n        icon: _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        description: \"Manage equipment categories\"\n    },\n    {\n        name: \"Component Master\",\n        href: \"/components\",\n        icon: _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        description: \"Manage raw materials and components\"\n    },\n    {\n        name: \"Conveyor Plans\",\n        href: \"/conveyor-plans\",\n        icon: _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        description: \"Design conveyor system configurations\"\n    },\n    {\n        name: \"Estimation Sheets\",\n        href: \"/estimations\",\n        icon: _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        description: \"Generate cost estimations and quotations\"\n    },\n    {\n        name: \"Users\",\n        href: \"/users\",\n        icon: _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        requiredRole: \"MANAGER\"\n    },\n    {\n        name: \"Settings\",\n        href: \"/settings\",\n        icon: _barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        requiredRole: \"ADMIN\"\n    }\n];\nfunction Sidebar() {\n    const [isMobileOpen, setIsMobileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { user, logout, isManager, isAdmin } = (0,_features_auth_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const canAccessItem = (item)=>{\n        if (!item.requiredRole) return true;\n        if (item.requiredRole === \"MANAGER\") return isManager;\n        if (item.requiredRole === \"ADMIN\") return isAdmin;\n        return false;\n    };\n    const filteredNavigation = navigation.filter(canAccessItem);\n    const handleLogout = async ()=>{\n        await logout();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed top-4 left-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"outline\",\n                    size: \"icon\",\n                    onClick: ()=>setIsMobileOpen(!isMobileOpen),\n                    className: \"bg-white shadow-lg\",\n                    children: isMobileOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                        lineNumber: 104,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                        lineNumber: 106,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            isMobileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40\",\n                onClick: ()=>setIsMobileOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_6__.cn)(\"fixed inset-y-0 left-0 z-50 w-64 sidebar-gradient transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\", isMobileOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-16 px-4 border-b border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-white rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5 text-brand-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                            lineNumber: 131,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl font-bold text-white\",\n                                        children: \"Coveyor\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 py-6 space-y-2\",\n                            children: filteredNavigation.map((item)=>{\n                                const isActive = pathname === item.href || pathname.startsWith(item.href + \"/\");\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    onClick: ()=>setIsMobileOpen(false),\n                                    className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_6__.cn)(\"flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors\", isActive ? \"bg-white/20 text-white shadow-lg\" : \"text-white/80 hover:bg-white/10 hover:text-white\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"mr-3 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                            lineNumber: 154,\n                                            columnNumber: 19\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, item.name, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-white\",\n                                                children: [\n                                                    user?.firstName?.[0],\n                                                    user?.lastName?.[0]\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-white truncate\",\n                                                    children: [\n                                                        user?.firstName,\n                                                        \" \",\n                                                        user?.lastName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-white/60 truncate\",\n                                                    children: user?.email\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: handleLogout,\n                                    className: \"w-full justify-start text-white/80 hover:bg-white/10 hover:text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calculator_FileText_LayoutDashboard_LogOut_Menu_Package_Package2_Settings_Settings2_Shield_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Sign Out\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\layout\\\\Sidebar.js\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.js\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/badge.js":
/*!************************************!*\
  !*** ./src/components/ui/badge.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils_cn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/cn */ \"(ssr)/./src/lib/utils/cn.js\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n            outline: \"text-foreground\",\n            // Custom status variants\n            active: \"badge-active border-transparent\",\n            inactive: \"badge-inactive border-transparent\",\n            pending: \"badge-pending border-transparent\",\n            success: \"border-transparent bg-success-500 text-white shadow\",\n            warning: \"border-transparent bg-warning-500 text-white shadow\",\n            danger: \"border-transparent bg-danger-500 text-white shadow\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\ui\\\\badge.js\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/badge.js\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.js":
/*!*************************************!*\
  !*** ./src/components/ui/button.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils_cn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/cn */ \"(ssr)/./src/lib/utils/cn.js\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\",\n            // Custom vibrant variants\n            gradient: \"btn-gradient-primary\",\n            gradientSuccess: \"btn-gradient-success\",\n            gradientWarning: \"text-gray-800 font-semibold transition-all duration-300 transform hover:scale-105 bg-warning-gradient shadow-lg hover:shadow-xl\",\n            gradientDanger: \"text-white font-semibold transition-all duration-300 transform hover:scale-105 bg-danger-gradient shadow-lg hover:shadow-xl\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2\",\n            sm: \"h-8 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            xl: \"h-12 rounded-lg px-10 text-base\",\n            icon: \"h-9 w-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\ui\\\\button.js\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.js\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.js":
/*!***********************************!*\
  !*** ./src/components/ui/card.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils_cn__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/cn */ \"(ssr)/./src/lib/utils/cn.js\");\n\n\n\n\nconst cardVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"rounded-xl border bg-card text-card-foreground shadow\", {\n    variants: {\n        variant: {\n            default: \"card-vibrant\",\n            primary: \"card-vibrant-primary\",\n            success: \"card-vibrant-success\",\n            outline: \"border-2 border-gray-200 bg-white\",\n            ghost: \"border-0 shadow-none bg-transparent\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(cardVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\ui\\\\card.js\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\ui\\\\card.js\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(\"font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\ui\\\\card.js\",\n        lineNumber: 42,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\ui\\\\card.js\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\ui\\\\card.js\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\ui\\\\card.js\",\n        lineNumber: 65,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.js\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.js":
/*!************************************!*\
  !*** ./src/components/ui/input.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils_cn__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/cn */ \"(ssr)/./src/lib/utils/cn.js\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils_cn__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50\", \"input-vibrant\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\components\\\\ui\\\\input.js\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBQ0s7QUFFbkMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUFDLENBQUMsRUFBRUksU0FBUyxFQUFFQyxJQUFJLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUM3RCxxQkFDRSw4REFBQ0M7UUFDQ0gsTUFBTUE7UUFDTkQsV0FBV0gsaURBQUVBLENBQ1gseVVBQ0EsaUJBQ0FHO1FBRUZHLEtBQUtBO1FBQ0osR0FBR0QsS0FBSzs7Ozs7O0FBR2Y7QUFDQUosTUFBTU8sV0FBVyxHQUFHO0FBRUoiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3ZleW9yLXBvYy8uL3NyYy9jb21wb25lbnRzL3VpL2lucHV0LmpzPzE1NTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzL2NuXCJcblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmKCh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPGlucHV0XG4gICAgICB0eXBlPXt0eXBlfVxuICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgXCJmbGV4IGgtOSB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLXRyYW5zcGFyZW50IHB4LTMgcHktMSB0ZXh0LXNtIHNoYWRvdy1zbSB0cmFuc2l0aW9uLWNvbG9ycyBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0xIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgIFwiaW5wdXQtdmlicmFudFwiLCAvLyBDdXN0b20gdmlicmFudCBzdHlsaW5nXG4gICAgICAgIGNsYXNzTmFtZVxuICAgICAgKX1cbiAgICAgIHJlZj17cmVmfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn0pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.js\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/AuthGuard.js":
/*!***************************************************!*\
  !*** ./src/features/auth/components/AuthGuard.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminGuard: () => (/* binding */ AdminGuard),\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   ManagerGuard: () => (/* binding */ ManagerGuard),\n/* harmony export */   PermissionGuard: () => (/* binding */ PermissionGuard),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_auth_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/auth/hooks/useAuth */ \"(ssr)/./src/features/auth/hooks/useAuth.js\");\n/* harmony import */ var _components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/common/LoadingSpinner */ \"(ssr)/./src/components/common/LoadingSpinner.js\");\n/* harmony import */ var _lib_auth_permissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/auth/permissions */ \"(ssr)/./src/lib/auth/permissions.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,withAuth,AdminGuard,ManagerGuard,PermissionGuard auto */ \n\n\n\n\n\nfunction AuthGuard({ children, requireAuth = true, requiredRole = null, requiredPermission = null, fallback = null }) {\n    const { user, loading, isAuthenticated } = (0,_features_auth_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && requireAuth && !isAuthenticated) {\n            router.push(\"/login\");\n        }\n    }, [\n        loading,\n        requireAuth,\n        isAuthenticated,\n        router\n    ]);\n    // Show loading spinner while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_LoadingSpinner__WEBPACK_IMPORTED_MODULE_4__.LoadingSpinner, {\n                        size: \"lg\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this);\n    }\n    // If authentication is required but user is not authenticated\n    if (requireAuth && !isAuthenticated) {\n        return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Redirecting to login...\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                    lineNumber: 42,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this);\n    }\n    // If user is authenticated, check role and permission requirements\n    if (isAuthenticated) {\n        // Check role requirement\n        if (requiredRole && !(0,_lib_auth_permissions__WEBPACK_IMPORTED_MODULE_5__.hasRoleLevel)(user.role, requiredRole)) {\n            return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Access Denied\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"You don't have permission to access this page.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                    lineNumber: 54,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, this);\n        }\n        // Check permission requirement\n        if (requiredPermission && !(0,_lib_auth_permissions__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(user.role, requiredPermission)) {\n            return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Access Denied\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"You don't have the required permission to access this page.\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this);\n        }\n    }\n    return children;\n}\n// Higher-order component for protecting pages\nfunction withAuth(Component, options = {}) {\n    return function AuthenticatedComponent(props) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n            ...options,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    };\n}\n// Role-based guards\nfunction AdminGuard({ children, fallback }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n        requiredRole: \"ADMIN\",\n        fallback: fallback,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\nfunction ManagerGuard({ children, fallback }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n        requiredRole: \"MANAGER\",\n        fallback: fallback,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n        lineNumber: 103,\n        columnNumber: 5\n    }, this);\n}\n// Permission-based guard\nfunction PermissionGuard({ children, permission, fallback }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthGuard, {\n        requiredPermission: permission,\n        fallback: fallback,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\components\\\\AuthGuard.js\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/AuthGuard.js\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/hooks/useAuth.js":
/*!********************************************!*\
  !*** ./src/features/auth/hooks/useAuth.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check authentication status on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    const checkAuth = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/me\", {\n                credentials: \"include\"\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setUser(data.data);\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Auth check failed:\", error);\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.message || \"Login failed\");\n            }\n            setUser(data.data.user);\n            return {\n                success: true,\n                user: data.data.user\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            setUser(null);\n            router.push(\"/login\");\n        }\n    };\n    const updateProfile = async (profileData)=>{\n        try {\n            const response = await fetch(\"/api/auth/profile\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify(profileData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.message || \"Profile update failed\");\n            }\n            setUser(data.data);\n            return {\n                success: true,\n                user: data.data\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n    };\n    const changePassword = async (currentPassword, newPassword)=>{\n        try {\n            const response = await fetch(\"/api/auth/change-password\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    currentPassword,\n                    newPassword\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.message || \"Password change failed\");\n            }\n            return {\n                success: true\n            };\n        } catch (error) {\n            return {\n                success: false,\n                error: error.message\n            };\n        }\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        updateProfile,\n        changePassword,\n        checkAuth,\n        isAuthenticated: !!user,\n        isAdmin: user?.role === \"ADMIN\",\n        isManager: user?.role === \"MANAGER\" || user?.role === \"ADMIN\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\features\\\\auth\\\\hooks\\\\useAuth.js\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/hooks/useAuth.js\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useToast.js":
/*!*******************************!*\
  !*** ./src/hooks/useToast.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast,default auto */ \n\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nfunction ToastProvider({ children }) {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((message, type = \"info\", duration = 5000)=>{\n        const id = Date.now() + Math.random();\n        const toast = {\n            id,\n            message,\n            type,\n            duration\n        };\n        setToasts((prev)=>[\n                ...prev,\n                toast\n            ]);\n        // Auto remove toast after duration\n        if (duration > 0) {\n            setTimeout(()=>{\n                removeToast(id);\n            }, duration);\n        }\n        return id;\n    }, []);\n    const removeToast = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    }, []);\n    const clearAllToasts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setToasts([]);\n    }, []);\n    const value = {\n        toasts,\n        showToast,\n        removeToast,\n        clearAllToasts\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: value,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\hooks\\\\useToast.js\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\hooks\\\\useToast.js\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\nfunction ToastContainer() {\n    const { toasts, removeToast } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (toasts.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                toast: toast,\n                onRemove: removeToast\n            }, toast.id, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\hooks\\\\useToast.js\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\hooks\\\\useToast.js\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\nfunction Toast({ toast, onRemove }) {\n    const getToastStyles = (type)=>{\n        const baseStyles = \"px-4 py-3 rounded-lg shadow-lg border-l-4 flex items-center justify-between min-w-80 max-w-md\";\n        switch(type){\n            case \"success\":\n                return `${baseStyles} bg-green-50 border-green-500 text-green-800`;\n            case \"error\":\n                return `${baseStyles} bg-red-50 border-red-500 text-red-800`;\n            case \"warning\":\n                return `${baseStyles} bg-yellow-50 border-yellow-500 text-yellow-800`;\n            default:\n                return `${baseStyles} bg-blue-50 border-blue-500 text-blue-800`;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: getToastStyles(toast.type),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"flex-1\",\n                children: toast.message\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\hooks\\\\useToast.js\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>onRemove(toast.id),\n                className: \"ml-3 text-gray-400 hover:text-gray-600 focus:outline-none\",\n                children: \"\\xd7\"\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\hooks\\\\useToast.js\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\hooks\\\\useToast.js\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\nfunction useToast() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (!context) {\n        throw new Error(\"useToast must be used within a ToastProvider\");\n    }\n    return context;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useToast);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlVG9hc3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFd0U7QUFFeEUsTUFBTUksNkJBQWVKLG9EQUFhQSxDQUFDLENBQUM7QUFFN0IsU0FBU0ssY0FBYyxFQUFFQyxRQUFRLEVBQUU7SUFDeEMsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdOLCtDQUFRQSxDQUFDLEVBQUU7SUFFdkMsTUFBTU8sWUFBWU4sa0RBQVdBLENBQUMsQ0FBQ08sU0FBU0MsT0FBTyxNQUFNLEVBQUVDLFdBQVcsSUFBSTtRQUNwRSxNQUFNQyxLQUFLQyxLQUFLQyxHQUFHLEtBQUtDLEtBQUtDLE1BQU07UUFDbkMsTUFBTUMsUUFBUTtZQUNaTDtZQUNBSDtZQUNBQztZQUNBQztRQUNGO1FBRUFKLFVBQVVXLENBQUFBLE9BQVE7bUJBQUlBO2dCQUFNRDthQUFNO1FBRWxDLG1DQUFtQztRQUNuQyxJQUFJTixXQUFXLEdBQUc7WUFDaEJRLFdBQVc7Z0JBQ1RDLFlBQVlSO1lBQ2QsR0FBR0Q7UUFDTDtRQUVBLE9BQU9DO0lBQ1QsR0FBRyxFQUFFO0lBRUwsTUFBTVEsY0FBY2xCLGtEQUFXQSxDQUFDLENBQUNVO1FBQy9CTCxVQUFVVyxDQUFBQSxPQUFRQSxLQUFLRyxNQUFNLENBQUNKLENBQUFBLFFBQVNBLE1BQU1MLEVBQUUsS0FBS0E7SUFDdEQsR0FBRyxFQUFFO0lBRUwsTUFBTVUsaUJBQWlCcEIsa0RBQVdBLENBQUM7UUFDakNLLFVBQVUsRUFBRTtJQUNkLEdBQUcsRUFBRTtJQUVMLE1BQU1nQixRQUFRO1FBQ1pqQjtRQUNBRTtRQUNBWTtRQUNBRTtJQUNGO0lBRUEscUJBQ0UsOERBQUNuQixhQUFhcUIsUUFBUTtRQUFDRCxPQUFPQTs7WUFDM0JsQjswQkFDRCw4REFBQ29COzs7Ozs7Ozs7OztBQUdQO0FBRUEsU0FBU0E7SUFDUCxNQUFNLEVBQUVuQixNQUFNLEVBQUVjLFdBQVcsRUFBRSxHQUFHcEIsaURBQVVBLENBQUNHO0lBRTNDLElBQUlHLE9BQU9vQixNQUFNLEtBQUssR0FBRyxPQUFPO0lBRWhDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNadEIsT0FBT3VCLEdBQUcsQ0FBQyxDQUFDWixzQkFDWCw4REFBQ2E7Z0JBQXFCYixPQUFPQTtnQkFBT2MsVUFBVVg7ZUFBbENILE1BQU1MLEVBQUU7Ozs7Ozs7Ozs7QUFJNUI7QUFFQSxTQUFTa0IsTUFBTSxFQUFFYixLQUFLLEVBQUVjLFFBQVEsRUFBRTtJQUNoQyxNQUFNQyxpQkFBaUIsQ0FBQ3RCO1FBQ3RCLE1BQU11QixhQUFhO1FBRW5CLE9BQVF2QjtZQUNOLEtBQUs7Z0JBQ0gsT0FBTyxDQUFDLEVBQUV1QixXQUFXLDRDQUE0QyxDQUFDO1lBQ3BFLEtBQUs7Z0JBQ0gsT0FBTyxDQUFDLEVBQUVBLFdBQVcsc0NBQXNDLENBQUM7WUFDOUQsS0FBSztnQkFDSCxPQUFPLENBQUMsRUFBRUEsV0FBVywrQ0FBK0MsQ0FBQztZQUN2RTtnQkFDRSxPQUFPLENBQUMsRUFBRUEsV0FBVyx5Q0FBeUMsQ0FBQztRQUNuRTtJQUNGO0lBRUEscUJBQ0UsOERBQUNOO1FBQUlDLFdBQVdJLGVBQWVmLE1BQU1QLElBQUk7OzBCQUN2Qyw4REFBQ3dCO2dCQUFLTixXQUFVOzBCQUFVWCxNQUFNUixPQUFPOzs7Ozs7MEJBQ3ZDLDhEQUFDMEI7Z0JBQ0NDLFNBQVMsSUFBTUwsU0FBU2QsTUFBTUwsRUFBRTtnQkFDaENnQixXQUFVOzBCQUNYOzs7Ozs7Ozs7Ozs7QUFLUDtBQUVPLFNBQVNTO0lBQ2QsTUFBTUMsVUFBVXRDLGlEQUFVQSxDQUFDRztJQUMzQixJQUFJLENBQUNtQyxTQUFTO1FBQ1osTUFBTSxJQUFJQyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVDtBQUVBLGlFQUFlRCxRQUFRQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY292ZXlvci1wb2MvLi9zcmMvaG9va3MvdXNlVG9hc3QuanM/NzM3YiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnXG5cbmNvbnN0IFRvYXN0Q29udGV4dCA9IGNyZWF0ZUNvbnRleHQoe30pXG5cbmV4cG9ydCBmdW5jdGlvbiBUb2FzdFByb3ZpZGVyKHsgY2hpbGRyZW4gfSkge1xuICBjb25zdCBbdG9hc3RzLCBzZXRUb2FzdHNdID0gdXNlU3RhdGUoW10pXG5cbiAgY29uc3Qgc2hvd1RvYXN0ID0gdXNlQ2FsbGJhY2soKG1lc3NhZ2UsIHR5cGUgPSAnaW5mbycsIGR1cmF0aW9uID0gNTAwMCkgPT4ge1xuICAgIGNvbnN0IGlkID0gRGF0ZS5ub3coKSArIE1hdGgucmFuZG9tKClcbiAgICBjb25zdCB0b2FzdCA9IHtcbiAgICAgIGlkLFxuICAgICAgbWVzc2FnZSxcbiAgICAgIHR5cGUsXG4gICAgICBkdXJhdGlvbixcbiAgICB9XG5cbiAgICBzZXRUb2FzdHMocHJldiA9PiBbLi4ucHJldiwgdG9hc3RdKVxuXG4gICAgLy8gQXV0byByZW1vdmUgdG9hc3QgYWZ0ZXIgZHVyYXRpb25cbiAgICBpZiAoZHVyYXRpb24gPiAwKSB7XG4gICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgcmVtb3ZlVG9hc3QoaWQpXG4gICAgICB9LCBkdXJhdGlvbilcbiAgICB9XG5cbiAgICByZXR1cm4gaWRcbiAgfSwgW10pXG5cbiAgY29uc3QgcmVtb3ZlVG9hc3QgPSB1c2VDYWxsYmFjaygoaWQpID0+IHtcbiAgICBzZXRUb2FzdHMocHJldiA9PiBwcmV2LmZpbHRlcih0b2FzdCA9PiB0b2FzdC5pZCAhPT0gaWQpKVxuICB9LCBbXSlcblxuICBjb25zdCBjbGVhckFsbFRvYXN0cyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRUb2FzdHMoW10pXG4gIH0sIFtdKVxuXG4gIGNvbnN0IHZhbHVlID0ge1xuICAgIHRvYXN0cyxcbiAgICBzaG93VG9hc3QsXG4gICAgcmVtb3ZlVG9hc3QsXG4gICAgY2xlYXJBbGxUb2FzdHMsXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxUb2FzdENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDxUb2FzdENvbnRhaW5lciAvPlxuICAgIDwvVG9hc3RDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG5cbmZ1bmN0aW9uIFRvYXN0Q29udGFpbmVyKCkge1xuICBjb25zdCB7IHRvYXN0cywgcmVtb3ZlVG9hc3QgfSA9IHVzZUNvbnRleHQoVG9hc3RDb250ZXh0KVxuXG4gIGlmICh0b2FzdHMubGVuZ3RoID09PSAwKSByZXR1cm4gbnVsbFxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCB0b3AtNCByaWdodC00IHotNTAgc3BhY2UteS0yXCI+XG4gICAgICB7dG9hc3RzLm1hcCgodG9hc3QpID0+IChcbiAgICAgICAgPFRvYXN0IGtleT17dG9hc3QuaWR9IHRvYXN0PXt0b2FzdH0gb25SZW1vdmU9e3JlbW92ZVRvYXN0fSAvPlxuICAgICAgKSl9XG4gICAgPC9kaXY+XG4gIClcbn1cblxuZnVuY3Rpb24gVG9hc3QoeyB0b2FzdCwgb25SZW1vdmUgfSkge1xuICBjb25zdCBnZXRUb2FzdFN0eWxlcyA9ICh0eXBlKSA9PiB7XG4gICAgY29uc3QgYmFzZVN0eWxlcyA9IFwicHgtNCBweS0zIHJvdW5kZWQtbGcgc2hhZG93LWxnIGJvcmRlci1sLTQgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1pbi13LTgwIG1heC13LW1kXCJcbiAgICBcbiAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgIGNhc2UgJ3N1Y2Nlc3MnOlxuICAgICAgICByZXR1cm4gYCR7YmFzZVN0eWxlc30gYmctZ3JlZW4tNTAgYm9yZGVyLWdyZWVuLTUwMCB0ZXh0LWdyZWVuLTgwMGBcbiAgICAgIGNhc2UgJ2Vycm9yJzpcbiAgICAgICAgcmV0dXJuIGAke2Jhc2VTdHlsZXN9IGJnLXJlZC01MCBib3JkZXItcmVkLTUwMCB0ZXh0LXJlZC04MDBgXG4gICAgICBjYXNlICd3YXJuaW5nJzpcbiAgICAgICAgcmV0dXJuIGAke2Jhc2VTdHlsZXN9IGJnLXllbGxvdy01MCBib3JkZXIteWVsbG93LTUwMCB0ZXh0LXllbGxvdy04MDBgXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gYCR7YmFzZVN0eWxlc30gYmctYmx1ZS01MCBib3JkZXItYmx1ZS01MDAgdGV4dC1ibHVlLTgwMGBcbiAgICB9XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtnZXRUb2FzdFN0eWxlcyh0b2FzdC50eXBlKX0+XG4gICAgICA8c3BhbiBjbGFzc05hbWU9XCJmbGV4LTFcIj57dG9hc3QubWVzc2FnZX08L3NwYW4+XG4gICAgICA8YnV0dG9uXG4gICAgICAgIG9uQ2xpY2s9eygpID0+IG9uUmVtb3ZlKHRvYXN0LmlkKX1cbiAgICAgICAgY2xhc3NOYW1lPVwibWwtMyB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDAgZm9jdXM6b3V0bGluZS1ub25lXCJcbiAgICAgID5cbiAgICAgICAgw5dcbiAgICAgIDwvYnV0dG9uPlxuICAgIDwvZGl2PlxuICApXG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VUb2FzdCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVG9hc3RDb250ZXh0KVxuICBpZiAoIWNvbnRleHQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVRvYXN0IG11c3QgYmUgdXNlZCB3aXRoaW4gYSBUb2FzdFByb3ZpZGVyJylcbiAgfVxuICByZXR1cm4gY29udGV4dFxufVxuXG5leHBvcnQgZGVmYXVsdCB1c2VUb2FzdFxuIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsIlRvYXN0Q29udGV4dCIsIlRvYXN0UHJvdmlkZXIiLCJjaGlsZHJlbiIsInRvYXN0cyIsInNldFRvYXN0cyIsInNob3dUb2FzdCIsIm1lc3NhZ2UiLCJ0eXBlIiwiZHVyYXRpb24iLCJpZCIsIkRhdGUiLCJub3ciLCJNYXRoIiwicmFuZG9tIiwidG9hc3QiLCJwcmV2Iiwic2V0VGltZW91dCIsInJlbW92ZVRvYXN0IiwiZmlsdGVyIiwiY2xlYXJBbGxUb2FzdHMiLCJ2YWx1ZSIsIlByb3ZpZGVyIiwiVG9hc3RDb250YWluZXIiLCJsZW5ndGgiLCJkaXYiLCJjbGFzc05hbWUiLCJtYXAiLCJUb2FzdCIsIm9uUmVtb3ZlIiwiZ2V0VG9hc3RTdHlsZXMiLCJiYXNlU3R5bGVzIiwic3BhbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJ1c2VUb2FzdCIsImNvbnRleHQiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useToast.js\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth/permissions.js":
/*!*************************************!*\
  !*** ./src/lib/auth/permissions.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES),\n/* harmony export */   canAccessResource: () => (/* binding */ canAccessResource),\n/* harmony export */   canManageUser: () => (/* binding */ canManageUser),\n/* harmony export */   filterByPermissions: () => (/* binding */ filterByPermissions),\n/* harmony export */   getRolePermissions: () => (/* binding */ getRolePermissions),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRoleLevel: () => (/* binding */ hasRoleLevel),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole)\n/* harmony export */ });\n/**\n * User roles hierarchy\n */ const USER_ROLES = {\n    ADMIN: \"ADMIN\",\n    MANAGER: \"MANAGER\",\n    USER: \"USER\"\n};\n/**\n * Role hierarchy levels (higher number = more permissions)\n */ const ROLE_LEVELS = {\n    [USER_ROLES.USER]: 1,\n    [USER_ROLES.MANAGER]: 2,\n    [USER_ROLES.ADMIN]: 3\n};\n/**\n * Permissions for different actions\n */ const PERMISSIONS = {\n    // User management\n    USER_CREATE: \"user:create\",\n    USER_READ: \"user:read\",\n    USER_UPDATE: \"user:update\",\n    USER_DELETE: \"user:delete\",\n    USER_INVITE: \"user:invite\",\n    USER_ACTIVATE: \"user:activate\",\n    USER_DEACTIVATE: \"user:deactivate\",\n    // Role management\n    ROLE_ASSIGN: \"role:assign\",\n    ROLE_VIEW: \"role:view\",\n    // System\n    AUDIT_VIEW: \"audit:view\",\n    SYSTEM_CONFIG: \"system:config\"\n};\n/**\n * Role-based permissions mapping\n */ const ROLE_PERMISSIONS = {\n    [USER_ROLES.ADMIN]: [\n        PERMISSIONS.USER_CREATE,\n        PERMISSIONS.USER_READ,\n        PERMISSIONS.USER_UPDATE,\n        PERMISSIONS.USER_DELETE,\n        PERMISSIONS.USER_INVITE,\n        PERMISSIONS.USER_ACTIVATE,\n        PERMISSIONS.USER_DEACTIVATE,\n        PERMISSIONS.ROLE_ASSIGN,\n        PERMISSIONS.ROLE_VIEW,\n        PERMISSIONS.AUDIT_VIEW,\n        PERMISSIONS.SYSTEM_CONFIG\n    ],\n    [USER_ROLES.MANAGER]: [\n        PERMISSIONS.USER_CREATE,\n        PERMISSIONS.USER_READ,\n        PERMISSIONS.USER_UPDATE,\n        PERMISSIONS.USER_INVITE,\n        PERMISSIONS.USER_ACTIVATE,\n        PERMISSIONS.USER_DEACTIVATE,\n        PERMISSIONS.ROLE_VIEW,\n        PERMISSIONS.AUDIT_VIEW\n    ],\n    [USER_ROLES.USER]: [\n        PERMISSIONS.USER_READ\n    ]\n};\n/**\n * Check if user has specific permission\n */ function hasPermission(userRole, permission) {\n    if (!userRole || !permission) return false;\n    const rolePermissions = ROLE_PERMISSIONS[userRole] || [];\n    return rolePermissions.includes(permission);\n}\n/**\n * Check if user has any of the specified permissions\n */ function hasAnyPermission(userRole, permissions) {\n    if (!userRole || !permissions || !Array.isArray(permissions)) return false;\n    return permissions.some((permission)=>hasPermission(userRole, permission));\n}\n/**\n * Check if user has all of the specified permissions\n */ function hasAllPermissions(userRole, permissions) {\n    if (!userRole || !permissions || !Array.isArray(permissions)) return false;\n    return permissions.every((permission)=>hasPermission(userRole, permission));\n}\n/**\n * Check if user role is higher than or equal to required role\n */ function hasRoleLevel(userRole, requiredRole) {\n    if (!userRole || !requiredRole) return false;\n    const userLevel = ROLE_LEVELS[userRole] || 0;\n    const requiredLevel = ROLE_LEVELS[requiredRole] || 0;\n    return userLevel >= requiredLevel;\n}\n/**\n * Check if user can manage another user (based on role hierarchy)\n */ function canManageUser(managerRole, targetRole) {\n    if (!managerRole || !targetRole) return false;\n    const managerLevel = ROLE_LEVELS[managerRole] || 0;\n    const targetLevel = ROLE_LEVELS[targetRole] || 0;\n    // Can manage users with lower or equal role level\n    return managerLevel >= targetLevel;\n}\n/**\n * Get all permissions for a role\n */ function getRolePermissions(role) {\n    return ROLE_PERMISSIONS[role] || [];\n}\n/**\n * Check if user can access resource\n */ function canAccessResource(userRole, resource, action = \"read\") {\n    const permission = `${resource}:${action}`;\n    return hasPermission(userRole, permission);\n}\n/**\n * Filter data based on user permissions\n */ function filterByPermissions(data, userRole, filterFn) {\n    if (!Array.isArray(data)) return data;\n    return data.filter((item)=>filterFn(item, userRole));\n}\n/**\n * Permission middleware helper\n */ function requirePermission(permission) {\n    return (userRole)=>{\n        if (!hasPermission(userRole, permission)) {\n            throw new Error(`Insufficient permissions. Required: ${permission}`);\n        }\n        return true;\n    };\n}\n/**\n * Role middleware helper\n */ function requireRole(requiredRole) {\n    return (userRole)=>{\n        if (!hasRoleLevel(userRole, requiredRole)) {\n            throw new Error(`Insufficient role level. Required: ${requiredRole}`);\n        }\n        return true;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth/permissions.js\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils/cn.js":
/*!*****************************!*\
  !*** ./src/lib/utils/cn.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzL2NuLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyQjtBQUNhO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBTTtJQUMxQixPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY292ZXlvci1wb2MvLi9zcmMvbGliL3V0aWxzL2NuLmpzP2U3NGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils/cn.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"c71fab985956\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY292ZXlvci1wb2MvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2I2NGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJjNzFmYWI5ODU5NTZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/(dashboard)/dashboard/page.js":
/*!***********************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\MobioProjects\coveyor-poc\src\app\(dashboard)\dashboard\page.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/(dashboard)/layout.js":
/*!***************************************!*\
  !*** ./src/app/(dashboard)/layout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\MobioProjects\coveyor-poc\src\app\(dashboard)\layout.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _features_auth_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/auth/hooks/useAuth */ \"(rsc)/./src/features/auth/hooks/useAuth.js\");\n/* harmony import */ var _hooks_useToast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useToast */ \"(rsc)/./src/hooks/useToast.js\");\n\n\n\n\n\nconst metadata = {\n    title: \"Coveyor POC - Internal Tool\",\n    description: \"Modern internal tool with user management and authentication\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useToast__WEBPACK_IMPORTED_MODULE_3__.ToastProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_auth_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\layout.js\",\n                    lineNumber: 18,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\layout.js\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBS01BO0FBSmlCO0FBQ3NDO0FBQ1o7QUFJMUMsTUFBTUcsV0FBVztJQUN0QkMsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFBRUMsUUFBUSxFQUFFO0lBQzdDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXWCw4SkFBZTtzQkFDOUIsNEVBQUNFLDBEQUFhQTswQkFDWiw0RUFBQ0Qsc0VBQVlBOzhCQUFFTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3pCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY292ZXlvci1wb2MvLi9zcmMvYXBwL2xheW91dC5qcz81YjE5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gXCJAL2ZlYXR1cmVzL2F1dGgvaG9va3MvdXNlQXV0aFwiO1xuaW1wb3J0IHsgVG9hc3RQcm92aWRlciB9IGZyb20gXCJAL2hvb2tzL3VzZVRvYXN0XCI7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJDb3ZleW9yIFBPQyAtIEludGVybmFsIFRvb2xcIixcbiAgZGVzY3JpcHRpb246IFwiTW9kZXJuIGludGVybmFsIHRvb2wgd2l0aCB1c2VyIG1hbmFnZW1lbnQgYW5kIGF1dGhlbnRpY2F0aW9uXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHsgY2hpbGRyZW4gfSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8VG9hc3RQcm92aWRlcj5cbiAgICAgICAgICA8QXV0aFByb3ZpZGVyPntjaGlsZHJlbn08L0F1dGhQcm92aWRlcj5cbiAgICAgICAgPC9Ub2FzdFByb3ZpZGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIkF1dGhQcm92aWRlciIsIlRvYXN0UHJvdmlkZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/features/auth/hooks/useAuth.js":
/*!********************************************!*\
  !*** ./src/features/auth/hooks/useAuth.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\MobioProjects\coveyor-poc\src\features\auth\hooks\useAuth.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\MobioProjects\coveyor-poc\src\features\auth\hooks\useAuth.js#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\MobioProjects\coveyor-poc\src\features\auth\hooks\useAuth.js#useAuth`);


/***/ }),

/***/ "(rsc)/./src/hooks/useToast.js":
/*!*******************************!*\
  !*** ./src/hooks/useToast.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   ToastProvider: () => (/* binding */ e0),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useToast: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\MobioProjects\coveyor-poc\src\hooks\useToast.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\MobioProjects\coveyor-poc\src\hooks\useToast.js#ToastProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\MobioProjects\coveyor-poc\src\hooks\useToast.js#useToast`);


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();