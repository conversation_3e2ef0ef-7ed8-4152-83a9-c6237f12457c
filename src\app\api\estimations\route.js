import { getCurrentUser } from '@/lib/auth/session'
import { getEstimationSheets, createEstimationSheet } from '@/features/estimation/services/estimationService'
import { estimationFilterSchema, createEstimationSchema } from '@/features/estimation/validation/estimationSchemas'
import { successResponse, unauthorizedResponse, forbiddenResponse, handleApiError, paginatedResponse } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view estimation sheets')
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const filters = estimationFilterSchema.parse({
      search: searchParams.get('search') || '',
      planId: searchParams.get('planId') || undefined,
      status: searchParams.get('status') || undefined,
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
      sortBy: searchParams.get('sortBy') || 'generatedAt',
      sortOrder: searchParams.get('sortOrder') || 'desc',
    })

    // Get estimation sheets
    const result = await getEstimationSheets(filters)
    
    return paginatedResponse(
      result.estimations,
      result.pagination,
      'Estimation sheets retrieved successfully'
    )
    
  } catch (error) {
    return handleApiError(error)
  }
}

export async function POST(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_CREATE)) {
      return forbiddenResponse('Insufficient permissions to create estimation sheets')
    }

    const body = await request.json()
    
    // Validate request body
    const validatedData = createEstimationSchema.parse(body)
    
    // Create estimation sheet
    const newEstimation = await createEstimationSheet(validatedData, user.id)
    
    return successResponse(newEstimation, 'Estimation sheet created successfully', null, 201)
    
  } catch (error) {
    return handleApiError(error)
  }
}
