import { getCurrentUser } from '@/lib/auth/session'
import { getEquipmentStats } from '@/features/equipment/services/equipmentService'
import { getComponentStats } from '@/features/components/services/componentService'
import { getConveyorPlanStats } from '@/features/conveyor/services/conveyorService'
import { getEstimationStats } from '@/features/estimation/services/estimationService'
import { successResponse, unauthorizedResponse, handleApiError } from '@/lib/api/response'

export async function GET(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Fetch stats from all modules
    const [
      equipmentStats,
      componentStats,
      planStats,
      estimationStats
    ] = await Promise.all([
      getEquipmentStats(),
      getComponentStats(),
      getConveyorPlanStats(),
      getEstimationStats()
    ])

    // Combine all stats
    const conveyorStats = {
      // Equipment stats
      totalEquipment: equipmentStats.totalEquipment,
      activeEquipment: equipmentStats.activeEquipment,
      inactiveEquipment: equipmentStats.inactiveEquipment,
      totalCategories: equipmentStats.totalCategories,
      activeCategories: equipmentStats.activeCategories,
      recentEquipment: equipmentStats.recentEquipment,

      // Component stats
      totalComponents: componentStats.totalComponents,
      activeComponents: componentStats.activeComponents,
      inactiveComponents: componentStats.inactiveComponents,
      componentCategoriesCount: componentStats.categoriesCount,
      recentComponents: componentStats.recentComponents,

      // Plan stats
      totalPlans: planStats.totalPlans,
      draftPlans: planStats.draftPlans,
      submittedPlans: planStats.submittedPlans,
      approvedPlans: planStats.approvedPlans,
      rejectedPlans: planStats.rejectedPlans,
      recentPlans: planStats.recentPlans,

      // Estimation stats
      totalEstimations: estimationStats.totalEstimations,
      draftEstimations: estimationStats.draftEstimations,
      finalEstimations: estimationStats.finalEstimations,
      sentEstimations: estimationStats.sentEstimations,
      approvedEstimations: estimationStats.approvedEstimations,
      recentEstimations: estimationStats.recentEstimations,
    }
    
    return successResponse(conveyorStats, 'Conveyor system statistics retrieved successfully')
    
  } catch (error) {
    return handleApiError(error)
  }
}
