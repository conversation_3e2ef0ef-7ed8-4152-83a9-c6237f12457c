import { getCurrentUser } from '@/lib/auth/session'
import { deactivateUser } from '@/features/users/services/userService'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_DEACTIVATE)) {
      return forbiddenResponse('Insufficient permissions to deactivate user')
    }

    const userId = params.id
    
    // Prevent self-deactivation
    if (userId === user.id) {
      return forbiddenResponse('Cannot deactivate your own account')
    }
    
    // Deactivate user
    const deactivatedUser = await deactivateUser(userId, user.id)
    
    return successResponse(deactivatedUser, 'User deactivated successfully')
    
  } catch (error) {
    if (error.message === 'User not found') {
      return notFoundResponse('User not found')
    }
    return handleApiError(error)
  }
}
