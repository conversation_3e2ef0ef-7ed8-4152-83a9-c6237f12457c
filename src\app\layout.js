import { Inter } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/features/auth/hooks/useAuth'

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: 'Coveyor POC - Internal Tool',
  description: 'Modern internal tool with user management and authentication',
}

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  )
}
