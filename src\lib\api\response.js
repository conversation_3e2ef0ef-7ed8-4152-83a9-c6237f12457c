import { NextResponse } from 'next/server'

/**
 * Standard API response structure
 */
export class ApiResponse {
  constructor(success = true, data = null, message = null, errors = null, meta = null) {
    this.success = success
    this.data = data
    this.message = message
    this.errors = errors
    this.meta = meta
    this.timestamp = new Date().toISOString()
  }
}

/**
 * Success response helper
 */
export function successResponse(data = null, message = null, meta = null, status = 200) {
  const response = new ApiResponse(true, data, message, null, meta)
  return NextResponse.json(response, { status })
}

/**
 * Error response helper
 */
export function errorResponse(message, errors = null, status = 400) {
  const response = new ApiResponse(false, null, message, errors)
  return NextResponse.json(response, { status })
}

/**
 * Validation error response
 */
export function validationErrorResponse(errors, message = 'Validation failed') {
  return errorResponse(message, errors, 422)
}

/**
 * Unauthorized response
 */
export function unauthorizedResponse(message = 'Unauthorized') {
  return errorResponse(message, null, 401)
}

/**
 * Forbidden response
 */
export function forbiddenResponse(message = 'Forbidden') {
  return errorResponse(message, null, 403)
}

/**
 * Not found response
 */
export function notFoundResponse(message = 'Resource not found') {
  return errorResponse(message, null, 404)
}

/**
 * Internal server error response
 */
export function serverErrorResponse(message = 'Internal server error') {
  return errorResponse(message, null, 500)
}

/**
 * Created response
 */
export function createdResponse(data, message = 'Resource created successfully') {
  return successResponse(data, message, null, 201)
}

/**
 * No content response
 */
export function noContentResponse() {
  return new NextResponse(null, { status: 204 })
}

/**
 * Paginated response helper
 */
export function paginatedResponse(data, pagination, message = null) {
  const meta = {
    pagination: {
      page: pagination.page,
      limit: pagination.limit,
      total: pagination.total,
      totalPages: Math.ceil(pagination.total / pagination.limit),
      hasNext: pagination.page < Math.ceil(pagination.total / pagination.limit),
      hasPrev: pagination.page > 1,
    }
  }
  
  return successResponse(data, message, meta)
}

/**
 * Handle API errors consistently
 */
export function handleApiError(error) {
  console.error('API Error:', error)
  
  // Prisma errors
  if (error.code === 'P2002') {
    return errorResponse('Resource already exists', null, 409)
  }
  
  if (error.code === 'P2025') {
    return notFoundResponse('Resource not found')
  }
  
  // Validation errors (Zod)
  if (error.name === 'ZodError') {
    const validationErrors = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message,
      code: err.code,
    }))
    return validationErrorResponse(validationErrors)
  }
  
  // JWT errors
  if (error.name === 'JsonWebTokenError') {
    return unauthorizedResponse('Invalid token')
  }
  
  if (error.name === 'TokenExpiredError') {
    return unauthorizedResponse('Token expired')
  }
  
  // Custom application errors
  if (error.message === 'Invalid credentials') {
    return unauthorizedResponse('Invalid email or password')
  }
  
  if (error.message === 'User not found') {
    return notFoundResponse('User not found')
  }
  
  if (error.message === 'Insufficient permissions') {
    return forbiddenResponse('Insufficient permissions')
  }
  
  // Default server error
  return serverErrorResponse()
}

/**
 * Async error handler wrapper
 */
export function withErrorHandling(handler) {
  return async (request, context) => {
    try {
      return await handler(request, context)
    } catch (error) {
      return handleApiError(error)
    }
  }
}
