"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/conveyor-plans/[id]/approve/route";
exports.ids = ["app/api/conveyor-plans/[id]/approve/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute&page=%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute&page=%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_MobioProjects_coveyor_poc_src_app_api_conveyor_plans_id_approve_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/conveyor-plans/[id]/approve/route.js */ \"(rsc)/./src/app/api/conveyor-plans/[id]/approve/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/conveyor-plans/[id]/approve/route\",\n        pathname: \"/api/conveyor-plans/[id]/approve\",\n        filename: \"route\",\n        bundlePath: \"app/api/conveyor-plans/[id]/approve/route\"\n    },\n    resolvedPagePath: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\api\\\\conveyor-plans\\\\[id]\\\\approve\\\\route.js\",\n    nextConfigOutput,\n    userland: D_MobioProjects_coveyor_poc_src_app_api_conveyor_plans_id_approve_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/conveyor-plans/[id]/approve/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute&page=%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/conveyor-plans/[id]/approve/route.js":
/*!**********************************************************!*\
  !*** ./src/app/api/conveyor-plans/[id]/approve/route.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_session__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth/session */ \"(rsc)/./src/lib/auth/session.js\");\n/* harmony import */ var _features_conveyor_services_conveyorService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/conveyor/services/conveyorService */ \"(rsc)/./src/features/conveyor/services/conveyorService.js\");\n/* harmony import */ var _lib_api_response__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/response */ \"(rsc)/./src/lib/api/response.js\");\n/* harmony import */ var _lib_auth_permissions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth/permissions */ \"(rsc)/./src/lib/auth/permissions.js\");\n\n\n\n\nasync function PUT(request, { params }) {\n    try {\n        const user = await (0,_lib_auth_session__WEBPACK_IMPORTED_MODULE_0__.getCurrentUser)();\n        if (!user) {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.unauthorizedResponse)(\"Authentication required\");\n        }\n        // Check permission - only managers and admins can approve\n        if (!(0,_lib_auth_permissions__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(user.role, _lib_auth_permissions__WEBPACK_IMPORTED_MODULE_3__.PERMISSIONS.USER_UPDATE) || user.role === \"USER\") {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.forbiddenResponse)(\"Insufficient permissions to approve conveyor plan\");\n        }\n        const planId = params.id;\n        // Approve plan\n        const approvedPlan = await (0,_features_conveyor_services_conveyorService__WEBPACK_IMPORTED_MODULE_1__.approveConveyorPlan)(planId, user.id);\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.successResponse)(approvedPlan, \"Conveyor plan approved successfully\");\n    } catch (error) {\n        if (error.message === \"Conveyor plan not found\") {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.notFoundResponse)(\"Conveyor plan not found\");\n        }\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.handleApiError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jb252ZXlvci1wbGFucy9baWRdL2FwcHJvdmUvcm91dGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBbUQ7QUFDK0I7QUFDNkM7QUFDNUQ7QUFFNUQsZUFBZVMsSUFBSUMsT0FBTyxFQUFFLEVBQUVDLE1BQU0sRUFBRTtJQUMzQyxJQUFJO1FBQ0YsTUFBTUMsT0FBTyxNQUFNWixpRUFBY0E7UUFFakMsSUFBSSxDQUFDWSxNQUFNO1lBQ1QsT0FBT1QsdUVBQW9CQSxDQUFDO1FBQzlCO1FBRUEsMERBQTBEO1FBQzFELElBQUksQ0FBQ0ksb0VBQWFBLENBQUNLLEtBQUtDLElBQUksRUFBRUwsOERBQVdBLENBQUNNLFdBQVcsS0FBS0YsS0FBS0MsSUFBSSxLQUFLLFFBQVE7WUFDOUUsT0FBT1Qsb0VBQWlCQSxDQUFDO1FBQzNCO1FBRUEsTUFBTVcsU0FBU0osT0FBT0ssRUFBRTtRQUV4QixlQUFlO1FBQ2YsTUFBTUMsZUFBZSxNQUFNaEIsZ0dBQW1CQSxDQUFDYyxRQUFRSCxLQUFLSSxFQUFFO1FBRTlELE9BQU9kLGtFQUFlQSxDQUFDZSxjQUFjO0lBRXZDLEVBQUUsT0FBT0MsT0FBTztRQUNkLElBQUlBLE1BQU1DLE9BQU8sS0FBSywyQkFBMkI7WUFDL0MsT0FBT2QsbUVBQWdCQSxDQUFDO1FBQzFCO1FBQ0EsT0FBT0MsaUVBQWNBLENBQUNZO0lBQ3hCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3ZleW9yLXBvYy8uL3NyYy9hcHAvYXBpL2NvbnZleW9yLXBsYW5zL1tpZF0vYXBwcm92ZS9yb3V0ZS5qcz9kNTllIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGdldEN1cnJlbnRVc2VyIH0gZnJvbSAnQC9saWIvYXV0aC9zZXNzaW9uJ1xuaW1wb3J0IHsgYXBwcm92ZUNvbnZleW9yUGxhbiB9IGZyb20gJ0AvZmVhdHVyZXMvY29udmV5b3Ivc2VydmljZXMvY29udmV5b3JTZXJ2aWNlJ1xuaW1wb3J0IHsgc3VjY2Vzc1Jlc3BvbnNlLCB1bmF1dGhvcml6ZWRSZXNwb25zZSwgZm9yYmlkZGVuUmVzcG9uc2UsIG5vdEZvdW5kUmVzcG9uc2UsIGhhbmRsZUFwaUVycm9yIH0gZnJvbSAnQC9saWIvYXBpL3Jlc3BvbnNlJ1xuaW1wb3J0IHsgaGFzUGVybWlzc2lvbiwgUEVSTUlTU0lPTlMgfSBmcm9tICdAL2xpYi9hdXRoL3Blcm1pc3Npb25zJ1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUFVUKHJlcXVlc3QsIHsgcGFyYW1zIH0pIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB1c2VyID0gYXdhaXQgZ2V0Q3VycmVudFVzZXIoKVxuICAgIFxuICAgIGlmICghdXNlcikge1xuICAgICAgcmV0dXJuIHVuYXV0aG9yaXplZFJlc3BvbnNlKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCcpXG4gICAgfVxuXG4gICAgLy8gQ2hlY2sgcGVybWlzc2lvbiAtIG9ubHkgbWFuYWdlcnMgYW5kIGFkbWlucyBjYW4gYXBwcm92ZVxuICAgIGlmICghaGFzUGVybWlzc2lvbih1c2VyLnJvbGUsIFBFUk1JU1NJT05TLlVTRVJfVVBEQVRFKSB8fCB1c2VyLnJvbGUgPT09ICdVU0VSJykge1xuICAgICAgcmV0dXJuIGZvcmJpZGRlblJlc3BvbnNlKCdJbnN1ZmZpY2llbnQgcGVybWlzc2lvbnMgdG8gYXBwcm92ZSBjb252ZXlvciBwbGFuJylcbiAgICB9XG5cbiAgICBjb25zdCBwbGFuSWQgPSBwYXJhbXMuaWRcbiAgICBcbiAgICAvLyBBcHByb3ZlIHBsYW5cbiAgICBjb25zdCBhcHByb3ZlZFBsYW4gPSBhd2FpdCBhcHByb3ZlQ29udmV5b3JQbGFuKHBsYW5JZCwgdXNlci5pZClcbiAgICBcbiAgICByZXR1cm4gc3VjY2Vzc1Jlc3BvbnNlKGFwcHJvdmVkUGxhbiwgJ0NvbnZleW9yIHBsYW4gYXBwcm92ZWQgc3VjY2Vzc2Z1bGx5JylcbiAgICBcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBpZiAoZXJyb3IubWVzc2FnZSA9PT0gJ0NvbnZleW9yIHBsYW4gbm90IGZvdW5kJykge1xuICAgICAgcmV0dXJuIG5vdEZvdW5kUmVzcG9uc2UoJ0NvbnZleW9yIHBsYW4gbm90IGZvdW5kJylcbiAgICB9XG4gICAgcmV0dXJuIGhhbmRsZUFwaUVycm9yKGVycm9yKVxuICB9XG59XG4iXSwibmFtZXMiOlsiZ2V0Q3VycmVudFVzZXIiLCJhcHByb3ZlQ29udmV5b3JQbGFuIiwic3VjY2Vzc1Jlc3BvbnNlIiwidW5hdXRob3JpemVkUmVzcG9uc2UiLCJmb3JiaWRkZW5SZXNwb25zZSIsIm5vdEZvdW5kUmVzcG9uc2UiLCJoYW5kbGVBcGlFcnJvciIsImhhc1Blcm1pc3Npb24iLCJQRVJNSVNTSU9OUyIsIlBVVCIsInJlcXVlc3QiLCJwYXJhbXMiLCJ1c2VyIiwicm9sZSIsIlVTRVJfVVBEQVRFIiwicGxhbklkIiwiaWQiLCJhcHByb3ZlZFBsYW4iLCJlcnJvciIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/conveyor-plans/[id]/approve/route.js\n");

/***/ }),

/***/ "(rsc)/./src/features/conveyor/services/conveyorService.js":
/*!***********************************************************!*\
  !*** ./src/features/conveyor/services/conveyorService.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   approveConveyorPlan: () => (/* binding */ approveConveyorPlan),\n/* harmony export */   createConveyorPlan: () => (/* binding */ createConveyorPlan),\n/* harmony export */   deleteConveyorPlan: () => (/* binding */ deleteConveyorPlan),\n/* harmony export */   getConveyorPlanById: () => (/* binding */ getConveyorPlanById),\n/* harmony export */   getConveyorPlanStats: () => (/* binding */ getConveyorPlanStats),\n/* harmony export */   getConveyorPlans: () => (/* binding */ getConveyorPlans),\n/* harmony export */   rejectConveyorPlan: () => (/* binding */ rejectConveyorPlan),\n/* harmony export */   submitConveyorPlan: () => (/* binding */ submitConveyorPlan),\n/* harmony export */   updateConveyorPlan: () => (/* binding */ updateConveyorPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n\n/**\n * Get paginated conveyor plans list with filters\n */ async function getConveyorPlans(filters = {}) {\n    const { search = \"\", conveyorType, loadType, status, region, page = 1, limit = 10, sortBy = \"createdAt\", sortOrder = \"desc\" } = filters;\n    const skip = (page - 1) * limit;\n    // Build where clause\n    const where = {\n        AND: [\n            // Search filter\n            search ? {\n                OR: [\n                    {\n                        planName: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        conveyorType: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        siteConditions: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        specialRequirements: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                ]\n            } : {},\n            // Conveyor type filter\n            conveyorType ? {\n                conveyorType: {\n                    contains: conveyorType,\n                    mode: \"insensitive\"\n                }\n            } : {},\n            // Load type filter\n            loadType ? {\n                loadType\n            } : {},\n            // Status filter\n            status ? {\n                status\n            } : {},\n            // Region filter\n            region ? {\n                region: {\n                    contains: region,\n                    mode: \"insensitive\"\n                }\n            } : {}\n        ]\n    };\n    // Get plans with pagination\n    const [plans, total] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findMany({\n            where,\n            select: {\n                id: true,\n                planName: true,\n                conveyorType: true,\n                totalLengthM: true,\n                loadType: true,\n                capacityTph: true,\n                inclinationAngle: true,\n                driveType: true,\n                environment: true,\n                region: true,\n                status: true,\n                createdAt: true,\n                updatedAt: true,\n                _count: {\n                    select: {\n                        estimationSheets: true\n                    }\n                }\n            },\n            orderBy: {\n                [sortBy]: sortOrder\n            },\n            skip,\n            take: limit\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where\n        })\n    ]);\n    return {\n        plans,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit)\n        }\n    };\n}\n/**\n * Get conveyor plan by ID\n */ async function getConveyorPlanById(id) {\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            estimationSheets: {\n                select: {\n                    id: true,\n                    estimationVersion: true,\n                    totalCost: true,\n                    status: true,\n                    generatedAt: true\n                },\n                orderBy: {\n                    generatedAt: \"desc\"\n                }\n            }\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    return plan;\n}\n/**\n * Create new conveyor plan\n */ async function createConveyorPlan(planData, createdBy) {\n    const { planName, conveyorType, totalLengthM, loadType, capacityTph, inclinationAngle, driveType, environment, region, siteConditions, specialRequirements, status } = planData;\n    // Create plan\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.create({\n        data: {\n            planName,\n            conveyorType,\n            totalLengthM,\n            loadType,\n            capacityTph,\n            inclinationAngle,\n            driveType,\n            environment,\n            region,\n            siteConditions,\n            specialRequirements,\n            status,\n            createdBy\n        }\n    });\n    return plan;\n}\n/**\n * Update conveyor plan\n */ async function updateConveyorPlan(id, planData, updatedBy) {\n    const { planName, conveyorType, totalLengthM, loadType, capacityTph, inclinationAngle, driveType, environment, region, siteConditions, specialRequirements, status } = planData;\n    // Check if plan exists\n    const existingPlan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!existingPlan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    // Update plan\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            ...planName && {\n                planName\n            },\n            ...conveyorType && {\n                conveyorType\n            },\n            ...totalLengthM !== undefined && {\n                totalLengthM\n            },\n            ...loadType && {\n                loadType\n            },\n            ...capacityTph !== undefined && {\n                capacityTph\n            },\n            ...inclinationAngle !== undefined && {\n                inclinationAngle\n            },\n            ...driveType !== undefined && {\n                driveType\n            },\n            ...environment !== undefined && {\n                environment\n            },\n            ...region !== undefined && {\n                region\n            },\n            ...siteConditions !== undefined && {\n                siteConditions\n            },\n            ...specialRequirements !== undefined && {\n                specialRequirements\n            },\n            ...status && {\n                status\n            },\n            updatedBy\n        }\n    });\n    return plan;\n}\n/**\n * Delete conveyor plan\n */ async function deleteConveyorPlan(id, deletedBy) {\n    // Check if plan exists\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            _count: {\n                select: {\n                    estimationSheets: true\n                }\n            }\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    // Check if plan has estimation sheets\n    if (plan._count.estimationSheets > 0) {\n        throw new Error(\"Cannot delete plan that has estimation sheets associated with it\");\n    }\n    // Delete plan\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.delete({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    return {\n        message: \"Conveyor plan deleted successfully\"\n    };\n}\n/**\n * Submit conveyor plan for approval\n */ async function submitConveyorPlan(id, submittedBy) {\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    if (plan.status !== \"Draft\") {\n        throw new Error(\"Only draft plans can be submitted\");\n    }\n    const updatedPlan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            status: \"Submitted\",\n            updatedBy: submittedBy\n        }\n    });\n    return updatedPlan;\n}\n/**\n * Approve conveyor plan\n */ async function approveConveyorPlan(id, approvedBy) {\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    if (plan.status !== \"Submitted\") {\n        throw new Error(\"Only submitted plans can be approved\");\n    }\n    const updatedPlan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            status: \"Approved\",\n            updatedBy: approvedBy\n        }\n    });\n    return updatedPlan;\n}\n/**\n * Reject conveyor plan\n */ async function rejectConveyorPlan(id, rejectedBy) {\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    if (plan.status !== \"Submitted\") {\n        throw new Error(\"Only submitted plans can be rejected\");\n    }\n    const updatedPlan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            status: \"Rejected\",\n            updatedBy: rejectedBy\n        }\n    });\n    return updatedPlan;\n}\n/**\n * Get conveyor plan statistics\n */ async function getConveyorPlanStats() {\n    const [totalPlans, draftPlans, submittedPlans, approvedPlans, rejectedPlans, recentPlans] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count(),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where: {\n                status: \"Draft\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where: {\n                status: \"Submitted\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where: {\n                status: \"Approved\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where: {\n                status: \"Rejected\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where: {\n                createdAt: {\n                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                }\n            }\n        })\n    ]);\n    return {\n        totalPlans,\n        draftPlans,\n        submittedPlans,\n        approvedPlans,\n        rejectedPlans,\n        recentPlans\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/conveyor/services/conveyorService.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/response.js":
/*!*********************************!*\
  !*** ./src/lib/api/response.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiResponse: () => (/* binding */ ApiResponse),\n/* harmony export */   createdResponse: () => (/* binding */ createdResponse),\n/* harmony export */   errorResponse: () => (/* binding */ errorResponse),\n/* harmony export */   forbiddenResponse: () => (/* binding */ forbiddenResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   noContentResponse: () => (/* binding */ noContentResponse),\n/* harmony export */   notFoundResponse: () => (/* binding */ notFoundResponse),\n/* harmony export */   paginatedResponse: () => (/* binding */ paginatedResponse),\n/* harmony export */   serverErrorResponse: () => (/* binding */ serverErrorResponse),\n/* harmony export */   successResponse: () => (/* binding */ successResponse),\n/* harmony export */   unauthorizedResponse: () => (/* binding */ unauthorizedResponse),\n/* harmony export */   validationErrorResponse: () => (/* binding */ validationErrorResponse),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\n/**\n * Standard API response structure\n */ class ApiResponse {\n    constructor(success = true, data = null, message = null, errors = null, meta = null){\n        this.success = success;\n        this.data = data;\n        this.message = message;\n        this.errors = errors;\n        this.meta = meta;\n        this.timestamp = new Date().toISOString();\n    }\n}\n/**\n * Success response helper\n */ function successResponse(data = null, message = null, meta = null, status = 200) {\n    const response = new ApiResponse(true, data, message, null, meta);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Error response helper\n */ function errorResponse(message, errors = null, status = 400) {\n    const response = new ApiResponse(false, null, message, errors);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Validation error response\n */ function validationErrorResponse(errors, message = \"Validation failed\") {\n    return errorResponse(message, errors, 422);\n}\n/**\n * Unauthorized response\n */ function unauthorizedResponse(message = \"Unauthorized\") {\n    return errorResponse(message, null, 401);\n}\n/**\n * Forbidden response\n */ function forbiddenResponse(message = \"Forbidden\") {\n    return errorResponse(message, null, 403);\n}\n/**\n * Not found response\n */ function notFoundResponse(message = \"Resource not found\") {\n    return errorResponse(message, null, 404);\n}\n/**\n * Internal server error response\n */ function serverErrorResponse(message = \"Internal server error\") {\n    return errorResponse(message, null, 500);\n}\n/**\n * Created response\n */ function createdResponse(data, message = \"Resource created successfully\") {\n    return successResponse(data, message, null, 201);\n}\n/**\n * No content response\n */ function noContentResponse() {\n    return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](null, {\n        status: 204\n    });\n}\n/**\n * Paginated response helper\n */ function paginatedResponse(data, pagination, message = null) {\n    const meta = {\n        pagination: {\n            page: pagination.page,\n            limit: pagination.limit,\n            total: pagination.total,\n            totalPages: Math.ceil(pagination.total / pagination.limit),\n            hasNext: pagination.page < Math.ceil(pagination.total / pagination.limit),\n            hasPrev: pagination.page > 1\n        }\n    };\n    return successResponse(data, message, meta);\n}\n/**\n * Handle API errors consistently\n */ function handleApiError(error) {\n    console.error(\"API Error:\", error);\n    // Prisma errors\n    if (error.code === \"P2002\") {\n        return errorResponse(\"Resource already exists\", null, 409);\n    }\n    if (error.code === \"P2025\") {\n        return notFoundResponse(\"Resource not found\");\n    }\n    // Validation errors (Zod)\n    if (error.name === \"ZodError\") {\n        const validationErrors = error.errors.map((err)=>({\n                field: err.path.join(\".\"),\n                message: err.message,\n                code: err.code\n            }));\n        return validationErrorResponse(validationErrors);\n    }\n    // JWT errors\n    if (error.name === \"JsonWebTokenError\") {\n        return unauthorizedResponse(\"Invalid token\");\n    }\n    if (error.name === \"TokenExpiredError\") {\n        return unauthorizedResponse(\"Token expired\");\n    }\n    // Custom application errors\n    if (error.message === \"Invalid credentials\") {\n        return unauthorizedResponse(\"Invalid email or password\");\n    }\n    if (error.message === \"User not found\") {\n        return notFoundResponse(\"User not found\");\n    }\n    if (error.message === \"Insufficient permissions\") {\n        return forbiddenResponse(\"Insufficient permissions\");\n    }\n    // Default server error\n    return serverErrorResponse();\n}\n/**\n * Async error handler wrapper\n */ function withErrorHandling(handler) {\n    return async (request, context)=>{\n        try {\n            return await handler(request, context);\n        } catch (error) {\n            return handleApiError(error);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/response.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/permissions.js":
/*!*************************************!*\
  !*** ./src/lib/auth/permissions.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES),\n/* harmony export */   canAccessResource: () => (/* binding */ canAccessResource),\n/* harmony export */   canManageUser: () => (/* binding */ canManageUser),\n/* harmony export */   filterByPermissions: () => (/* binding */ filterByPermissions),\n/* harmony export */   getRolePermissions: () => (/* binding */ getRolePermissions),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRoleLevel: () => (/* binding */ hasRoleLevel),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole)\n/* harmony export */ });\n/**\n * User roles hierarchy\n */ const USER_ROLES = {\n    ADMIN: \"ADMIN\",\n    MANAGER: \"MANAGER\",\n    USER: \"USER\"\n};\n/**\n * Role hierarchy levels (higher number = more permissions)\n */ const ROLE_LEVELS = {\n    [USER_ROLES.USER]: 1,\n    [USER_ROLES.MANAGER]: 2,\n    [USER_ROLES.ADMIN]: 3\n};\n/**\n * Permissions for different actions\n */ const PERMISSIONS = {\n    // User management\n    USER_CREATE: \"user:create\",\n    USER_READ: \"user:read\",\n    USER_UPDATE: \"user:update\",\n    USER_DELETE: \"user:delete\",\n    USER_INVITE: \"user:invite\",\n    USER_ACTIVATE: \"user:activate\",\n    USER_DEACTIVATE: \"user:deactivate\",\n    // Role management\n    ROLE_ASSIGN: \"role:assign\",\n    ROLE_VIEW: \"role:view\",\n    // System\n    AUDIT_VIEW: \"audit:view\",\n    SYSTEM_CONFIG: \"system:config\"\n};\n/**\n * Role-based permissions mapping\n */ const ROLE_PERMISSIONS = {\n    [USER_ROLES.ADMIN]: [\n        PERMISSIONS.USER_CREATE,\n        PERMISSIONS.USER_READ,\n        PERMISSIONS.USER_UPDATE,\n        PERMISSIONS.USER_DELETE,\n        PERMISSIONS.USER_INVITE,\n        PERMISSIONS.USER_ACTIVATE,\n        PERMISSIONS.USER_DEACTIVATE,\n        PERMISSIONS.ROLE_ASSIGN,\n        PERMISSIONS.ROLE_VIEW,\n        PERMISSIONS.AUDIT_VIEW,\n        PERMISSIONS.SYSTEM_CONFIG\n    ],\n    [USER_ROLES.MANAGER]: [\n        PERMISSIONS.USER_CREATE,\n        PERMISSIONS.USER_READ,\n        PERMISSIONS.USER_UPDATE,\n        PERMISSIONS.USER_INVITE,\n        PERMISSIONS.USER_ACTIVATE,\n        PERMISSIONS.USER_DEACTIVATE,\n        PERMISSIONS.ROLE_VIEW,\n        PERMISSIONS.AUDIT_VIEW\n    ],\n    [USER_ROLES.USER]: [\n        PERMISSIONS.USER_READ\n    ]\n};\n/**\n * Check if user has specific permission\n */ function hasPermission(userRole, permission) {\n    if (!userRole || !permission) return false;\n    const rolePermissions = ROLE_PERMISSIONS[userRole] || [];\n    return rolePermissions.includes(permission);\n}\n/**\n * Check if user has any of the specified permissions\n */ function hasAnyPermission(userRole, permissions) {\n    if (!userRole || !permissions || !Array.isArray(permissions)) return false;\n    return permissions.some((permission)=>hasPermission(userRole, permission));\n}\n/**\n * Check if user has all of the specified permissions\n */ function hasAllPermissions(userRole, permissions) {\n    if (!userRole || !permissions || !Array.isArray(permissions)) return false;\n    return permissions.every((permission)=>hasPermission(userRole, permission));\n}\n/**\n * Check if user role is higher than or equal to required role\n */ function hasRoleLevel(userRole, requiredRole) {\n    if (!userRole || !requiredRole) return false;\n    const userLevel = ROLE_LEVELS[userRole] || 0;\n    const requiredLevel = ROLE_LEVELS[requiredRole] || 0;\n    return userLevel >= requiredLevel;\n}\n/**\n * Check if user can manage another user (based on role hierarchy)\n */ function canManageUser(managerRole, targetRole) {\n    if (!managerRole || !targetRole) return false;\n    const managerLevel = ROLE_LEVELS[managerRole] || 0;\n    const targetLevel = ROLE_LEVELS[targetRole] || 0;\n    // Can manage users with lower or equal role level\n    return managerLevel >= targetLevel;\n}\n/**\n * Get all permissions for a role\n */ function getRolePermissions(role) {\n    return ROLE_PERMISSIONS[role] || [];\n}\n/**\n * Check if user can access resource\n */ function canAccessResource(userRole, resource, action = \"read\") {\n    const permission = `${resource}:${action}`;\n    return hasPermission(userRole, permission);\n}\n/**\n * Filter data based on user permissions\n */ function filterByPermissions(data, userRole, filterFn) {\n    if (!Array.isArray(data)) return data;\n    return data.filter((item)=>filterFn(item, userRole));\n}\n/**\n * Permission middleware helper\n */ function requirePermission(permission) {\n    return (userRole)=>{\n        if (!hasPermission(userRole, permission)) {\n            throw new Error(`Insufficient permissions. Required: ${permission}`);\n        }\n        return true;\n    };\n}\n/**\n * Role middleware helper\n */ function requireRole(requiredRole) {\n    return (userRole)=>{\n        if (!hasRoleLevel(userRole, requiredRole)) {\n            throw new Error(`Insufficient role level. Required: ${requiredRole}`);\n        }\n        return true;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/permissions.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/session.js":
/*!*********************************!*\
  !*** ./src/lib/auth/session.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   clearAuthCookie: () => (/* binding */ clearAuthCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   deleteAllUserSessions: () => (/* binding */ deleteAllUserSessions),\n/* harmony export */   deleteSession: () => (/* binding */ deleteSession),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   setAuthCookie: () => (/* binding */ setAuthCookie),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n/* harmony import */ var _lib_config_env__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/config/env */ \"(rsc)/./src/lib/config/env.js\");\n\n\n\n\nconst JWT_SECRET = _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].JWT_SECRET;\nconst TOKEN_EXPIRY = \"7d\" // 7 days\n;\nconst COOKIE_NAME = \"auth-token\";\n/**\n * Generate JWT token for user\n */ function generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: TOKEN_EXPIRY,\n        issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n        audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n    });\n}\n/**\n * Verify JWT token\n */ function verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET, {\n            issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n            audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n        });\n    } catch (error) {\n        throw new Error(\"Invalid token\");\n    }\n}\n/**\n * Create session for user\n */ async function createSession(userId, userAgent = null, ipAddress = null) {\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now\n    ;\n    // Generate JWT token\n    const tokenPayload = {\n        userId,\n        sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    };\n    const token = generateToken(tokenPayload);\n    // Store session in database\n    const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.create({\n        data: {\n            userId,\n            token,\n            expiresAt,\n            userAgent,\n            ipAddress\n        }\n    });\n    return {\n        session,\n        token\n    };\n}\n/**\n * Get session from token\n */ async function getSession(token) {\n    if (!token) return null;\n    try {\n        // Verify JWT token\n        const payload = verifyToken(token);\n        // Get session from database\n        const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.findUnique({\n            where: {\n                token\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        role: true,\n                        status: true,\n                        avatar: true\n                    }\n                }\n            }\n        });\n        if (!session || session.expiresAt < new Date()) {\n            // Session expired or not found\n            if (session) {\n                await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n                    where: {\n                        id: session.id\n                    }\n                });\n            }\n            return null;\n        }\n        return session;\n    } catch (error) {\n        return null;\n    }\n}\n/**\n * Delete session\n */ async function deleteSession(token) {\n    if (!token) return;\n    try {\n        await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n            where: {\n                token\n            }\n        });\n    } catch (error) {\n    // Session might not exist, ignore error\n    }\n}\n/**\n * Delete all sessions for user\n */ async function deleteAllUserSessions(userId) {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            userId\n        }\n    });\n}\n/**\n * Clean up expired sessions\n */ async function cleanupExpiredSessions() {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            expiresAt: {\n                lt: new Date()\n            }\n        }\n    });\n}\n/**\n * Set auth cookie\n */ function setAuthCookie(token) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.set(COOKIE_NAME, token, {\n        httpOnly: true,\n        secure: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].NODE_ENV === \"production\",\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\n/**\n * Clear auth cookie\n */ function clearAuthCookie() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.delete(COOKIE_NAME);\n}\n/**\n * Get auth token from cookies\n */ function getAuthToken() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return cookieStore.get(COOKIE_NAME)?.value;\n}\n/**\n * Get current user from session\n */ async function getCurrentUser() {\n    const token = getAuthToken();\n    if (!token) return null;\n    const session = await getSession(token);\n    return session?.user || null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/session.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/config/env.js":
/*!*******************************!*\
  !*** ./src/lib/config/env.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   env: () => (/* binding */ env)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\nconst envSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"development\",\n        \"production\",\n        \"test\"\n    ]).default(\"development\"),\n    DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Database URL is required\"),\n    NEXTAUTH_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"NextAuth secret is required\"),\n    NEXTAUTH_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    JWT_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"JWT secret is required\"),\n    APP_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().default(\"http://localhost:3000\"),\n    APP_NAME: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default(\"Coveyor POC\"),\n    // Email configuration (optional)\n    SMTP_HOST: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_USER: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PASS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    FROM_EMAIL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email().optional()\n});\nfunction validateEnv() {\n    try {\n        return envSchema.parse(process.env);\n    } catch (error) {\n        console.error(\"❌ Invalid environment variables:\");\n        console.error(error.errors);\n        throw new Error(\"Environment validation failed\");\n    }\n}\nconst env = validateEnv();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (env);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config/env.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/prisma.js":
/*!******************************!*\
  !*** ./src/lib/db/prisma.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3ByaXNtYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUU7QUFFbEUsSUFBSUksSUFBeUIsRUFBYztJQUN6Q0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBQzNCO0FBRUEsaUVBQWVBLE1BQU1BLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3ZleW9yLXBvYy8uL3NyYy9saWIvZGIvcHJpc21hLmpzP2QyZTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXNcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbn1cblxuZXhwb3J0IGRlZmF1bHQgcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/prisma.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute&page=%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconveyor-plans%2F%5Bid%5D%2Fapprove%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();