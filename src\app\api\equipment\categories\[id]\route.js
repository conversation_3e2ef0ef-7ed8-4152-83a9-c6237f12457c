import { getCurrentUser } from '@/lib/auth/session'
import { getEquipmentCategoryById, updateEquipmentCategory, deleteEquipmentCategory } from '@/features/equipment/services/equipmentService'
import { updateEquipmentCategorySchema } from '@/features/equipment/validation/equipmentSchemas'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view equipment category')
    }

    const categoryId = params.id
    const category = await getEquipmentCategoryById(categoryId)

    return successResponse(category, 'Equipment category retrieved successfully')

  } catch (error) {
    if (error.message === 'Equipment category not found') {
      return notFoundResponse('Equipment category not found')
    }
    return handleApiError(error)
  }
}

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_UPDATE)) {
      return forbiddenResponse('Insufficient permissions to update equipment category')
    }

    const categoryId = params.id
    const body = await request.json()
    
    // Validate request body
    const validatedData = updateEquipmentCategorySchema.parse(body)
    
    // Update category
    const updatedCategory = await updateEquipmentCategory(categoryId, validatedData, user.id)
    
    return successResponse(updatedCategory, 'Equipment category updated successfully')
    
  } catch (error) {
    if (error.message === 'Equipment category not found') {
      return notFoundResponse('Equipment category not found')
    }
    return handleApiError(error)
  }
}

export async function DELETE(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_DELETE)) {
      return forbiddenResponse('Insufficient permissions to delete equipment category')
    }

    const categoryId = params.id
    
    // Delete category
    const result = await deleteEquipmentCategory(categoryId, user.id)
    
    return successResponse(result, 'Equipment category deleted successfully')
    
  } catch (error) {
    if (error.message === 'Equipment category not found') {
      return notFoundResponse('Equipment category not found')
    }
    return handleApiError(error)
  }
}
