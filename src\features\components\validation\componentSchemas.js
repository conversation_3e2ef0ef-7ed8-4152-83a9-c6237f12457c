import { z } from 'zod'

/**
 * Component Master validation schema
 */
export const createComponentSchema = z.object({
  name: z
    .string()
    .min(1, 'Component name is required')
    .min(2, 'Component name must be at least 2 characters')
    .max(150, 'Component name must be less than 150 characters'),
  code: z
    .string()
    .min(1, 'Component code is required')
    .min(2, 'Component code must be at least 2 characters')
    .max(100, 'Component code must be less than 100 characters')
    .regex(/^[A-Z0-9_-]+$/, 'Component code must contain only uppercase letters, numbers, underscores, and hyphens'),
  category: z
    .string()
    .min(1, 'Category is required')
    .max(100, 'Category must be less than 100 characters'),
  equipmentId: z
    .number()
    .int()
    .positive('Equipment is required'),
  description: z
    .string()
    .max(1000, 'Description must be less than 1000 characters')
    .optional(),
  uom: z
    .string()
    .min(1, 'Unit of measure is required')
    .max(50, 'Unit of measure must be less than 50 characters'),
  baseRate: z
    .number()
    .positive('Base rate must be a positive number')
    .max(999999999999, 'Base rate is too large'),
  rateEffectiveFrom: z
    .string()
    .datetime('Invalid effective from date')
    .or(z.date()),
  validUpto: z
    .string()
    .datetime('Invalid valid upto date')
    .or(z.date())
    .optional(),
  vendorInfo: z
    .string()
    .max(255, 'Vendor info must be less than 255 characters')
    .optional(),
  status: z.boolean().default(true),
})

export const updateComponentSchema = createComponentSchema.partial()

/**
 * Component search/filter validation schema
 */
export const componentFilterSchema = z.object({
  search: z.string().optional(),
  category: z.string().optional(),
  equipmentId: z.coerce.number().int().positive().optional(),
  status: z.boolean().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  sortBy: z.enum(['name', 'code', 'category', 'baseRate', 'rateEffectiveFrom', 'createdAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

/**
 * Component categories for dropdown
 */
export const componentCategories = [
  'Steel',
  'Rubber',
  'Plastic',
  'Electronics',
  'Mechanical',
  'Hydraulic',
  'Pneumatic',
  'Electrical',
  'Fasteners',
  'Bearings',
  'Motors',
  'Sensors',
  'Other'
]
