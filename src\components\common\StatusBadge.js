import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils/cn'

const statusConfig = {
  ACTIVE: {
    variant: 'active',
    label: 'Active',
    className: 'bg-success-gradient text-success-900',
  },
  INACTIVE: {
    variant: 'inactive',
    label: 'Inactive',
    className: 'bg-gray-100 text-gray-700',
  },
  PENDING: {
    variant: 'pending',
    label: 'Pending',
    className: 'bg-warning-gradient text-warning-900',
  },
  SUSPENDED: {
    variant: 'destructive',
    label: 'Suspended',
    className: 'bg-danger-gradient text-danger-900',
  },
}

const roleConfig = {
  ADMIN: {
    variant: 'default',
    label: 'Admin',
    className: 'bg-brand-gradient text-white',
  },
  MANAGER: {
    variant: 'secondary',
    label: 'Manager',
    className: 'bg-purple-100 text-purple-800',
  },
  USER: {
    variant: 'outline',
    label: 'User',
    className: 'bg-blue-50 text-blue-700 border-blue-200',
  },
}

export function StatusBadge({ status, className, ...props }) {
  const config = statusConfig[status] || statusConfig.INACTIVE

  return (
    <Badge
      variant={config.variant}
      className={cn(config.className, className)}
      {...props}
    >
      {config.label}
    </Badge>
  )
}

export function RoleBadge({ role, className, ...props }) {
  const config = roleConfig[role] || roleConfig.USER

  return (
    <Badge
      variant={config.variant}
      className={cn(config.className, className)}
      {...props}
    >
      {config.label}
    </Badge>
  )
}
