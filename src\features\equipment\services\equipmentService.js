import prisma from "@/lib/db/prisma";

/**
 * Get paginated equipment categories list with filters
 */
export async function getEquipmentCategories(filters = {}) {
  const {
    search = "",
    status,
    page = 1,
    limit = 10,
    sortBy = "createdAt",
    sortOrder = "desc",
  } = filters;

  const skip = (page - 1) * limit;

  // Build where clause
  const where = {
    AND: [
      // Search filter
      search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { description: { contains: search, mode: "insensitive" } },
            ],
          }
        : {},
      // Status filter
      status !== undefined ? { status } : {},
    ],
  };

  // Get categories with pagination
  const [categories, total] = await Promise.all([
    prisma.equipmentCategory.findMany({
      where,
      select: {
        id: true,
        name: true,
        description: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            equipment: true,
          },
        },
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit,
    }),
    prisma.equipmentCategory.count({ where }),
  ]);

  return {
    categories,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  };
}

/**
 * Get equipment category by ID
 */
export async function getEquipmentCategoryById(id) {
  const category = await prisma.equipmentCategory.findUnique({
    where: { id: parseInt(id) },
    include: {
      equipment: {
        select: {
          id: true,
          name: true,
          code: true,
          status: true,
        },
      },
    },
  });

  if (!category) {
    throw new Error("Equipment category not found");
  }

  return category;
}

/**
 * Create new equipment category
 */
export async function createEquipmentCategory(categoryData, createdBy) {
  const { name, description, status } = categoryData;

  // Check if category already exists
  const existingCategory = await prisma.equipmentCategory.findUnique({
    where: { name },
  });

  if (existingCategory) {
    throw new Error("Equipment category with this name already exists");
  }

  // Create category
  const category = await prisma.equipmentCategory.create({
    data: {
      name,
      description,
      status,
    },
    select: {
      id: true,
      name: true,
      description: true,
      status: true,
      createdAt: true,
    },
  });

  return category;
}

/**
 * Update equipment category
 */
export async function updateEquipmentCategory(id, categoryData, updatedBy) {
  const { name, description, status } = categoryData;

  // Check if category exists
  const existingCategory = await prisma.equipmentCategory.findUnique({
    where: { id: parseInt(id) },
  });

  if (!existingCategory) {
    throw new Error("Equipment category not found");
  }

  // Check if name is being changed and if new name already exists
  if (name && name !== existingCategory.name) {
    const nameExists = await prisma.equipmentCategory.findUnique({
      where: { name },
    });

    if (nameExists) {
      throw new Error("Equipment category with this name already exists");
    }
  }

  // Update category
  const category = await prisma.equipmentCategory.update({
    where: { id: parseInt(id) },
    data: {
      ...(name && { name }),
      ...(description !== undefined && { description }),
      ...(status !== undefined && { status }),
    },
    select: {
      id: true,
      name: true,
      description: true,
      status: true,
      updatedAt: true,
    },
  });

  return category;
}

/**
 * Delete equipment category
 */
export async function deleteEquipmentCategory(id, deletedBy) {
  // Check if category exists
  const category = await prisma.equipmentCategory.findUnique({
    where: { id: parseInt(id) },
    include: {
      _count: {
        select: {
          equipment: true,
        },
      },
    },
  });

  if (!category) {
    throw new Error("Equipment category not found");
  }

  // Check if category has equipment
  if (category._count.equipment > 0) {
    throw new Error(
      "Cannot delete category that has equipment associated with it"
    );
  }

  // Delete category
  await prisma.equipmentCategory.delete({
    where: { id: parseInt(id) },
  });

  return { message: "Equipment category deleted successfully" };
}

/**
 * Get paginated equipment list with filters
 */
export async function getEquipment(filters = {}) {
  const {
    search = "",
    categoryId,
    status,
    page = 1,
    limit = 10,
    sortBy = "createdAt",
    sortOrder = "desc",
  } = filters;

  const skip = (page - 1) * limit;

  // Build where clause
  const where = {
    AND: [
      // Search filter
      search
        ? {
            OR: [
              { name: { contains: search, mode: "insensitive" } },
              { code: { contains: search, mode: "insensitive" } },
              { description: { contains: search, mode: "insensitive" } },
            ],
          }
        : {},
      // Category filter
      categoryId ? { categoryId: parseInt(categoryId) } : {},
      // Status filter
      status !== undefined ? { status } : {},
    ],
  };

  // Get equipment with pagination
  const [equipment, total] = await Promise.all([
    prisma.equipmentMaster.findMany({
      where,
      select: {
        id: true,
        name: true,
        code: true,
        subCategory: true,
        description: true,
        uom: true,
        baseRate: true,
        effectiveFrom: true,
        validUpto: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            components: true,
          },
        },
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit,
    }),
    prisma.equipmentMaster.count({ where }),
  ]);

  return {
    equipment,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  };
}

/**
 * Get equipment by ID
 */
export async function getEquipmentById(id) {
  const equipment = await prisma.equipmentMaster.findUnique({
    where: { id: parseInt(id) },
    include: {
      category: {
        select: {
          id: true,
          name: true,
        },
      },
      components: {
        select: {
          id: true,
          name: true,
          code: true,
          baseRate: true,
          status: true,
        },
      },
    },
  });

  if (!equipment) {
    throw new Error("Equipment not found");
  }

  return equipment;
}

/**
 * Create new equipment
 */
export async function createEquipment(equipmentData, createdBy) {
  const {
    name,
    code,
    categoryId,
    subCategory,
    description,
    specifications,
    uom,
    baseRate,
    regionRates,
    effectiveFrom,
    validUpto,
    costBreakup,
    status,
  } = equipmentData;

  // Check if equipment code already exists
  const existingEquipment = await prisma.equipmentMaster.findUnique({
    where: { code },
  });

  if (existingEquipment) {
    throw new Error("Equipment with this code already exists");
  }

  // Verify category exists
  const category = await prisma.equipmentCategory.findUnique({
    where: { id: parseInt(categoryId) },
  });

  if (!category) {
    throw new Error("Equipment category not found");
  }

  // Create equipment
  const equipment = await prisma.equipmentMaster.create({
    data: {
      name,
      code,
      categoryId: parseInt(categoryId),
      subCategory,
      description,
      specifications,
      uom,
      baseRate,
      regionRates,
      effectiveFrom: new Date(effectiveFrom),
      validUpto: validUpto ? new Date(validUpto) : null,
      costBreakup,
      status,
      createdBy,
    },
    include: {
      category: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  return equipment;
}

/**
 * Update equipment
 */
export async function updateEquipment(id, equipmentData, updatedBy) {
  const {
    name,
    code,
    categoryId,
    subCategory,
    description,
    specifications,
    uom,
    baseRate,
    regionRates,
    effectiveFrom,
    validUpto,
    costBreakup,
    status,
  } = equipmentData;

  // Check if equipment exists
  const existingEquipment = await prisma.equipmentMaster.findUnique({
    where: { id: parseInt(id) },
  });

  if (!existingEquipment) {
    throw new Error("Equipment not found");
  }

  // Check if code is being changed and if new code already exists
  if (code && code !== existingEquipment.code) {
    const codeExists = await prisma.equipmentMaster.findUnique({
      where: { code },
    });

    if (codeExists) {
      throw new Error("Equipment with this code already exists");
    }
  }

  // Verify category exists if being changed
  if (categoryId && categoryId !== existingEquipment.categoryId) {
    const category = await prisma.equipmentCategory.findUnique({
      where: { id: parseInt(categoryId) },
    });

    if (!category) {
      throw new Error("Equipment category not found");
    }
  }

  // Update equipment
  const equipment = await prisma.equipmentMaster.update({
    where: { id: parseInt(id) },
    data: {
      ...(name && { name }),
      ...(code && { code }),
      ...(categoryId && { categoryId: parseInt(categoryId) }),
      ...(subCategory !== undefined && { subCategory }),
      ...(description !== undefined && { description }),
      ...(specifications !== undefined && { specifications }),
      ...(uom && { uom }),
      ...(baseRate !== undefined && { baseRate }),
      ...(regionRates !== undefined && { regionRates }),
      ...(effectiveFrom && { effectiveFrom: new Date(effectiveFrom) }),
      ...(validUpto !== undefined && {
        validUpto: validUpto ? new Date(validUpto) : null,
      }),
      ...(costBreakup !== undefined && { costBreakup }),
      ...(status !== undefined && { status }),
      updatedBy,
    },
    include: {
      category: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  return equipment;
}

/**
 * Delete equipment
 */
export async function deleteEquipment(id, deletedBy) {
  // Check if equipment exists
  const equipment = await prisma.equipmentMaster.findUnique({
    where: { id: parseInt(id) },
    include: {
      _count: {
        select: {
          components: true,
        },
      },
    },
  });

  if (!equipment) {
    throw new Error("Equipment not found");
  }

  // Check if equipment has components
  if (equipment._count.components > 0) {
    throw new Error(
      "Cannot delete equipment that has components associated with it"
    );
  }

  // Delete equipment
  await prisma.equipmentMaster.delete({
    where: { id: parseInt(id) },
  });

  return { message: "Equipment deleted successfully" };
}

/**
 * Get all active equipment categories for dropdown
 */
export async function getActiveEquipmentCategories() {
  const categories = await prisma.equipmentCategory.findMany({
    where: { status: true },
    select: {
      id: true,
      name: true,
    },
    orderBy: { name: "asc" },
  });

  return categories;
}

/**
 * Get equipment statistics
 */
export async function getEquipmentStats() {
  const [
    totalEquipment,
    activeEquipment,
    inactiveEquipment,
    totalCategories,
    activeCategories,
    recentEquipment,
  ] = await Promise.all([
    prisma.equipmentMaster.count(),
    prisma.equipmentMaster.count({ where: { status: true } }),
    prisma.equipmentMaster.count({ where: { status: false } }),
    prisma.equipmentCategory.count(),
    prisma.equipmentCategory.count({ where: { status: true } }),
    prisma.equipmentMaster.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
      },
    }),
  ]);

  return {
    totalEquipment,
    activeEquipment,
    inactiveEquipment,
    totalCategories,
    activeCategories,
    recentEquipment,
  };
}
