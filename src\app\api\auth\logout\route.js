import { cookies } from 'next/headers'
import { logoutUser } from '@/features/auth/services/authService'
import { getSession } from '@/lib/auth/session'
import { successResponse, handleApiError } from '@/lib/api/response'

export async function POST(request) {
  try {
    const cookieStore = cookies()
    const token = cookieStore.get('auth-token')?.value
    
    if (token) {
      // Get session to get user ID for logging
      const session = await getSession(token)
      const userId = session?.user?.id
      
      // Logout user
      await logoutUser(token, userId)
    }
    
    // Clear auth cookie
    cookieStore.delete('auth-token')
    
    return successResponse(null, 'Logout successful')
    
  } catch (error) {
    return handleApiError(error)
  }
}
