globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/estimations/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/features/auth/hooks/useAuth.js":{"*":{"id":"(ssr)/./src/features/auth/hooks/useAuth.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/hooks/useToast.js":{"*":{"id":"(ssr)/./src/hooks/useToast.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(dashboard)/layout.js":{"*":{"id":"(ssr)/./src/app/(dashboard)/layout.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/estimations/page.js":{"*":{"id":"(ssr)/./src/app/estimations/page.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\dist\\esm\\client\\components\\static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\src\\features\\auth\\hooks\\useAuth.js":{"id":"(app-pages-browser)/./src/features/auth/hooks/useAuth.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\src\\hooks\\useToast.js":{"id":"(app-pages-browser)/./src/hooks/useToast.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\src\\app\\(dashboard)\\layout.js":{"id":"(app-pages-browser)/./src/app/(dashboard)/layout.js","name":"*","chunks":["app/(dashboard)/layout","static/chunks/app/(dashboard)/layout.js"],"async":false},"D:\\MobioProjects\\coveyor-poc\\src\\app\\estimations\\page.js":{"id":"(app-pages-browser)/./src/app/estimations/page.js","name":"*","chunks":["app/estimations/page","static/chunks/app/estimations/page.js"],"async":false}},"entryCSSFiles":{"D:\\MobioProjects\\coveyor-poc\\src\\app\\layout":["static/css/app/layout.css"],"D:\\MobioProjects\\coveyor-poc\\src\\app\\(auth)\\layout":[],"D:\\MobioProjects\\coveyor-poc\\src\\app\\(dashboard)\\layout":[],"D:\\MobioProjects\\coveyor-poc\\src\\app\\estimations\\page":[]}}