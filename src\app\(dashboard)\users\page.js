'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Plus, Search, Filter, MoreHorizontal, Edit, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, UserX } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { StatusBadge, RoleBadge } from '@/components/common/StatusBadge'
import { LoadingSpinner, LoadingTable } from '@/components/common/LoadingSpinner'
import { useUsers, useUserActions } from '@/features/users/hooks/useUsers'
import { useAuth } from '@/features/auth/hooks/useAuth'
import { PermissionGuard } from '@/features/auth/components/AuthGuard'
import { PERMISSIONS } from '@/lib/auth/permissions'

export default function UsersPage() {
  const { isManager } = useAuth()
  const [searchTerm, setSearchTerm] = useState('')
  const { users, loading, error, pagination, filters, updateFilters, updatePagination, refresh } = useUsers()
  const { activateUser, deactivateUser, deleteUser, loading: actionLoading } = useUserActions()

  const handleSearch = (e) => {
    const value = e.target.value
    setSearchTerm(value)
    updateFilters({ search: value })
  }

  const handleStatusFilter = (status) => {
    updateFilters({ status: status === filters.status ? '' : status })
  }

  const handleRoleFilter = (role) => {
    updateFilters({ role: role === filters.role ? '' : role })
  }

  const handleUserAction = async (action, userId) => {
    let result
    switch (action) {
      case 'activate':
        result = await activateUser(userId)
        break
      case 'deactivate':
        result = await deactivateUser(userId)
        break
      case 'delete':
        if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
          result = await deleteUser(userId)
        }
        break
    }
    
    if (result?.success) {
      refresh()
    }
  }

  if (!isManager) {
    return (
      <div className="p-6">
        <Card className="card-vibrant">
          <CardContent className="p-6 text-center">
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-gray-600">You don't have permission to view users.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Users</h1>
          <p className="text-gray-600">Manage your team members and their permissions</p>
        </div>
        <PermissionGuard permission={PERMISSIONS.USER_CREATE}>
          <Link href="/users/create">
            <Button variant="gradient" size="lg">
              <Plus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </Link>
        </PermissionGuard>
      </div>

      {/* Filters */}
      <Card className="card-vibrant">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search users..."
                value={searchTerm}
                onChange={handleSearch}
                className="pl-10"
              />
            </div>

            {/* Status filters */}
            <div className="flex gap-2">
              <Button
                variant={filters.status === 'ACTIVE' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleStatusFilter('ACTIVE')}
              >
                Active
              </Button>
              <Button
                variant={filters.status === 'INACTIVE' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleStatusFilter('INACTIVE')}
              >
                Inactive
              </Button>
            </div>

            {/* Role filters */}
            <div className="flex gap-2">
              <Button
                variant={filters.role === 'ADMIN' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleRoleFilter('ADMIN')}
              >
                Admin
              </Button>
              <Button
                variant={filters.role === 'MANAGER' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleRoleFilter('MANAGER')}
              >
                Manager
              </Button>
              <Button
                variant={filters.role === 'USER' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleRoleFilter('USER')}
              >
                User
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle>Team Members</CardTitle>
          <CardDescription>
            {pagination.total} total users
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <LoadingTable rows={5} cols={5} />
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-600">{error}</p>
              <Button variant="outline" onClick={refresh} className="mt-2">
                Try Again
              </Button>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No users found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {users.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-brand-gradient rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-white">
                        {user.firstName?.[0]}{user.lastName?.[0]}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {user.firstName} {user.lastName}
                      </h3>
                      <p className="text-sm text-gray-500">{user.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <RoleBadge role={user.role} />
                    <StatusBadge status={user.status} />
                    
                    <div className="flex items-center space-x-1">
                      <Link href={`/users/${user.id}`}>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                      
                      {user.status === 'ACTIVE' ? (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleUserAction('deactivate', user.id)}
                          disabled={actionLoading}
                        >
                          <UserX className="h-4 w-4" />
                        </Button>
                      ) : (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleUserAction('activate', user.id)}
                          disabled={actionLoading}
                        >
                          <UserCheck className="h-4 w-4" />
                        </Button>
                      )}
                      
                      <PermissionGuard permission={PERMISSIONS.USER_DELETE}>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleUserAction('delete', user.id)}
                          disabled={actionLoading}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </PermissionGuard>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <p className="text-sm text-gray-500">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total} results
              </p>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updatePagination({ page: pagination.page - 1 })}
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => updatePagination({ page: pagination.page + 1 })}
                  disabled={pagination.page === pagination.totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
