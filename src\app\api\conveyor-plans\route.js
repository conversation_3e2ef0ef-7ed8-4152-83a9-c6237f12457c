import { getCurrentUser } from '@/lib/auth/session'
import { getConveyorPlans, createConveyorPlan } from '@/features/conveyor/services/conveyorService'
import { conveyorPlanFilterSchema, createConveyorPlanSchema } from '@/features/conveyor/validation/conveyorSchemas'
import { successResponse, unauthorizedResponse, forbiddenResponse, handleApiError, paginatedResponse } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view conveyor plans')
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const filters = conveyorPlanFilterSchema.parse({
      search: searchParams.get('search') || '',
      conveyorType: searchParams.get('conveyorType') || undefined,
      loadType: searchParams.get('loadType') || undefined,
      status: searchParams.get('status') || undefined,
      region: searchParams.get('region') || undefined,
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: searchParams.get('sortOrder') || 'desc',
    })

    // Get plans
    const result = await getConveyorPlans(filters)
    
    return paginatedResponse(
      result.plans,
      result.pagination,
      'Conveyor plans retrieved successfully'
    )
    
  } catch (error) {
    return handleApiError(error)
  }
}

export async function POST(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_CREATE)) {
      return forbiddenResponse('Insufficient permissions to create conveyor plans')
    }

    const body = await request.json()
    
    // Validate request body
    const validatedData = createConveyorPlanSchema.parse(body)
    
    // Create plan
    const newPlan = await createConveyorPlan(validatedData, user.id)
    
    return successResponse(newPlan, 'Conveyor plan created successfully', null, 201)
    
  } catch (error) {
    return handleApiError(error)
  }
}
