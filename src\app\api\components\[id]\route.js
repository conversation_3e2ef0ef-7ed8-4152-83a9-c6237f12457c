import { getCurrentUser } from '@/lib/auth/session'
import { getComponentById, updateComponent, deleteComponent } from '@/features/components/services/componentService'
import { updateComponentSchema } from '@/features/components/validation/componentSchemas'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view component')
    }

    const componentId = params.id
    const component = await getComponentById(componentId)

    return successResponse(component, 'Component retrieved successfully')

  } catch (error) {
    if (error.message === 'Component not found') {
      return notFoundResponse('Component not found')
    }
    return handleApiError(error)
  }
}

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_UPDATE)) {
      return forbiddenResponse('Insufficient permissions to update component')
    }

    const componentId = params.id
    const body = await request.json()
    
    // Validate request body
    const validatedData = updateComponentSchema.parse(body)
    
    // Update component
    const updatedComponent = await updateComponent(componentId, validatedData, user.id)
    
    return successResponse(updatedComponent, 'Component updated successfully')
    
  } catch (error) {
    if (error.message === 'Component not found') {
      return notFoundResponse('Component not found')
    }
    return handleApiError(error)
  }
}

export async function DELETE(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_DELETE)) {
      return forbiddenResponse('Insufficient permissions to delete component')
    }

    const componentId = params.id
    
    // Delete component
    const result = await deleteComponent(componentId, user.id)
    
    return successResponse(result, 'Component deleted successfully')
    
  } catch (error) {
    if (error.message === 'Component not found') {
      return notFoundResponse('Component not found')
    }
    return handleApiError(error)
  }
}
