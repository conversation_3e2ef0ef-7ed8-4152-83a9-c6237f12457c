'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  Package2
} from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { useToast } from '@/hooks/useToast'
import { componentCategories } from '@/features/components/validation/componentSchemas'

export default function ComponentList({ onCreateNew, onEdit, onView }) {
  const [components, setComponents] = useState([])
  const [equipment, setEquipment] = useState([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    equipmentId: '',
    status: '',
    page: 1,
    limit: 10
  })
  const [pagination, setPagination] = useState({})

  const { apiCall } = useApi()
  const { showToast } = useToast()

  // Fetch components data
  const fetchComponents = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value)
      })

      const response = await apiCall(`/api/components?${params}`)
      
      if (response.success) {
        setComponents(response.data)
        setPagination(response.pagination || {})
      }
    } catch (error) {
      showToast('Failed to fetch components', 'error')
    } finally {
      setLoading(false)
    }
  }

  // Fetch equipment for filter
  const fetchEquipment = async () => {
    try {
      const response = await apiCall('/api/equipment?limit=100')
      if (response.success) {
        setEquipment(response.data)
      }
    } catch (error) {
      console.error('Failed to fetch equipment:', error)
    }
  }

  useEffect(() => {
    fetchComponents()
  }, [filters])

  useEffect(() => {
    fetchEquipment()
  }, [])

  const handleSearch = (value) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }))
  }

  const handleCategoryFilter = (category) => {
    setFilters(prev => ({ ...prev, category, page: 1 }))
  }

  const handleEquipmentFilter = (equipmentId) => {
    setFilters(prev => ({ ...prev, equipmentId, page: 1 }))
  }

  const handleStatusFilter = (status) => {
    setFilters(prev => ({ ...prev, status, page: 1 }))
  }

  const handleDelete = async (id) => {
    if (!confirm('Are you sure you want to delete this component?')) return

    try {
      const response = await apiCall(`/api/components/${id}`, 'DELETE')
      
      if (response.success) {
        showToast('Component deleted successfully', 'success')
        fetchComponents()
      }
    } catch (error) {
      showToast('Failed to delete component', 'error')
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-IN')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Component Master</h1>
          <p className="text-gray-600 mt-1">Manage raw materials and components</p>
        </div>
        <Button onClick={onCreateNew} className="btn-gradient-primary">
          <Plus className="w-4 h-4 mr-2" />
          Add Component
        </Button>
      </div>

      {/* Filters */}
      <Card className="card-vibrant">
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search components..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <select
              value={filters.category}
              onChange={(e) => handleCategoryFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Categories</option>
              {componentCategories.map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            <select
              value={filters.equipmentId}
              onChange={(e) => handleEquipmentFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Equipment</option>
              {equipment.map(item => (
                <option key={item.id} value={item.id}>
                  {item.name}
                </option>
              ))}
            </select>

            <select
              value={filters.status}
              onChange={(e) => handleStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Components Table */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package2 className="w-5 h-5 mr-2" />
            Components List
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Code</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Equipment</TableHead>
                    <TableHead>UOM</TableHead>
                    <TableHead>Base Rate</TableHead>
                    <TableHead>Effective From</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {components.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-mono text-sm">{item.code}</TableCell>
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{item.category}</Badge>
                      </TableCell>
                      <TableCell>{item.equipment?.name}</TableCell>
                      <TableCell>{item.uom}</TableCell>
                      <TableCell>{formatCurrency(item.baseRate)}</TableCell>
                      <TableCell>{formatDate(item.rateEffectiveFrom)}</TableCell>
                      <TableCell>
                        <Badge variant={item.status ? 'success' : 'secondary'}>
                          {item.status ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onView(item)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => onEdit(item)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleDelete(item.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {components.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No components found
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
