"use client";

import { useEffect, useState } from "react";
import {
  Users,
  UserPlus,
  Activity,
  Shield,
  Package,
  Package2,
  FileText,
  Calculator,
  Settings2,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/features/auth/hooks/useAuth";
import { LoadingSpinner } from "@/components/common/LoadingSpinner";
import Link from "next/link";

export default function DashboardPage() {
  const { user, isManager } = useAuth();
  const [stats, setStats] = useState(null);
  const [conveyorStats, setConveyorStats] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch user stats for managers
        if (isManager) {
          const userResponse = await fetch("/api/users/stats", {
            credentials: "include",
          });
          if (userResponse.ok) {
            const userData = await userResponse.json();
            setStats(userData.data);
          }
        }

        // Fetch conveyor system stats for all users
        const conveyorResponse = await fetch("/api/dashboard/conveyor-stats", {
          credentials: "include",
        });
        if (conveyorResponse.ok) {
          const conveyorData = await conveyorResponse.json();
          setConveyorStats(conveyorData.data);
        }
      } catch (error) {
        console.error("Failed to fetch stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [isManager]);

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div className="bg-brand-gradient rounded-xl p-6 text-white">
        <h1 className="text-3xl font-bold mb-2">
          Welcome back, {user?.firstName}! 👋
        </h1>
        <p className="text-white/80">
          Here's what's happening with your team today.
        </p>
      </div>

      {/* Stats Cards - Only for Managers and Admins */}
      {isManager && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {loading ? (
            Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} className="card-vibrant">
                <CardContent className="p-6">
                  <LoadingSpinner />
                </CardContent>
              </Card>
            ))
          ) : (
            <>
              <Card className="card-vibrant">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Users
                  </CardTitle>
                  <Users className="h-4 w-4 text-brand-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-brand-700">
                    {stats?.total || 0}
                  </div>
                  <p className="text-xs text-gray-600">
                    +{stats?.recent || 0} this month
                  </p>
                </CardContent>
              </Card>

              <Card className="card-vibrant">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Active Users
                  </CardTitle>
                  <Activity className="h-4 w-4 text-success-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-success-700">
                    {stats?.byStatus?.active || 0}
                  </div>
                  <p className="text-xs text-gray-600">
                    {stats?.total
                      ? Math.round((stats.byStatus.active / stats.total) * 100)
                      : 0}
                    % of total
                  </p>
                </CardContent>
              </Card>

              <Card className="card-vibrant">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Managers
                  </CardTitle>
                  <Shield className="h-4 w-4 text-purple-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-700">
                    {stats?.byRole?.manager || 0}
                  </div>
                  <p className="text-xs text-gray-600">
                    Including {stats?.byRole?.admin || 0} admins
                  </p>
                </CardContent>
              </Card>

              <Card className="card-vibrant">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending</CardTitle>
                  <UserPlus className="h-4 w-4 text-warning-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-warning-700">
                    {stats?.byStatus?.pending || 0}
                  </div>
                  <p className="text-xs text-gray-600">Awaiting activation</p>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      )}

      {/* Conveyor System Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {loading ? (
          Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="card-vibrant">
              <CardContent className="p-6">
                <LoadingSpinner />
              </CardContent>
            </Card>
          ))
        ) : (
          <>
            <Card className="card-vibrant">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Equipment</CardTitle>
                <Package className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-700">
                  {conveyorStats?.totalEquipment || 0}
                </div>
                <p className="text-xs text-gray-600">
                  {conveyorStats?.activeEquipment || 0} active
                </p>
              </CardContent>
            </Card>

            <Card className="card-vibrant">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Components
                </CardTitle>
                <Package2 className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-700">
                  {conveyorStats?.totalComponents || 0}
                </div>
                <p className="text-xs text-gray-600">
                  {conveyorStats?.activeComponents || 0} active
                </p>
              </CardContent>
            </Card>

            <Card className="card-vibrant">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Plans</CardTitle>
                <FileText className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-700">
                  {conveyorStats?.totalPlans || 0}
                </div>
                <p className="text-xs text-gray-600">
                  {conveyorStats?.approvedPlans || 0} approved
                </p>
              </CardContent>
            </Card>

            <Card className="card-vibrant">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Estimations
                </CardTitle>
                <Calculator className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-700">
                  {conveyorStats?.totalEstimations || 0}
                </div>
                <p className="text-xs text-gray-600">
                  {conveyorStats?.finalEstimations || 0} finalized
                </p>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks you can perform</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link href="/equipment">
              <Button variant="outline" className="w-full justify-start">
                <Package className="mr-2 h-4 w-4" />
                Manage Equipment
              </Button>
            </Link>
            <Link href="/components">
              <Button variant="outline" className="w-full justify-start">
                <Package2 className="mr-2 h-4 w-4" />
                Manage Components
              </Button>
            </Link>
            <Link href="/conveyor-plans">
              <Button variant="outline" className="w-full justify-start">
                <FileText className="mr-2 h-4 w-4" />
                Create Conveyor Plan
              </Button>
            </Link>
            <Link href="/estimations">
              <Button variant="outline" className="w-full justify-start">
                <Calculator className="mr-2 h-4 w-4" />
                Generate Estimation
              </Button>
            </Link>
            {isManager && (
              <>
                <Link href="/users">
                  <Button variant="outline" className="w-full justify-start">
                    <Users className="mr-2 h-4 w-4" />
                    Manage Users
                  </Button>
                </Link>
                <Link href="/equipment/categories">
                  <Button variant="outline" className="w-full justify-start">
                    <Settings2 className="mr-2 h-4 w-4" />
                    Equipment Categories
                  </Button>
                </Link>
              </>
            )}
          </CardContent>
        </Card>

        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest system activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">System running smoothly</p>
                  <p className="text-xs text-gray-500">
                    All services operational
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-brand-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Welcome to Coveyor POC</p>
                  <p className="text-xs text-gray-500">
                    Your internal tool is ready
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
