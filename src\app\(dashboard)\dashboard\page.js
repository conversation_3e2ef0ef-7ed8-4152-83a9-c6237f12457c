'use client'

import { useEffect, useState } from 'react'
import { Users, UserPlus, Activity, Shield } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/features/auth/hooks/useAuth'
import { LoadingSpinner } from '@/components/common/LoadingSpinner'
import Link from 'next/link'

export default function DashboardPage() {
  const { user, isManager } = useAuth()
  const [stats, setStats] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/users/stats', {
          credentials: 'include',
        })
        if (response.ok) {
          const data = await response.json()
          setStats(data.data)
        }
      } catch (error) {
        console.error('Failed to fetch stats:', error)
      } finally {
        setLoading(false)
      }
    }

    if (isManager) {
      fetchStats()
    } else {
      setLoading(false)
    }
  }, [isManager])

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div className="bg-brand-gradient rounded-xl p-6 text-white">
        <h1 className="text-3xl font-bold mb-2">
          Welcome back, {user?.firstName}! 👋
        </h1>
        <p className="text-white/80">
          Here's what's happening with your team today.
        </p>
      </div>

      {/* Stats Cards - Only for Managers and Admins */}
      {isManager && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {loading ? (
            Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} className="card-vibrant">
                <CardContent className="p-6">
                  <LoadingSpinner />
                </CardContent>
              </Card>
            ))
          ) : (
            <>
              <Card className="card-vibrant">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                  <Users className="h-4 w-4 text-brand-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-brand-700">
                    {stats?.total || 0}
                  </div>
                  <p className="text-xs text-gray-600">
                    +{stats?.recent || 0} this month
                  </p>
                </CardContent>
              </Card>

              <Card className="card-vibrant">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                  <Activity className="h-4 w-4 text-success-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-success-700">
                    {stats?.byStatus?.active || 0}
                  </div>
                  <p className="text-xs text-gray-600">
                    {stats?.total ? Math.round((stats.byStatus.active / stats.total) * 100) : 0}% of total
                  </p>
                </CardContent>
              </Card>

              <Card className="card-vibrant">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Managers</CardTitle>
                  <Shield className="h-4 w-4 text-purple-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-700">
                    {stats?.byRole?.manager || 0}
                  </div>
                  <p className="text-xs text-gray-600">
                    Including {stats?.byRole?.admin || 0} admins
                  </p>
                </CardContent>
              </Card>

              <Card className="card-vibrant">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending</CardTitle>
                  <UserPlus className="h-4 w-4 text-warning-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-warning-700">
                    {stats?.byStatus?.pending || 0}
                  </div>
                  <p className="text-xs text-gray-600">
                    Awaiting activation
                  </p>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks you can perform
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {isManager && (
              <>
                <Link href="/users">
                  <Button variant="outline" className="w-full justify-start">
                    <Users className="mr-2 h-4 w-4" />
                    Manage Users
                  </Button>
                </Link>
                <Link href="/users/create">
                  <Button variant="outline" className="w-full justify-start">
                    <UserPlus className="mr-2 h-4 w-4" />
                    Add New User
                  </Button>
                </Link>
              </>
            )}
            <Button variant="outline" className="w-full justify-start">
              <Shield className="mr-2 h-4 w-4" />
              Update Profile
            </Button>
          </CardContent>
        </Card>

        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest system activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">System running smoothly</p>
                  <p className="text-xs text-gray-500">All services operational</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-brand-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Welcome to Coveyor POC</p>
                  <p className="text-xs text-gray-500">Your internal tool is ready</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
