"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/conveyor-stats/route";
exports.ids = ["app/api/dashboard/conveyor-stats/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute&page=%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute&page=%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_MobioProjects_coveyor_poc_src_app_api_dashboard_conveyor_stats_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/dashboard/conveyor-stats/route.js */ \"(rsc)/./src/app/api/dashboard/conveyor-stats/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/conveyor-stats/route\",\n        pathname: \"/api/dashboard/conveyor-stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/conveyor-stats/route\"\n    },\n    resolvedPagePath: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\api\\\\dashboard\\\\conveyor-stats\\\\route.js\",\n    nextConfigOutput,\n    userland: D_MobioProjects_coveyor_poc_src_app_api_dashboard_conveyor_stats_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/dashboard/conveyor-stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute&page=%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/dashboard/conveyor-stats/route.js":
/*!*******************************************************!*\
  !*** ./src/app/api/dashboard/conveyor-stats/route.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_session__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth/session */ \"(rsc)/./src/lib/auth/session.js\");\n/* harmony import */ var _features_equipment_services_equipmentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/equipment/services/equipmentService */ \"(rsc)/./src/features/equipment/services/equipmentService.js\");\n/* harmony import */ var _features_components_services_componentService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/components/services/componentService */ \"(rsc)/./src/features/components/services/componentService.js\");\n/* harmony import */ var _features_conveyor_services_conveyorService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/conveyor/services/conveyorService */ \"(rsc)/./src/features/conveyor/services/conveyorService.js\");\n/* harmony import */ var _features_estimation_services_estimationService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/estimation/services/estimationService */ \"(rsc)/./src/features/estimation/services/estimationService.js\");\n/* harmony import */ var _lib_api_response__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/response */ \"(rsc)/./src/lib/api/response.js\");\n\n\n\n\n\n\nasync function GET(request) {\n    try {\n        const user = await (0,_lib_auth_session__WEBPACK_IMPORTED_MODULE_0__.getCurrentUser)();\n        if (!user) {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_5__.unauthorizedResponse)(\"Authentication required\");\n        }\n        // Fetch stats from all modules\n        const [equipmentStats, componentStats, planStats, estimationStats] = await Promise.all([\n            (0,_features_equipment_services_equipmentService__WEBPACK_IMPORTED_MODULE_1__.getEquipmentStats)(),\n            (0,_features_components_services_componentService__WEBPACK_IMPORTED_MODULE_2__.getComponentStats)(),\n            (0,_features_conveyor_services_conveyorService__WEBPACK_IMPORTED_MODULE_3__.getConveyorPlanStats)(),\n            (0,_features_estimation_services_estimationService__WEBPACK_IMPORTED_MODULE_4__.getEstimationStats)()\n        ]);\n        // Combine all stats\n        const conveyorStats = {\n            // Equipment stats\n            totalEquipment: equipmentStats.totalEquipment,\n            activeEquipment: equipmentStats.activeEquipment,\n            inactiveEquipment: equipmentStats.inactiveEquipment,\n            totalCategories: equipmentStats.totalCategories,\n            activeCategories: equipmentStats.activeCategories,\n            recentEquipment: equipmentStats.recentEquipment,\n            // Component stats\n            totalComponents: componentStats.totalComponents,\n            activeComponents: componentStats.activeComponents,\n            inactiveComponents: componentStats.inactiveComponents,\n            componentCategoriesCount: componentStats.categoriesCount,\n            recentComponents: componentStats.recentComponents,\n            // Plan stats\n            totalPlans: planStats.totalPlans,\n            draftPlans: planStats.draftPlans,\n            submittedPlans: planStats.submittedPlans,\n            approvedPlans: planStats.approvedPlans,\n            rejectedPlans: planStats.rejectedPlans,\n            recentPlans: planStats.recentPlans,\n            // Estimation stats\n            totalEstimations: estimationStats.totalEstimations,\n            draftEstimations: estimationStats.draftEstimations,\n            finalEstimations: estimationStats.finalEstimations,\n            sentEstimations: estimationStats.sentEstimations,\n            approvedEstimations: estimationStats.approvedEstimations,\n            recentEstimations: estimationStats.recentEstimations\n        };\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_5__.successResponse)(conveyorStats, \"Conveyor system statistics retrieved successfully\");\n    } catch (error) {\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_5__.handleApiError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/dashboard/conveyor-stats/route.js\n");

/***/ }),

/***/ "(rsc)/./src/features/components/services/componentService.js":
/*!**************************************************************!*\
  !*** ./src/features/components/services/componentService.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createComponent: () => (/* binding */ createComponent),\n/* harmony export */   deleteComponent: () => (/* binding */ deleteComponent),\n/* harmony export */   getComponentById: () => (/* binding */ getComponentById),\n/* harmony export */   getComponentStats: () => (/* binding */ getComponentStats),\n/* harmony export */   getComponents: () => (/* binding */ getComponents),\n/* harmony export */   getComponentsByEquipment: () => (/* binding */ getComponentsByEquipment),\n/* harmony export */   updateComponent: () => (/* binding */ updateComponent)\n/* harmony export */ });\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n\n/**\n * Get paginated components list with filters\n */ async function getComponents(filters = {}) {\n    const { search = \"\", category, equipmentId, status, page = 1, limit = 10, sortBy = \"createdAt\", sortOrder = \"desc\" } = filters;\n    const skip = (page - 1) * limit;\n    // Build where clause\n    const where = {\n        AND: [\n            // Search filter\n            search ? {\n                OR: [\n                    {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        code: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        description: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        vendorInfo: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                ]\n            } : {},\n            // Category filter\n            category ? {\n                category: {\n                    contains: category,\n                    mode: \"insensitive\"\n                }\n            } : {},\n            // Equipment filter\n            equipmentId ? {\n                equipmentId: parseInt(equipmentId)\n            } : {},\n            // Status filter\n            status !== undefined ? {\n                status\n            } : {}\n        ]\n    };\n    // Get components with pagination\n    const [components, total] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findMany({\n            where,\n            select: {\n                id: true,\n                name: true,\n                code: true,\n                category: true,\n                description: true,\n                uom: true,\n                baseRate: true,\n                rateEffectiveFrom: true,\n                validUpto: true,\n                vendorInfo: true,\n                status: true,\n                createdAt: true,\n                updatedAt: true,\n                equipment: {\n                    select: {\n                        id: true,\n                        name: true,\n                        code: true\n                    }\n                }\n            },\n            orderBy: {\n                [sortBy]: sortOrder\n            },\n            skip,\n            take: limit\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.count({\n            where\n        })\n    ]);\n    return {\n        components,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit)\n        }\n    };\n}\n/**\n * Get component by ID\n */ async function getComponentById(id) {\n    const component = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            equipment: {\n                select: {\n                    id: true,\n                    name: true,\n                    code: true,\n                    category: {\n                        select: {\n                            id: true,\n                            name: true\n                        }\n                    }\n                }\n            }\n        }\n    });\n    if (!component) {\n        throw new Error(\"Component not found\");\n    }\n    return component;\n}\n/**\n * Create new component\n */ async function createComponent(componentData, createdBy) {\n    const { name, code, category, equipmentId, description, uom, baseRate, rateEffectiveFrom, validUpto, vendorInfo, status } = componentData;\n    // Check if component code already exists\n    const existingComponent = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findUnique({\n        where: {\n            code\n        }\n    });\n    if (existingComponent) {\n        throw new Error(\"Component with this code already exists\");\n    }\n    // Verify equipment exists\n    const equipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findUnique({\n        where: {\n            id: parseInt(equipmentId)\n        }\n    });\n    if (!equipment) {\n        throw new Error(\"Equipment not found\");\n    }\n    // Create component\n    const component = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.create({\n        data: {\n            name,\n            code,\n            category,\n            equipmentId: parseInt(equipmentId),\n            description,\n            uom,\n            baseRate,\n            rateEffectiveFrom: new Date(rateEffectiveFrom),\n            validUpto: validUpto ? new Date(validUpto) : null,\n            vendorInfo,\n            status,\n            createdBy\n        },\n        include: {\n            equipment: {\n                select: {\n                    id: true,\n                    name: true,\n                    code: true\n                }\n            }\n        }\n    });\n    return component;\n}\n/**\n * Update component\n */ async function updateComponent(id, componentData, updatedBy) {\n    const { name, code, category, equipmentId, description, uom, baseRate, rateEffectiveFrom, validUpto, vendorInfo, status } = componentData;\n    // Check if component exists\n    const existingComponent = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!existingComponent) {\n        throw new Error(\"Component not found\");\n    }\n    // Check if code is being changed and if new code already exists\n    if (code && code !== existingComponent.code) {\n        const codeExists = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findUnique({\n            where: {\n                code\n            }\n        });\n        if (codeExists) {\n            throw new Error(\"Component with this code already exists\");\n        }\n    }\n    // Verify equipment exists if being changed\n    if (equipmentId && equipmentId !== existingComponent.equipmentId) {\n        const equipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findUnique({\n            where: {\n                id: parseInt(equipmentId)\n            }\n        });\n        if (!equipment) {\n            throw new Error(\"Equipment not found\");\n        }\n    }\n    // Update component\n    const component = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            ...name && {\n                name\n            },\n            ...code && {\n                code\n            },\n            ...category && {\n                category\n            },\n            ...equipmentId && {\n                equipmentId: parseInt(equipmentId)\n            },\n            ...description !== undefined && {\n                description\n            },\n            ...uom && {\n                uom\n            },\n            ...baseRate !== undefined && {\n                baseRate\n            },\n            ...rateEffectiveFrom && {\n                rateEffectiveFrom: new Date(rateEffectiveFrom)\n            },\n            ...validUpto !== undefined && {\n                validUpto: validUpto ? new Date(validUpto) : null\n            },\n            ...vendorInfo !== undefined && {\n                vendorInfo\n            },\n            ...status !== undefined && {\n                status\n            },\n            updatedBy\n        },\n        include: {\n            equipment: {\n                select: {\n                    id: true,\n                    name: true,\n                    code: true\n                }\n            }\n        }\n    });\n    return component;\n}\n/**\n * Delete component\n */ async function deleteComponent(id, deletedBy) {\n    // Check if component exists\n    const component = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!component) {\n        throw new Error(\"Component not found\");\n    }\n    // Delete component\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.delete({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    return {\n        message: \"Component deleted successfully\"\n    };\n}\n/**\n * Get components by equipment ID\n */ async function getComponentsByEquipment(equipmentId) {\n    const components = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findMany({\n        where: {\n            equipmentId: parseInt(equipmentId),\n            status: true\n        },\n        select: {\n            id: true,\n            name: true,\n            code: true,\n            category: true,\n            baseRate: true,\n            uom: true\n        },\n        orderBy: {\n            name: \"asc\"\n        }\n    });\n    return components;\n}\n/**\n * Get component statistics\n */ async function getComponentStats() {\n    const [totalComponents, activeComponents, inactiveComponents, categoriesCount, recentComponents] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.count(),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.count({\n            where: {\n                status: true\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.count({\n            where: {\n                status: false\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.groupBy({\n            by: [\n                \"category\"\n            ],\n            _count: {\n                category: true\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.count({\n            where: {\n                createdAt: {\n                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                }\n            }\n        })\n    ]);\n    return {\n        totalComponents,\n        activeComponents,\n        inactiveComponents,\n        categoriesCount: categoriesCount.length,\n        recentComponents\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/components/services/componentService.js\n");

/***/ }),

/***/ "(rsc)/./src/features/conveyor/services/conveyorService.js":
/*!***********************************************************!*\
  !*** ./src/features/conveyor/services/conveyorService.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   approveConveyorPlan: () => (/* binding */ approveConveyorPlan),\n/* harmony export */   createConveyorPlan: () => (/* binding */ createConveyorPlan),\n/* harmony export */   deleteConveyorPlan: () => (/* binding */ deleteConveyorPlan),\n/* harmony export */   getConveyorPlanById: () => (/* binding */ getConveyorPlanById),\n/* harmony export */   getConveyorPlanStats: () => (/* binding */ getConveyorPlanStats),\n/* harmony export */   getConveyorPlans: () => (/* binding */ getConveyorPlans),\n/* harmony export */   rejectConveyorPlan: () => (/* binding */ rejectConveyorPlan),\n/* harmony export */   submitConveyorPlan: () => (/* binding */ submitConveyorPlan),\n/* harmony export */   updateConveyorPlan: () => (/* binding */ updateConveyorPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n\n/**\n * Get paginated conveyor plans list with filters\n */ async function getConveyorPlans(filters = {}) {\n    const { search = \"\", conveyorType, loadType, status, region, page = 1, limit = 10, sortBy = \"createdAt\", sortOrder = \"desc\" } = filters;\n    const skip = (page - 1) * limit;\n    // Build where clause\n    const where = {\n        AND: [\n            // Search filter\n            search ? {\n                OR: [\n                    {\n                        planName: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        conveyorType: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        siteConditions: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        specialRequirements: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                ]\n            } : {},\n            // Conveyor type filter\n            conveyorType ? {\n                conveyorType: {\n                    contains: conveyorType,\n                    mode: \"insensitive\"\n                }\n            } : {},\n            // Load type filter\n            loadType ? {\n                loadType\n            } : {},\n            // Status filter\n            status ? {\n                status\n            } : {},\n            // Region filter\n            region ? {\n                region: {\n                    contains: region,\n                    mode: \"insensitive\"\n                }\n            } : {}\n        ]\n    };\n    // Get plans with pagination\n    const [plans, total] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findMany({\n            where,\n            select: {\n                id: true,\n                planName: true,\n                conveyorType: true,\n                totalLengthM: true,\n                loadType: true,\n                capacityTph: true,\n                inclinationAngle: true,\n                driveType: true,\n                environment: true,\n                region: true,\n                status: true,\n                createdAt: true,\n                updatedAt: true,\n                _count: {\n                    select: {\n                        estimationSheets: true\n                    }\n                }\n            },\n            orderBy: {\n                [sortBy]: sortOrder\n            },\n            skip,\n            take: limit\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where\n        })\n    ]);\n    return {\n        plans,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit)\n        }\n    };\n}\n/**\n * Get conveyor plan by ID\n */ async function getConveyorPlanById(id) {\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            estimationSheets: {\n                select: {\n                    id: true,\n                    estimationVersion: true,\n                    totalCost: true,\n                    status: true,\n                    generatedAt: true\n                },\n                orderBy: {\n                    generatedAt: \"desc\"\n                }\n            }\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    return plan;\n}\n/**\n * Create new conveyor plan\n */ async function createConveyorPlan(planData, createdBy) {\n    const { planName, conveyorType, totalLengthM, loadType, capacityTph, inclinationAngle, driveType, environment, region, siteConditions, specialRequirements, status } = planData;\n    // Create plan\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.create({\n        data: {\n            planName,\n            conveyorType,\n            totalLengthM,\n            loadType,\n            capacityTph,\n            inclinationAngle,\n            driveType,\n            environment,\n            region,\n            siteConditions,\n            specialRequirements,\n            status,\n            createdBy\n        }\n    });\n    return plan;\n}\n/**\n * Update conveyor plan\n */ async function updateConveyorPlan(id, planData, updatedBy) {\n    const { planName, conveyorType, totalLengthM, loadType, capacityTph, inclinationAngle, driveType, environment, region, siteConditions, specialRequirements, status } = planData;\n    // Check if plan exists\n    const existingPlan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!existingPlan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    // Update plan\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            ...planName && {\n                planName\n            },\n            ...conveyorType && {\n                conveyorType\n            },\n            ...totalLengthM !== undefined && {\n                totalLengthM\n            },\n            ...loadType && {\n                loadType\n            },\n            ...capacityTph !== undefined && {\n                capacityTph\n            },\n            ...inclinationAngle !== undefined && {\n                inclinationAngle\n            },\n            ...driveType !== undefined && {\n                driveType\n            },\n            ...environment !== undefined && {\n                environment\n            },\n            ...region !== undefined && {\n                region\n            },\n            ...siteConditions !== undefined && {\n                siteConditions\n            },\n            ...specialRequirements !== undefined && {\n                specialRequirements\n            },\n            ...status && {\n                status\n            },\n            updatedBy\n        }\n    });\n    return plan;\n}\n/**\n * Delete conveyor plan\n */ async function deleteConveyorPlan(id, deletedBy) {\n    // Check if plan exists\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            _count: {\n                select: {\n                    estimationSheets: true\n                }\n            }\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    // Check if plan has estimation sheets\n    if (plan._count.estimationSheets > 0) {\n        throw new Error(\"Cannot delete plan that has estimation sheets associated with it\");\n    }\n    // Delete plan\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.delete({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    return {\n        message: \"Conveyor plan deleted successfully\"\n    };\n}\n/**\n * Submit conveyor plan for approval\n */ async function submitConveyorPlan(id, submittedBy) {\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    if (plan.status !== \"Draft\") {\n        throw new Error(\"Only draft plans can be submitted\");\n    }\n    const updatedPlan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            status: \"Submitted\",\n            updatedBy: submittedBy\n        }\n    });\n    return updatedPlan;\n}\n/**\n * Approve conveyor plan\n */ async function approveConveyorPlan(id, approvedBy) {\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    if (plan.status !== \"Submitted\") {\n        throw new Error(\"Only submitted plans can be approved\");\n    }\n    const updatedPlan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            status: \"Approved\",\n            updatedBy: approvedBy\n        }\n    });\n    return updatedPlan;\n}\n/**\n * Reject conveyor plan\n */ async function rejectConveyorPlan(id, rejectedBy) {\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    if (plan.status !== \"Submitted\") {\n        throw new Error(\"Only submitted plans can be rejected\");\n    }\n    const updatedPlan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            status: \"Rejected\",\n            updatedBy: rejectedBy\n        }\n    });\n    return updatedPlan;\n}\n/**\n * Get conveyor plan statistics\n */ async function getConveyorPlanStats() {\n    const [totalPlans, draftPlans, submittedPlans, approvedPlans, rejectedPlans, recentPlans] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count(),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where: {\n                status: \"Draft\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where: {\n                status: \"Submitted\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where: {\n                status: \"Approved\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where: {\n                status: \"Rejected\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.count({\n            where: {\n                createdAt: {\n                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                }\n            }\n        })\n    ]);\n    return {\n        totalPlans,\n        draftPlans,\n        submittedPlans,\n        approvedPlans,\n        rejectedPlans,\n        recentPlans\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/conveyor/services/conveyorService.js\n");

/***/ }),

/***/ "(rsc)/./src/features/equipment/services/equipmentService.js":
/*!*************************************************************!*\
  !*** ./src/features/equipment/services/equipmentService.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEquipment: () => (/* binding */ createEquipment),\n/* harmony export */   createEquipmentCategory: () => (/* binding */ createEquipmentCategory),\n/* harmony export */   deleteEquipment: () => (/* binding */ deleteEquipment),\n/* harmony export */   deleteEquipmentCategory: () => (/* binding */ deleteEquipmentCategory),\n/* harmony export */   getActiveEquipmentCategories: () => (/* binding */ getActiveEquipmentCategories),\n/* harmony export */   getEquipment: () => (/* binding */ getEquipment),\n/* harmony export */   getEquipmentById: () => (/* binding */ getEquipmentById),\n/* harmony export */   getEquipmentCategories: () => (/* binding */ getEquipmentCategories),\n/* harmony export */   getEquipmentCategoryById: () => (/* binding */ getEquipmentCategoryById),\n/* harmony export */   getEquipmentStats: () => (/* binding */ getEquipmentStats),\n/* harmony export */   updateEquipment: () => (/* binding */ updateEquipment),\n/* harmony export */   updateEquipmentCategory: () => (/* binding */ updateEquipmentCategory)\n/* harmony export */ });\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n\n/**\n * Get paginated equipment categories list with filters\n */ async function getEquipmentCategories(filters = {}) {\n    const { search = \"\", status, page = 1, limit = 10, sortBy = \"createdAt\", sortOrder = \"desc\" } = filters;\n    const skip = (page - 1) * limit;\n    // Build where clause\n    const where = {\n        AND: [\n            // Search filter\n            search ? {\n                OR: [\n                    {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        description: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                ]\n            } : {},\n            // Status filter\n            status !== undefined ? {\n                status\n            } : {}\n        ]\n    };\n    // Get categories with pagination\n    const [categories, total] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.findMany({\n            where,\n            select: {\n                id: true,\n                name: true,\n                description: true,\n                status: true,\n                createdAt: true,\n                updatedAt: true,\n                _count: {\n                    select: {\n                        equipment: true\n                    }\n                }\n            },\n            orderBy: {\n                [sortBy]: sortOrder\n            },\n            skip,\n            take: limit\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.count({\n            where\n        })\n    ]);\n    return {\n        categories,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit)\n        }\n    };\n}\n/**\n * Get equipment category by ID\n */ async function getEquipmentCategoryById(id) {\n    const category = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            equipment: {\n                select: {\n                    id: true,\n                    name: true,\n                    code: true,\n                    status: true\n                }\n            }\n        }\n    });\n    if (!category) {\n        throw new Error(\"Equipment category not found\");\n    }\n    return category;\n}\n/**\n * Create new equipment category\n */ async function createEquipmentCategory(categoryData, createdBy) {\n    const { name, description, status } = categoryData;\n    // Check if category already exists\n    const existingCategory = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.findUnique({\n        where: {\n            name\n        }\n    });\n    if (existingCategory) {\n        throw new Error(\"Equipment category with this name already exists\");\n    }\n    // Create category\n    const category = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.create({\n        data: {\n            name,\n            description,\n            status\n        },\n        select: {\n            id: true,\n            name: true,\n            description: true,\n            status: true,\n            createdAt: true\n        }\n    });\n    return category;\n}\n/**\n * Update equipment category\n */ async function updateEquipmentCategory(id, categoryData, updatedBy) {\n    const { name, description, status } = categoryData;\n    // Check if category exists\n    const existingCategory = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!existingCategory) {\n        throw new Error(\"Equipment category not found\");\n    }\n    // Check if name is being changed and if new name already exists\n    if (name && name !== existingCategory.name) {\n        const nameExists = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.findUnique({\n            where: {\n                name\n            }\n        });\n        if (nameExists) {\n            throw new Error(\"Equipment category with this name already exists\");\n        }\n    }\n    // Update category\n    const category = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            ...name && {\n                name\n            },\n            ...description !== undefined && {\n                description\n            },\n            ...status !== undefined && {\n                status\n            }\n        },\n        select: {\n            id: true,\n            name: true,\n            description: true,\n            status: true,\n            updatedAt: true\n        }\n    });\n    return category;\n}\n/**\n * Delete equipment category\n */ async function deleteEquipmentCategory(id, deletedBy) {\n    // Check if category exists\n    const category = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            _count: {\n                select: {\n                    equipment: true\n                }\n            }\n        }\n    });\n    if (!category) {\n        throw new Error(\"Equipment category not found\");\n    }\n    // Check if category has equipment\n    if (category._count.equipment > 0) {\n        throw new Error(\"Cannot delete category that has equipment associated with it\");\n    }\n    // Delete category\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.delete({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    return {\n        message: \"Equipment category deleted successfully\"\n    };\n}\n/**\n * Get paginated equipment list with filters\n */ async function getEquipment(filters = {}) {\n    const { search = \"\", categoryId, status, page = 1, limit = 10, sortBy = \"createdAt\", sortOrder = \"desc\" } = filters;\n    const skip = (page - 1) * limit;\n    // Build where clause\n    const where = {\n        AND: [\n            // Search filter\n            search ? {\n                OR: [\n                    {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        code: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        description: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                ]\n            } : {},\n            // Category filter\n            categoryId ? {\n                categoryId: parseInt(categoryId)\n            } : {},\n            // Status filter\n            status !== undefined ? {\n                status\n            } : {}\n        ]\n    };\n    // Get equipment with pagination\n    const [equipment, total] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findMany({\n            where,\n            select: {\n                id: true,\n                name: true,\n                code: true,\n                subCategory: true,\n                description: true,\n                uom: true,\n                baseRate: true,\n                effectiveFrom: true,\n                validUpto: true,\n                status: true,\n                createdAt: true,\n                updatedAt: true,\n                category: {\n                    select: {\n                        id: true,\n                        name: true\n                    }\n                },\n                _count: {\n                    select: {\n                        components: true\n                    }\n                }\n            },\n            orderBy: {\n                [sortBy]: sortOrder\n            },\n            skip,\n            take: limit\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.count({\n            where\n        })\n    ]);\n    return {\n        equipment,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit)\n        }\n    };\n}\n/**\n * Get equipment by ID\n */ async function getEquipmentById(id) {\n    const equipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            category: {\n                select: {\n                    id: true,\n                    name: true\n                }\n            },\n            components: {\n                select: {\n                    id: true,\n                    name: true,\n                    code: true,\n                    baseRate: true,\n                    status: true\n                }\n            }\n        }\n    });\n    if (!equipment) {\n        throw new Error(\"Equipment not found\");\n    }\n    return equipment;\n}\n/**\n * Create new equipment\n */ async function createEquipment(equipmentData, createdBy) {\n    const { name, code, categoryId, subCategory, description, specifications, uom, baseRate, regionRates, effectiveFrom, validUpto, costBreakup, status } = equipmentData;\n    // Check if equipment code already exists\n    const existingEquipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findUnique({\n        where: {\n            code\n        }\n    });\n    if (existingEquipment) {\n        throw new Error(\"Equipment with this code already exists\");\n    }\n    // Verify category exists\n    const category = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.findUnique({\n        where: {\n            id: parseInt(categoryId)\n        }\n    });\n    if (!category) {\n        throw new Error(\"Equipment category not found\");\n    }\n    // Create equipment\n    const equipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.create({\n        data: {\n            name,\n            code,\n            categoryId: parseInt(categoryId),\n            subCategory,\n            description,\n            specifications,\n            uom,\n            baseRate,\n            regionRates,\n            effectiveFrom: new Date(effectiveFrom),\n            validUpto: validUpto ? new Date(validUpto) : null,\n            costBreakup,\n            status,\n            createdBy\n        },\n        include: {\n            category: {\n                select: {\n                    id: true,\n                    name: true\n                }\n            }\n        }\n    });\n    return equipment;\n}\n/**\n * Update equipment\n */ async function updateEquipment(id, equipmentData, updatedBy) {\n    const { name, code, categoryId, subCategory, description, specifications, uom, baseRate, regionRates, effectiveFrom, validUpto, costBreakup, status } = equipmentData;\n    // Check if equipment exists\n    const existingEquipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!existingEquipment) {\n        throw new Error(\"Equipment not found\");\n    }\n    // Check if code is being changed and if new code already exists\n    if (code && code !== existingEquipment.code) {\n        const codeExists = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findUnique({\n            where: {\n                code\n            }\n        });\n        if (codeExists) {\n            throw new Error(\"Equipment with this code already exists\");\n        }\n    }\n    // Verify category exists if being changed\n    if (categoryId && categoryId !== existingEquipment.categoryId) {\n        const category = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.findUnique({\n            where: {\n                id: parseInt(categoryId)\n            }\n        });\n        if (!category) {\n            throw new Error(\"Equipment category not found\");\n        }\n    }\n    // Update equipment\n    const equipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            ...name && {\n                name\n            },\n            ...code && {\n                code\n            },\n            ...categoryId && {\n                categoryId: parseInt(categoryId)\n            },\n            ...subCategory !== undefined && {\n                subCategory\n            },\n            ...description !== undefined && {\n                description\n            },\n            ...specifications !== undefined && {\n                specifications\n            },\n            ...uom && {\n                uom\n            },\n            ...baseRate !== undefined && {\n                baseRate\n            },\n            ...regionRates !== undefined && {\n                regionRates\n            },\n            ...effectiveFrom && {\n                effectiveFrom: new Date(effectiveFrom)\n            },\n            ...validUpto !== undefined && {\n                validUpto: validUpto ? new Date(validUpto) : null\n            },\n            ...costBreakup !== undefined && {\n                costBreakup\n            },\n            ...status !== undefined && {\n                status\n            },\n            updatedBy\n        },\n        include: {\n            category: {\n                select: {\n                    id: true,\n                    name: true\n                }\n            }\n        }\n    });\n    return equipment;\n}\n/**\n * Delete equipment\n */ async function deleteEquipment(id, deletedBy) {\n    // Check if equipment exists\n    const equipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            _count: {\n                select: {\n                    components: true\n                }\n            }\n        }\n    });\n    if (!equipment) {\n        throw new Error(\"Equipment not found\");\n    }\n    // Check if equipment has components\n    if (equipment._count.components > 0) {\n        throw new Error(\"Cannot delete equipment that has components associated with it\");\n    }\n    // Delete equipment\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.delete({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    return {\n        message: \"Equipment deleted successfully\"\n    };\n}\n/**\n * Get all active equipment categories for dropdown\n */ async function getActiveEquipmentCategories() {\n    const categories = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.findMany({\n        where: {\n            status: true\n        },\n        select: {\n            id: true,\n            name: true\n        },\n        orderBy: {\n            name: \"asc\"\n        }\n    });\n    return categories;\n}\n/**\n * Get equipment statistics\n */ async function getEquipmentStats() {\n    const [totalEquipment, activeEquipment, inactiveEquipment, totalCategories, activeCategories, recentEquipment] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.count(),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.count({\n            where: {\n                status: true\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.count({\n            where: {\n                status: false\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.count(),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentCategory.count({\n            where: {\n                status: true\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.count({\n            where: {\n                createdAt: {\n                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)\n                }\n            }\n        })\n    ]);\n    return {\n        totalEquipment,\n        activeEquipment,\n        inactiveEquipment,\n        totalCategories,\n        activeCategories,\n        recentEquipment\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvZmVhdHVyZXMvZXF1aXBtZW50L3NlcnZpY2VzL2VxdWlwbWVudFNlcnZpY2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFxQztBQUVyQzs7Q0FFQyxHQUNNLGVBQWVDLHVCQUF1QkMsVUFBVSxDQUFDLENBQUM7SUFDdkQsTUFBTSxFQUNKQyxTQUFTLEVBQUUsRUFDWEMsTUFBTSxFQUNOQyxPQUFPLENBQUMsRUFDUkMsUUFBUSxFQUFFLEVBQ1ZDLFNBQVMsV0FBVyxFQUNwQkMsWUFBWSxNQUFNLEVBQ25CLEdBQUdOO0lBRUosTUFBTU8sT0FBTyxDQUFDSixPQUFPLEtBQUtDO0lBRTFCLHFCQUFxQjtJQUNyQixNQUFNSSxRQUFRO1FBQ1pDLEtBQUs7WUFDSCxnQkFBZ0I7WUFDaEJSLFNBQ0k7Z0JBQ0VTLElBQUk7b0JBQ0Y7d0JBQUVDLE1BQU07NEJBQUVDLFVBQVVYOzRCQUFRWSxNQUFNO3dCQUFjO29CQUFFO29CQUNsRDt3QkFBRUMsYUFBYTs0QkFBRUYsVUFBVVg7NEJBQVFZLE1BQU07d0JBQWM7b0JBQUU7aUJBQzFEO1lBQ0gsSUFDQSxDQUFDO1lBQ0wsZ0JBQWdCO1lBQ2hCWCxXQUFXYSxZQUFZO2dCQUFFYjtZQUFPLElBQUksQ0FBQztTQUN0QztJQUNIO0lBRUEsaUNBQWlDO0lBQ2pDLE1BQU0sQ0FBQ2MsWUFBWUMsTUFBTSxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztRQUM1Q3JCLHNEQUFNQSxDQUFDc0IsaUJBQWlCLENBQUNDLFFBQVEsQ0FBQztZQUNoQ2I7WUFDQWMsUUFBUTtnQkFDTkMsSUFBSTtnQkFDSlosTUFBTTtnQkFDTkcsYUFBYTtnQkFDYlosUUFBUTtnQkFDUnNCLFdBQVc7Z0JBQ1hDLFdBQVc7Z0JBQ1hDLFFBQVE7b0JBQ05KLFFBQVE7d0JBQ05LLFdBQVc7b0JBQ2I7Z0JBQ0Y7WUFDRjtZQUNBQyxTQUFTO2dCQUFFLENBQUN2QixPQUFPLEVBQUVDO1lBQVU7WUFDL0JDO1lBQ0FzQixNQUFNekI7UUFDUjtRQUNBTixzREFBTUEsQ0FBQ3NCLGlCQUFpQixDQUFDVSxLQUFLLENBQUM7WUFBRXRCO1FBQU07S0FDeEM7SUFFRCxPQUFPO1FBQ0xRO1FBQ0FlLFlBQVk7WUFDVjVCO1lBQ0FDO1lBQ0FhO1lBQ0FlLFlBQVlDLEtBQUtDLElBQUksQ0FBQ2pCLFFBQVFiO1FBQ2hDO0lBQ0Y7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZStCLHlCQUF5QlosRUFBRTtJQUMvQyxNQUFNYSxXQUFXLE1BQU10QyxzREFBTUEsQ0FBQ3NCLGlCQUFpQixDQUFDaUIsVUFBVSxDQUFDO1FBQ3pEN0IsT0FBTztZQUFFZSxJQUFJZSxTQUFTZjtRQUFJO1FBQzFCZ0IsU0FBUztZQUNQWixXQUFXO2dCQUNUTCxRQUFRO29CQUNOQyxJQUFJO29CQUNKWixNQUFNO29CQUNONkIsTUFBTTtvQkFDTnRDLFFBQVE7Z0JBQ1Y7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxJQUFJLENBQUNrQyxVQUFVO1FBQ2IsTUFBTSxJQUFJSyxNQUFNO0lBQ2xCO0lBRUEsT0FBT0w7QUFDVDtBQUVBOztDQUVDLEdBQ00sZUFBZU0sd0JBQXdCQyxZQUFZLEVBQUVDLFNBQVM7SUFDbkUsTUFBTSxFQUFFakMsSUFBSSxFQUFFRyxXQUFXLEVBQUVaLE1BQU0sRUFBRSxHQUFHeUM7SUFFdEMsbUNBQW1DO0lBQ25DLE1BQU1FLG1CQUFtQixNQUFNL0Msc0RBQU1BLENBQUNzQixpQkFBaUIsQ0FBQ2lCLFVBQVUsQ0FBQztRQUNqRTdCLE9BQU87WUFBRUc7UUFBSztJQUNoQjtJQUVBLElBQUlrQyxrQkFBa0I7UUFDcEIsTUFBTSxJQUFJSixNQUFNO0lBQ2xCO0lBRUEsa0JBQWtCO0lBQ2xCLE1BQU1MLFdBQVcsTUFBTXRDLHNEQUFNQSxDQUFDc0IsaUJBQWlCLENBQUMwQixNQUFNLENBQUM7UUFDckRDLE1BQU07WUFDSnBDO1lBQ0FHO1lBQ0FaO1FBQ0Y7UUFDQW9CLFFBQVE7WUFDTkMsSUFBSTtZQUNKWixNQUFNO1lBQ05HLGFBQWE7WUFDYlosUUFBUTtZQUNSc0IsV0FBVztRQUNiO0lBQ0Y7SUFFQSxPQUFPWTtBQUNUO0FBRUE7O0NBRUMsR0FDTSxlQUFlWSx3QkFBd0J6QixFQUFFLEVBQUVvQixZQUFZLEVBQUVNLFNBQVM7SUFDdkUsTUFBTSxFQUFFdEMsSUFBSSxFQUFFRyxXQUFXLEVBQUVaLE1BQU0sRUFBRSxHQUFHeUM7SUFFdEMsMkJBQTJCO0lBQzNCLE1BQU1FLG1CQUFtQixNQUFNL0Msc0RBQU1BLENBQUNzQixpQkFBaUIsQ0FBQ2lCLFVBQVUsQ0FBQztRQUNqRTdCLE9BQU87WUFBRWUsSUFBSWUsU0FBU2Y7UUFBSTtJQUM1QjtJQUVBLElBQUksQ0FBQ3NCLGtCQUFrQjtRQUNyQixNQUFNLElBQUlKLE1BQU07SUFDbEI7SUFFQSxnRUFBZ0U7SUFDaEUsSUFBSTlCLFFBQVFBLFNBQVNrQyxpQkFBaUJsQyxJQUFJLEVBQUU7UUFDMUMsTUFBTXVDLGFBQWEsTUFBTXBELHNEQUFNQSxDQUFDc0IsaUJBQWlCLENBQUNpQixVQUFVLENBQUM7WUFDM0Q3QixPQUFPO2dCQUFFRztZQUFLO1FBQ2hCO1FBRUEsSUFBSXVDLFlBQVk7WUFDZCxNQUFNLElBQUlULE1BQU07UUFDbEI7SUFDRjtJQUVBLGtCQUFrQjtJQUNsQixNQUFNTCxXQUFXLE1BQU10QyxzREFBTUEsQ0FBQ3NCLGlCQUFpQixDQUFDK0IsTUFBTSxDQUFDO1FBQ3JEM0MsT0FBTztZQUFFZSxJQUFJZSxTQUFTZjtRQUFJO1FBQzFCd0IsTUFBTTtZQUNKLEdBQUlwQyxRQUFRO2dCQUFFQTtZQUFLLENBQUM7WUFDcEIsR0FBSUcsZ0JBQWdCQyxhQUFhO2dCQUFFRDtZQUFZLENBQUM7WUFDaEQsR0FBSVosV0FBV2EsYUFBYTtnQkFBRWI7WUFBTyxDQUFDO1FBQ3hDO1FBQ0FvQixRQUFRO1lBQ05DLElBQUk7WUFDSlosTUFBTTtZQUNORyxhQUFhO1lBQ2JaLFFBQVE7WUFDUnVCLFdBQVc7UUFDYjtJQUNGO0lBRUEsT0FBT1c7QUFDVDtBQUVBOztDQUVDLEdBQ00sZUFBZWdCLHdCQUF3QjdCLEVBQUUsRUFBRThCLFNBQVM7SUFDekQsMkJBQTJCO0lBQzNCLE1BQU1qQixXQUFXLE1BQU10QyxzREFBTUEsQ0FBQ3NCLGlCQUFpQixDQUFDaUIsVUFBVSxDQUFDO1FBQ3pEN0IsT0FBTztZQUFFZSxJQUFJZSxTQUFTZjtRQUFJO1FBQzFCZ0IsU0FBUztZQUNQYixRQUFRO2dCQUNOSixRQUFRO29CQUNOSyxXQUFXO2dCQUNiO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsSUFBSSxDQUFDUyxVQUFVO1FBQ2IsTUFBTSxJQUFJSyxNQUFNO0lBQ2xCO0lBRUEsa0NBQWtDO0lBQ2xDLElBQUlMLFNBQVNWLE1BQU0sQ0FBQ0MsU0FBUyxHQUFHLEdBQUc7UUFDakMsTUFBTSxJQUFJYyxNQUNSO0lBRUo7SUFFQSxrQkFBa0I7SUFDbEIsTUFBTTNDLHNEQUFNQSxDQUFDc0IsaUJBQWlCLENBQUNrQyxNQUFNLENBQUM7UUFDcEM5QyxPQUFPO1lBQUVlLElBQUllLFNBQVNmO1FBQUk7SUFDNUI7SUFFQSxPQUFPO1FBQUVnQyxTQUFTO0lBQTBDO0FBQzlEO0FBRUE7O0NBRUMsR0FDTSxlQUFlQyxhQUFheEQsVUFBVSxDQUFDLENBQUM7SUFDN0MsTUFBTSxFQUNKQyxTQUFTLEVBQUUsRUFDWHdELFVBQVUsRUFDVnZELE1BQU0sRUFDTkMsT0FBTyxDQUFDLEVBQ1JDLFFBQVEsRUFBRSxFQUNWQyxTQUFTLFdBQVcsRUFDcEJDLFlBQVksTUFBTSxFQUNuQixHQUFHTjtJQUVKLE1BQU1PLE9BQU8sQ0FBQ0osT0FBTyxLQUFLQztJQUUxQixxQkFBcUI7SUFDckIsTUFBTUksUUFBUTtRQUNaQyxLQUFLO1lBQ0gsZ0JBQWdCO1lBQ2hCUixTQUNJO2dCQUNFUyxJQUFJO29CQUNGO3dCQUFFQyxNQUFNOzRCQUFFQyxVQUFVWDs0QkFBUVksTUFBTTt3QkFBYztvQkFBRTtvQkFDbEQ7d0JBQUUyQixNQUFNOzRCQUFFNUIsVUFBVVg7NEJBQVFZLE1BQU07d0JBQWM7b0JBQUU7b0JBQ2xEO3dCQUFFQyxhQUFhOzRCQUFFRixVQUFVWDs0QkFBUVksTUFBTTt3QkFBYztvQkFBRTtpQkFDMUQ7WUFDSCxJQUNBLENBQUM7WUFDTCxrQkFBa0I7WUFDbEI0QyxhQUFhO2dCQUFFQSxZQUFZbkIsU0FBU21CO1lBQVksSUFBSSxDQUFDO1lBQ3JELGdCQUFnQjtZQUNoQnZELFdBQVdhLFlBQVk7Z0JBQUViO1lBQU8sSUFBSSxDQUFDO1NBQ3RDO0lBQ0g7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTSxDQUFDeUIsV0FBV1YsTUFBTSxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztRQUMzQ3JCLHNEQUFNQSxDQUFDNEQsZUFBZSxDQUFDckMsUUFBUSxDQUFDO1lBQzlCYjtZQUNBYyxRQUFRO2dCQUNOQyxJQUFJO2dCQUNKWixNQUFNO2dCQUNONkIsTUFBTTtnQkFDTm1CLGFBQWE7Z0JBQ2I3QyxhQUFhO2dCQUNiOEMsS0FBSztnQkFDTEMsVUFBVTtnQkFDVkMsZUFBZTtnQkFDZkMsV0FBVztnQkFDWDdELFFBQVE7Z0JBQ1JzQixXQUFXO2dCQUNYQyxXQUFXO2dCQUNYVyxVQUFVO29CQUNSZCxRQUFRO3dCQUNOQyxJQUFJO3dCQUNKWixNQUFNO29CQUNSO2dCQUNGO2dCQUNBZSxRQUFRO29CQUNOSixRQUFRO3dCQUNOMEMsWUFBWTtvQkFDZDtnQkFDRjtZQUNGO1lBQ0FwQyxTQUFTO2dCQUFFLENBQUN2QixPQUFPLEVBQUVDO1lBQVU7WUFDL0JDO1lBQ0FzQixNQUFNekI7UUFDUjtRQUNBTixzREFBTUEsQ0FBQzRELGVBQWUsQ0FBQzVCLEtBQUssQ0FBQztZQUFFdEI7UUFBTTtLQUN0QztJQUVELE9BQU87UUFDTG1CO1FBQ0FJLFlBQVk7WUFDVjVCO1lBQ0FDO1lBQ0FhO1lBQ0FlLFlBQVlDLEtBQUtDLElBQUksQ0FBQ2pCLFFBQVFiO1FBQ2hDO0lBQ0Y7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZTZELGlCQUFpQjFDLEVBQUU7SUFDdkMsTUFBTUksWUFBWSxNQUFNN0Isc0RBQU1BLENBQUM0RCxlQUFlLENBQUNyQixVQUFVLENBQUM7UUFDeEQ3QixPQUFPO1lBQUVlLElBQUllLFNBQVNmO1FBQUk7UUFDMUJnQixTQUFTO1lBQ1BILFVBQVU7Z0JBQ1JkLFFBQVE7b0JBQ05DLElBQUk7b0JBQ0paLE1BQU07Z0JBQ1I7WUFDRjtZQUNBcUQsWUFBWTtnQkFDVjFDLFFBQVE7b0JBQ05DLElBQUk7b0JBQ0paLE1BQU07b0JBQ042QixNQUFNO29CQUNOcUIsVUFBVTtvQkFDVjNELFFBQVE7Z0JBQ1Y7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxJQUFJLENBQUN5QixXQUFXO1FBQ2QsTUFBTSxJQUFJYyxNQUFNO0lBQ2xCO0lBRUEsT0FBT2Q7QUFDVDtBQUVBOztDQUVDLEdBQ00sZUFBZXVDLGdCQUFnQkMsYUFBYSxFQUFFdkIsU0FBUztJQUM1RCxNQUFNLEVBQ0pqQyxJQUFJLEVBQ0o2QixJQUFJLEVBQ0ppQixVQUFVLEVBQ1ZFLFdBQVcsRUFDWDdDLFdBQVcsRUFDWHNELGNBQWMsRUFDZFIsR0FBRyxFQUNIQyxRQUFRLEVBQ1JRLFdBQVcsRUFDWFAsYUFBYSxFQUNiQyxTQUFTLEVBQ1RPLFdBQVcsRUFDWHBFLE1BQU0sRUFDUCxHQUFHaUU7SUFFSix5Q0FBeUM7SUFDekMsTUFBTUksb0JBQW9CLE1BQU16RSxzREFBTUEsQ0FBQzRELGVBQWUsQ0FBQ3JCLFVBQVUsQ0FBQztRQUNoRTdCLE9BQU87WUFBRWdDO1FBQUs7SUFDaEI7SUFFQSxJQUFJK0IsbUJBQW1CO1FBQ3JCLE1BQU0sSUFBSTlCLE1BQU07SUFDbEI7SUFFQSx5QkFBeUI7SUFDekIsTUFBTUwsV0FBVyxNQUFNdEMsc0RBQU1BLENBQUNzQixpQkFBaUIsQ0FBQ2lCLFVBQVUsQ0FBQztRQUN6RDdCLE9BQU87WUFBRWUsSUFBSWUsU0FBU21CO1FBQVk7SUFDcEM7SUFFQSxJQUFJLENBQUNyQixVQUFVO1FBQ2IsTUFBTSxJQUFJSyxNQUFNO0lBQ2xCO0lBRUEsbUJBQW1CO0lBQ25CLE1BQU1kLFlBQVksTUFBTTdCLHNEQUFNQSxDQUFDNEQsZUFBZSxDQUFDWixNQUFNLENBQUM7UUFDcERDLE1BQU07WUFDSnBDO1lBQ0E2QjtZQUNBaUIsWUFBWW5CLFNBQVNtQjtZQUNyQkU7WUFDQTdDO1lBQ0FzRDtZQUNBUjtZQUNBQztZQUNBUTtZQUNBUCxlQUFlLElBQUlVLEtBQUtWO1lBQ3hCQyxXQUFXQSxZQUFZLElBQUlTLEtBQUtULGFBQWE7WUFDN0NPO1lBQ0FwRTtZQUNBMEM7UUFDRjtRQUNBTCxTQUFTO1lBQ1BILFVBQVU7Z0JBQ1JkLFFBQVE7b0JBQ05DLElBQUk7b0JBQ0paLE1BQU07Z0JBQ1I7WUFDRjtRQUNGO0lBQ0Y7SUFFQSxPQUFPZ0I7QUFDVDtBQUVBOztDQUVDLEdBQ00sZUFBZThDLGdCQUFnQmxELEVBQUUsRUFBRTRDLGFBQWEsRUFBRWxCLFNBQVM7SUFDaEUsTUFBTSxFQUNKdEMsSUFBSSxFQUNKNkIsSUFBSSxFQUNKaUIsVUFBVSxFQUNWRSxXQUFXLEVBQ1g3QyxXQUFXLEVBQ1hzRCxjQUFjLEVBQ2RSLEdBQUcsRUFDSEMsUUFBUSxFQUNSUSxXQUFXLEVBQ1hQLGFBQWEsRUFDYkMsU0FBUyxFQUNUTyxXQUFXLEVBQ1hwRSxNQUFNLEVBQ1AsR0FBR2lFO0lBRUosNEJBQTRCO0lBQzVCLE1BQU1JLG9CQUFvQixNQUFNekUsc0RBQU1BLENBQUM0RCxlQUFlLENBQUNyQixVQUFVLENBQUM7UUFDaEU3QixPQUFPO1lBQUVlLElBQUllLFNBQVNmO1FBQUk7SUFDNUI7SUFFQSxJQUFJLENBQUNnRCxtQkFBbUI7UUFDdEIsTUFBTSxJQUFJOUIsTUFBTTtJQUNsQjtJQUVBLGdFQUFnRTtJQUNoRSxJQUFJRCxRQUFRQSxTQUFTK0Isa0JBQWtCL0IsSUFBSSxFQUFFO1FBQzNDLE1BQU1rQyxhQUFhLE1BQU01RSxzREFBTUEsQ0FBQzRELGVBQWUsQ0FBQ3JCLFVBQVUsQ0FBQztZQUN6RDdCLE9BQU87Z0JBQUVnQztZQUFLO1FBQ2hCO1FBRUEsSUFBSWtDLFlBQVk7WUFDZCxNQUFNLElBQUlqQyxNQUFNO1FBQ2xCO0lBQ0Y7SUFFQSwwQ0FBMEM7SUFDMUMsSUFBSWdCLGNBQWNBLGVBQWVjLGtCQUFrQmQsVUFBVSxFQUFFO1FBQzdELE1BQU1yQixXQUFXLE1BQU10QyxzREFBTUEsQ0FBQ3NCLGlCQUFpQixDQUFDaUIsVUFBVSxDQUFDO1lBQ3pEN0IsT0FBTztnQkFBRWUsSUFBSWUsU0FBU21CO1lBQVk7UUFDcEM7UUFFQSxJQUFJLENBQUNyQixVQUFVO1lBQ2IsTUFBTSxJQUFJSyxNQUFNO1FBQ2xCO0lBQ0Y7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTWQsWUFBWSxNQUFNN0Isc0RBQU1BLENBQUM0RCxlQUFlLENBQUNQLE1BQU0sQ0FBQztRQUNwRDNDLE9BQU87WUFBRWUsSUFBSWUsU0FBU2Y7UUFBSTtRQUMxQndCLE1BQU07WUFDSixHQUFJcEMsUUFBUTtnQkFBRUE7WUFBSyxDQUFDO1lBQ3BCLEdBQUk2QixRQUFRO2dCQUFFQTtZQUFLLENBQUM7WUFDcEIsR0FBSWlCLGNBQWM7Z0JBQUVBLFlBQVluQixTQUFTbUI7WUFBWSxDQUFDO1lBQ3RELEdBQUlFLGdCQUFnQjVDLGFBQWE7Z0JBQUU0QztZQUFZLENBQUM7WUFDaEQsR0FBSTdDLGdCQUFnQkMsYUFBYTtnQkFBRUQ7WUFBWSxDQUFDO1lBQ2hELEdBQUlzRCxtQkFBbUJyRCxhQUFhO2dCQUFFcUQ7WUFBZSxDQUFDO1lBQ3RELEdBQUlSLE9BQU87Z0JBQUVBO1lBQUksQ0FBQztZQUNsQixHQUFJQyxhQUFhOUMsYUFBYTtnQkFBRThDO1lBQVMsQ0FBQztZQUMxQyxHQUFJUSxnQkFBZ0J0RCxhQUFhO2dCQUFFc0Q7WUFBWSxDQUFDO1lBQ2hELEdBQUlQLGlCQUFpQjtnQkFBRUEsZUFBZSxJQUFJVSxLQUFLVjtZQUFlLENBQUM7WUFDL0QsR0FBSUMsY0FBY2hELGFBQWE7Z0JBQzdCZ0QsV0FBV0EsWUFBWSxJQUFJUyxLQUFLVCxhQUFhO1lBQy9DLENBQUM7WUFDRCxHQUFJTyxnQkFBZ0J2RCxhQUFhO2dCQUFFdUQ7WUFBWSxDQUFDO1lBQ2hELEdBQUlwRSxXQUFXYSxhQUFhO2dCQUFFYjtZQUFPLENBQUM7WUFDdEMrQztRQUNGO1FBQ0FWLFNBQVM7WUFDUEgsVUFBVTtnQkFDUmQsUUFBUTtvQkFDTkMsSUFBSTtvQkFDSlosTUFBTTtnQkFDUjtZQUNGO1FBQ0Y7SUFDRjtJQUVBLE9BQU9nQjtBQUNUO0FBRUE7O0NBRUMsR0FDTSxlQUFlZ0QsZ0JBQWdCcEQsRUFBRSxFQUFFOEIsU0FBUztJQUNqRCw0QkFBNEI7SUFDNUIsTUFBTTFCLFlBQVksTUFBTTdCLHNEQUFNQSxDQUFDNEQsZUFBZSxDQUFDckIsVUFBVSxDQUFDO1FBQ3hEN0IsT0FBTztZQUFFZSxJQUFJZSxTQUFTZjtRQUFJO1FBQzFCZ0IsU0FBUztZQUNQYixRQUFRO2dCQUNOSixRQUFRO29CQUNOMEMsWUFBWTtnQkFDZDtZQUNGO1FBQ0Y7SUFDRjtJQUVBLElBQUksQ0FBQ3JDLFdBQVc7UUFDZCxNQUFNLElBQUljLE1BQU07SUFDbEI7SUFFQSxvQ0FBb0M7SUFDcEMsSUFBSWQsVUFBVUQsTUFBTSxDQUFDc0MsVUFBVSxHQUFHLEdBQUc7UUFDbkMsTUFBTSxJQUFJdkIsTUFDUjtJQUVKO0lBRUEsbUJBQW1CO0lBQ25CLE1BQU0zQyxzREFBTUEsQ0FBQzRELGVBQWUsQ0FBQ0osTUFBTSxDQUFDO1FBQ2xDOUMsT0FBTztZQUFFZSxJQUFJZSxTQUFTZjtRQUFJO0lBQzVCO0lBRUEsT0FBTztRQUFFZ0MsU0FBUztJQUFpQztBQUNyRDtBQUVBOztDQUVDLEdBQ00sZUFBZXFCO0lBQ3BCLE1BQU01RCxhQUFhLE1BQU1sQixzREFBTUEsQ0FBQ3NCLGlCQUFpQixDQUFDQyxRQUFRLENBQUM7UUFDekRiLE9BQU87WUFBRU4sUUFBUTtRQUFLO1FBQ3RCb0IsUUFBUTtZQUNOQyxJQUFJO1lBQ0paLE1BQU07UUFDUjtRQUNBaUIsU0FBUztZQUFFakIsTUFBTTtRQUFNO0lBQ3pCO0lBRUEsT0FBT0s7QUFDVDtBQUVBOztDQUVDLEdBQ00sZUFBZTZEO0lBQ3BCLE1BQU0sQ0FDSkMsZ0JBQ0FDLGlCQUNBQyxtQkFDQUMsaUJBQ0FDLGtCQUNBQyxnQkFDRCxHQUFHLE1BQU1qRSxRQUFRQyxHQUFHLENBQUM7UUFDcEJyQixzREFBTUEsQ0FBQzRELGVBQWUsQ0FBQzVCLEtBQUs7UUFDNUJoQyxzREFBTUEsQ0FBQzRELGVBQWUsQ0FBQzVCLEtBQUssQ0FBQztZQUFFdEIsT0FBTztnQkFBRU4sUUFBUTtZQUFLO1FBQUU7UUFDdkRKLHNEQUFNQSxDQUFDNEQsZUFBZSxDQUFDNUIsS0FBSyxDQUFDO1lBQUV0QixPQUFPO2dCQUFFTixRQUFRO1lBQU07UUFBRTtRQUN4REosc0RBQU1BLENBQUNzQixpQkFBaUIsQ0FBQ1UsS0FBSztRQUM5QmhDLHNEQUFNQSxDQUFDc0IsaUJBQWlCLENBQUNVLEtBQUssQ0FBQztZQUFFdEIsT0FBTztnQkFBRU4sUUFBUTtZQUFLO1FBQUU7UUFDekRKLHNEQUFNQSxDQUFDNEQsZUFBZSxDQUFDNUIsS0FBSyxDQUFDO1lBQzNCdEIsT0FBTztnQkFDTGdCLFdBQVc7b0JBQ1Q0RCxLQUFLLElBQUlaLEtBQUtBLEtBQUthLEdBQUcsS0FBSyxLQUFLLEtBQUssS0FBSyxLQUFLO2dCQUNqRDtZQUNGO1FBQ0Y7S0FDRDtJQUVELE9BQU87UUFDTFA7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7UUFDQUM7SUFDRjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY292ZXlvci1wb2MvLi9zcmMvZmVhdHVyZXMvZXF1aXBtZW50L3NlcnZpY2VzL2VxdWlwbWVudFNlcnZpY2UuanM/NGNiZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgcHJpc21hIGZyb20gXCJAL2xpYi9kYi9wcmlzbWFcIjtcblxuLyoqXG4gKiBHZXQgcGFnaW5hdGVkIGVxdWlwbWVudCBjYXRlZ29yaWVzIGxpc3Qgd2l0aCBmaWx0ZXJzXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRFcXVpcG1lbnRDYXRlZ29yaWVzKGZpbHRlcnMgPSB7fSkge1xuICBjb25zdCB7XG4gICAgc2VhcmNoID0gXCJcIixcbiAgICBzdGF0dXMsXG4gICAgcGFnZSA9IDEsXG4gICAgbGltaXQgPSAxMCxcbiAgICBzb3J0QnkgPSBcImNyZWF0ZWRBdFwiLFxuICAgIHNvcnRPcmRlciA9IFwiZGVzY1wiLFxuICB9ID0gZmlsdGVycztcblxuICBjb25zdCBza2lwID0gKHBhZ2UgLSAxKSAqIGxpbWl0O1xuXG4gIC8vIEJ1aWxkIHdoZXJlIGNsYXVzZVxuICBjb25zdCB3aGVyZSA9IHtcbiAgICBBTkQ6IFtcbiAgICAgIC8vIFNlYXJjaCBmaWx0ZXJcbiAgICAgIHNlYXJjaFxuICAgICAgICA/IHtcbiAgICAgICAgICAgIE9SOiBbXG4gICAgICAgICAgICAgIHsgbmFtZTogeyBjb250YWluczogc2VhcmNoLCBtb2RlOiBcImluc2Vuc2l0aXZlXCIgfSB9LFxuICAgICAgICAgICAgICB7IGRlc2NyaXB0aW9uOiB7IGNvbnRhaW5zOiBzZWFyY2gsIG1vZGU6IFwiaW5zZW5zaXRpdmVcIiB9IH0sXG4gICAgICAgICAgICBdLFxuICAgICAgICAgIH1cbiAgICAgICAgOiB7fSxcbiAgICAgIC8vIFN0YXR1cyBmaWx0ZXJcbiAgICAgIHN0YXR1cyAhPT0gdW5kZWZpbmVkID8geyBzdGF0dXMgfSA6IHt9LFxuICAgIF0sXG4gIH07XG5cbiAgLy8gR2V0IGNhdGVnb3JpZXMgd2l0aCBwYWdpbmF0aW9uXG4gIGNvbnN0IFtjYXRlZ29yaWVzLCB0b3RhbF0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgcHJpc21hLmVxdWlwbWVudENhdGVnb3J5LmZpbmRNYW55KHtcbiAgICAgIHdoZXJlLFxuICAgICAgc2VsZWN0OiB7XG4gICAgICAgIGlkOiB0cnVlLFxuICAgICAgICBuYW1lOiB0cnVlLFxuICAgICAgICBkZXNjcmlwdGlvbjogdHJ1ZSxcbiAgICAgICAgc3RhdHVzOiB0cnVlLFxuICAgICAgICBjcmVhdGVkQXQ6IHRydWUsXG4gICAgICAgIHVwZGF0ZWRBdDogdHJ1ZSxcbiAgICAgICAgX2NvdW50OiB7XG4gICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICBlcXVpcG1lbnQ6IHRydWUsXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgICBvcmRlckJ5OiB7IFtzb3J0QnldOiBzb3J0T3JkZXIgfSxcbiAgICAgIHNraXAsXG4gICAgICB0YWtlOiBsaW1pdCxcbiAgICB9KSxcbiAgICBwcmlzbWEuZXF1aXBtZW50Q2F0ZWdvcnkuY291bnQoeyB3aGVyZSB9KSxcbiAgXSk7XG5cbiAgcmV0dXJuIHtcbiAgICBjYXRlZ29yaWVzLFxuICAgIHBhZ2luYXRpb246IHtcbiAgICAgIHBhZ2UsXG4gICAgICBsaW1pdCxcbiAgICAgIHRvdGFsLFxuICAgICAgdG90YWxQYWdlczogTWF0aC5jZWlsKHRvdGFsIC8gbGltaXQpLFxuICAgIH0sXG4gIH07XG59XG5cbi8qKlxuICogR2V0IGVxdWlwbWVudCBjYXRlZ29yeSBieSBJRFxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0RXF1aXBtZW50Q2F0ZWdvcnlCeUlkKGlkKSB7XG4gIGNvbnN0IGNhdGVnb3J5ID0gYXdhaXQgcHJpc21hLmVxdWlwbWVudENhdGVnb3J5LmZpbmRVbmlxdWUoe1xuICAgIHdoZXJlOiB7IGlkOiBwYXJzZUludChpZCkgfSxcbiAgICBpbmNsdWRlOiB7XG4gICAgICBlcXVpcG1lbnQ6IHtcbiAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgaWQ6IHRydWUsXG4gICAgICAgICAgbmFtZTogdHJ1ZSxcbiAgICAgICAgICBjb2RlOiB0cnVlLFxuICAgICAgICAgIHN0YXR1czogdHJ1ZSxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgfSxcbiAgfSk7XG5cbiAgaWYgKCFjYXRlZ29yeSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkVxdWlwbWVudCBjYXRlZ29yeSBub3QgZm91bmRcIik7XG4gIH1cblxuICByZXR1cm4gY2F0ZWdvcnk7XG59XG5cbi8qKlxuICogQ3JlYXRlIG5ldyBlcXVpcG1lbnQgY2F0ZWdvcnlcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUVxdWlwbWVudENhdGVnb3J5KGNhdGVnb3J5RGF0YSwgY3JlYXRlZEJ5KSB7XG4gIGNvbnN0IHsgbmFtZSwgZGVzY3JpcHRpb24sIHN0YXR1cyB9ID0gY2F0ZWdvcnlEYXRhO1xuXG4gIC8vIENoZWNrIGlmIGNhdGVnb3J5IGFscmVhZHkgZXhpc3RzXG4gIGNvbnN0IGV4aXN0aW5nQ2F0ZWdvcnkgPSBhd2FpdCBwcmlzbWEuZXF1aXBtZW50Q2F0ZWdvcnkuZmluZFVuaXF1ZSh7XG4gICAgd2hlcmU6IHsgbmFtZSB9LFxuICB9KTtcblxuICBpZiAoZXhpc3RpbmdDYXRlZ29yeSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkVxdWlwbWVudCBjYXRlZ29yeSB3aXRoIHRoaXMgbmFtZSBhbHJlYWR5IGV4aXN0c1wiKTtcbiAgfVxuXG4gIC8vIENyZWF0ZSBjYXRlZ29yeVxuICBjb25zdCBjYXRlZ29yeSA9IGF3YWl0IHByaXNtYS5lcXVpcG1lbnRDYXRlZ29yeS5jcmVhdGUoe1xuICAgIGRhdGE6IHtcbiAgICAgIG5hbWUsXG4gICAgICBkZXNjcmlwdGlvbixcbiAgICAgIHN0YXR1cyxcbiAgICB9LFxuICAgIHNlbGVjdDoge1xuICAgICAgaWQ6IHRydWUsXG4gICAgICBuYW1lOiB0cnVlLFxuICAgICAgZGVzY3JpcHRpb246IHRydWUsXG4gICAgICBzdGF0dXM6IHRydWUsXG4gICAgICBjcmVhdGVkQXQ6IHRydWUsXG4gICAgfSxcbiAgfSk7XG5cbiAgcmV0dXJuIGNhdGVnb3J5O1xufVxuXG4vKipcbiAqIFVwZGF0ZSBlcXVpcG1lbnQgY2F0ZWdvcnlcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZUVxdWlwbWVudENhdGVnb3J5KGlkLCBjYXRlZ29yeURhdGEsIHVwZGF0ZWRCeSkge1xuICBjb25zdCB7IG5hbWUsIGRlc2NyaXB0aW9uLCBzdGF0dXMgfSA9IGNhdGVnb3J5RGF0YTtcblxuICAvLyBDaGVjayBpZiBjYXRlZ29yeSBleGlzdHNcbiAgY29uc3QgZXhpc3RpbmdDYXRlZ29yeSA9IGF3YWl0IHByaXNtYS5lcXVpcG1lbnRDYXRlZ29yeS5maW5kVW5pcXVlKHtcbiAgICB3aGVyZTogeyBpZDogcGFyc2VJbnQoaWQpIH0sXG4gIH0pO1xuXG4gIGlmICghZXhpc3RpbmdDYXRlZ29yeSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkVxdWlwbWVudCBjYXRlZ29yeSBub3QgZm91bmRcIik7XG4gIH1cblxuICAvLyBDaGVjayBpZiBuYW1lIGlzIGJlaW5nIGNoYW5nZWQgYW5kIGlmIG5ldyBuYW1lIGFscmVhZHkgZXhpc3RzXG4gIGlmIChuYW1lICYmIG5hbWUgIT09IGV4aXN0aW5nQ2F0ZWdvcnkubmFtZSkge1xuICAgIGNvbnN0IG5hbWVFeGlzdHMgPSBhd2FpdCBwcmlzbWEuZXF1aXBtZW50Q2F0ZWdvcnkuZmluZFVuaXF1ZSh7XG4gICAgICB3aGVyZTogeyBuYW1lIH0sXG4gICAgfSk7XG5cbiAgICBpZiAobmFtZUV4aXN0cykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRXF1aXBtZW50IGNhdGVnb3J5IHdpdGggdGhpcyBuYW1lIGFscmVhZHkgZXhpc3RzXCIpO1xuICAgIH1cbiAgfVxuXG4gIC8vIFVwZGF0ZSBjYXRlZ29yeVxuICBjb25zdCBjYXRlZ29yeSA9IGF3YWl0IHByaXNtYS5lcXVpcG1lbnRDYXRlZ29yeS51cGRhdGUoe1xuICAgIHdoZXJlOiB7IGlkOiBwYXJzZUludChpZCkgfSxcbiAgICBkYXRhOiB7XG4gICAgICAuLi4obmFtZSAmJiB7IG5hbWUgfSksXG4gICAgICAuLi4oZGVzY3JpcHRpb24gIT09IHVuZGVmaW5lZCAmJiB7IGRlc2NyaXB0aW9uIH0pLFxuICAgICAgLi4uKHN0YXR1cyAhPT0gdW5kZWZpbmVkICYmIHsgc3RhdHVzIH0pLFxuICAgIH0sXG4gICAgc2VsZWN0OiB7XG4gICAgICBpZDogdHJ1ZSxcbiAgICAgIG5hbWU6IHRydWUsXG4gICAgICBkZXNjcmlwdGlvbjogdHJ1ZSxcbiAgICAgIHN0YXR1czogdHJ1ZSxcbiAgICAgIHVwZGF0ZWRBdDogdHJ1ZSxcbiAgICB9LFxuICB9KTtcblxuICByZXR1cm4gY2F0ZWdvcnk7XG59XG5cbi8qKlxuICogRGVsZXRlIGVxdWlwbWVudCBjYXRlZ29yeVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZGVsZXRlRXF1aXBtZW50Q2F0ZWdvcnkoaWQsIGRlbGV0ZWRCeSkge1xuICAvLyBDaGVjayBpZiBjYXRlZ29yeSBleGlzdHNcbiAgY29uc3QgY2F0ZWdvcnkgPSBhd2FpdCBwcmlzbWEuZXF1aXBtZW50Q2F0ZWdvcnkuZmluZFVuaXF1ZSh7XG4gICAgd2hlcmU6IHsgaWQ6IHBhcnNlSW50KGlkKSB9LFxuICAgIGluY2x1ZGU6IHtcbiAgICAgIF9jb3VudDoge1xuICAgICAgICBzZWxlY3Q6IHtcbiAgICAgICAgICBlcXVpcG1lbnQ6IHRydWUsXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0sXG4gIH0pO1xuXG4gIGlmICghY2F0ZWdvcnkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJFcXVpcG1lbnQgY2F0ZWdvcnkgbm90IGZvdW5kXCIpO1xuICB9XG5cbiAgLy8gQ2hlY2sgaWYgY2F0ZWdvcnkgaGFzIGVxdWlwbWVudFxuICBpZiAoY2F0ZWdvcnkuX2NvdW50LmVxdWlwbWVudCA+IDApIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICBcIkNhbm5vdCBkZWxldGUgY2F0ZWdvcnkgdGhhdCBoYXMgZXF1aXBtZW50IGFzc29jaWF0ZWQgd2l0aCBpdFwiXG4gICAgKTtcbiAgfVxuXG4gIC8vIERlbGV0ZSBjYXRlZ29yeVxuICBhd2FpdCBwcmlzbWEuZXF1aXBtZW50Q2F0ZWdvcnkuZGVsZXRlKHtcbiAgICB3aGVyZTogeyBpZDogcGFyc2VJbnQoaWQpIH0sXG4gIH0pO1xuXG4gIHJldHVybiB7IG1lc3NhZ2U6IFwiRXF1aXBtZW50IGNhdGVnb3J5IGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5XCIgfTtcbn1cblxuLyoqXG4gKiBHZXQgcGFnaW5hdGVkIGVxdWlwbWVudCBsaXN0IHdpdGggZmlsdGVyc1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0RXF1aXBtZW50KGZpbHRlcnMgPSB7fSkge1xuICBjb25zdCB7XG4gICAgc2VhcmNoID0gXCJcIixcbiAgICBjYXRlZ29yeUlkLFxuICAgIHN0YXR1cyxcbiAgICBwYWdlID0gMSxcbiAgICBsaW1pdCA9IDEwLFxuICAgIHNvcnRCeSA9IFwiY3JlYXRlZEF0XCIsXG4gICAgc29ydE9yZGVyID0gXCJkZXNjXCIsXG4gIH0gPSBmaWx0ZXJzO1xuXG4gIGNvbnN0IHNraXAgPSAocGFnZSAtIDEpICogbGltaXQ7XG5cbiAgLy8gQnVpbGQgd2hlcmUgY2xhdXNlXG4gIGNvbnN0IHdoZXJlID0ge1xuICAgIEFORDogW1xuICAgICAgLy8gU2VhcmNoIGZpbHRlclxuICAgICAgc2VhcmNoXG4gICAgICAgID8ge1xuICAgICAgICAgICAgT1I6IFtcbiAgICAgICAgICAgICAgeyBuYW1lOiB7IGNvbnRhaW5zOiBzZWFyY2gsIG1vZGU6IFwiaW5zZW5zaXRpdmVcIiB9IH0sXG4gICAgICAgICAgICAgIHsgY29kZTogeyBjb250YWluczogc2VhcmNoLCBtb2RlOiBcImluc2Vuc2l0aXZlXCIgfSB9LFxuICAgICAgICAgICAgICB7IGRlc2NyaXB0aW9uOiB7IGNvbnRhaW5zOiBzZWFyY2gsIG1vZGU6IFwiaW5zZW5zaXRpdmVcIiB9IH0sXG4gICAgICAgICAgICBdLFxuICAgICAgICAgIH1cbiAgICAgICAgOiB7fSxcbiAgICAgIC8vIENhdGVnb3J5IGZpbHRlclxuICAgICAgY2F0ZWdvcnlJZCA/IHsgY2F0ZWdvcnlJZDogcGFyc2VJbnQoY2F0ZWdvcnlJZCkgfSA6IHt9LFxuICAgICAgLy8gU3RhdHVzIGZpbHRlclxuICAgICAgc3RhdHVzICE9PSB1bmRlZmluZWQgPyB7IHN0YXR1cyB9IDoge30sXG4gICAgXSxcbiAgfTtcblxuICAvLyBHZXQgZXF1aXBtZW50IHdpdGggcGFnaW5hdGlvblxuICBjb25zdCBbZXF1aXBtZW50LCB0b3RhbF0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgcHJpc21hLmVxdWlwbWVudE1hc3Rlci5maW5kTWFueSh7XG4gICAgICB3aGVyZSxcbiAgICAgIHNlbGVjdDoge1xuICAgICAgICBpZDogdHJ1ZSxcbiAgICAgICAgbmFtZTogdHJ1ZSxcbiAgICAgICAgY29kZTogdHJ1ZSxcbiAgICAgICAgc3ViQ2F0ZWdvcnk6IHRydWUsXG4gICAgICAgIGRlc2NyaXB0aW9uOiB0cnVlLFxuICAgICAgICB1b206IHRydWUsXG4gICAgICAgIGJhc2VSYXRlOiB0cnVlLFxuICAgICAgICBlZmZlY3RpdmVGcm9tOiB0cnVlLFxuICAgICAgICB2YWxpZFVwdG86IHRydWUsXG4gICAgICAgIHN0YXR1czogdHJ1ZSxcbiAgICAgICAgY3JlYXRlZEF0OiB0cnVlLFxuICAgICAgICB1cGRhdGVkQXQ6IHRydWUsXG4gICAgICAgIGNhdGVnb3J5OiB7XG4gICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICBpZDogdHJ1ZSxcbiAgICAgICAgICAgIG5hbWU6IHRydWUsXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgX2NvdW50OiB7XG4gICAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgICBjb21wb25lbnRzOiB0cnVlLFxuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgICAgb3JkZXJCeTogeyBbc29ydEJ5XTogc29ydE9yZGVyIH0sXG4gICAgICBza2lwLFxuICAgICAgdGFrZTogbGltaXQsXG4gICAgfSksXG4gICAgcHJpc21hLmVxdWlwbWVudE1hc3Rlci5jb3VudCh7IHdoZXJlIH0pLFxuICBdKTtcblxuICByZXR1cm4ge1xuICAgIGVxdWlwbWVudCxcbiAgICBwYWdpbmF0aW9uOiB7XG4gICAgICBwYWdlLFxuICAgICAgbGltaXQsXG4gICAgICB0b3RhbCxcbiAgICAgIHRvdGFsUGFnZXM6IE1hdGguY2VpbCh0b3RhbCAvIGxpbWl0KSxcbiAgICB9LFxuICB9O1xufVxuXG4vKipcbiAqIEdldCBlcXVpcG1lbnQgYnkgSURcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldEVxdWlwbWVudEJ5SWQoaWQpIHtcbiAgY29uc3QgZXF1aXBtZW50ID0gYXdhaXQgcHJpc21hLmVxdWlwbWVudE1hc3Rlci5maW5kVW5pcXVlKHtcbiAgICB3aGVyZTogeyBpZDogcGFyc2VJbnQoaWQpIH0sXG4gICAgaW5jbHVkZToge1xuICAgICAgY2F0ZWdvcnk6IHtcbiAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgaWQ6IHRydWUsXG4gICAgICAgICAgbmFtZTogdHJ1ZSxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgICBjb21wb25lbnRzOiB7XG4gICAgICAgIHNlbGVjdDoge1xuICAgICAgICAgIGlkOiB0cnVlLFxuICAgICAgICAgIG5hbWU6IHRydWUsXG4gICAgICAgICAgY29kZTogdHJ1ZSxcbiAgICAgICAgICBiYXNlUmF0ZTogdHJ1ZSxcbiAgICAgICAgICBzdGF0dXM6IHRydWUsXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0sXG4gIH0pO1xuXG4gIGlmICghZXF1aXBtZW50KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiRXF1aXBtZW50IG5vdCBmb3VuZFwiKTtcbiAgfVxuXG4gIHJldHVybiBlcXVpcG1lbnQ7XG59XG5cbi8qKlxuICogQ3JlYXRlIG5ldyBlcXVpcG1lbnRcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNyZWF0ZUVxdWlwbWVudChlcXVpcG1lbnREYXRhLCBjcmVhdGVkQnkpIHtcbiAgY29uc3Qge1xuICAgIG5hbWUsXG4gICAgY29kZSxcbiAgICBjYXRlZ29yeUlkLFxuICAgIHN1YkNhdGVnb3J5LFxuICAgIGRlc2NyaXB0aW9uLFxuICAgIHNwZWNpZmljYXRpb25zLFxuICAgIHVvbSxcbiAgICBiYXNlUmF0ZSxcbiAgICByZWdpb25SYXRlcyxcbiAgICBlZmZlY3RpdmVGcm9tLFxuICAgIHZhbGlkVXB0byxcbiAgICBjb3N0QnJlYWt1cCxcbiAgICBzdGF0dXMsXG4gIH0gPSBlcXVpcG1lbnREYXRhO1xuXG4gIC8vIENoZWNrIGlmIGVxdWlwbWVudCBjb2RlIGFscmVhZHkgZXhpc3RzXG4gIGNvbnN0IGV4aXN0aW5nRXF1aXBtZW50ID0gYXdhaXQgcHJpc21hLmVxdWlwbWVudE1hc3Rlci5maW5kVW5pcXVlKHtcbiAgICB3aGVyZTogeyBjb2RlIH0sXG4gIH0pO1xuXG4gIGlmIChleGlzdGluZ0VxdWlwbWVudCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkVxdWlwbWVudCB3aXRoIHRoaXMgY29kZSBhbHJlYWR5IGV4aXN0c1wiKTtcbiAgfVxuXG4gIC8vIFZlcmlmeSBjYXRlZ29yeSBleGlzdHNcbiAgY29uc3QgY2F0ZWdvcnkgPSBhd2FpdCBwcmlzbWEuZXF1aXBtZW50Q2F0ZWdvcnkuZmluZFVuaXF1ZSh7XG4gICAgd2hlcmU6IHsgaWQ6IHBhcnNlSW50KGNhdGVnb3J5SWQpIH0sXG4gIH0pO1xuXG4gIGlmICghY2F0ZWdvcnkpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJFcXVpcG1lbnQgY2F0ZWdvcnkgbm90IGZvdW5kXCIpO1xuICB9XG5cbiAgLy8gQ3JlYXRlIGVxdWlwbWVudFxuICBjb25zdCBlcXVpcG1lbnQgPSBhd2FpdCBwcmlzbWEuZXF1aXBtZW50TWFzdGVyLmNyZWF0ZSh7XG4gICAgZGF0YToge1xuICAgICAgbmFtZSxcbiAgICAgIGNvZGUsXG4gICAgICBjYXRlZ29yeUlkOiBwYXJzZUludChjYXRlZ29yeUlkKSxcbiAgICAgIHN1YkNhdGVnb3J5LFxuICAgICAgZGVzY3JpcHRpb24sXG4gICAgICBzcGVjaWZpY2F0aW9ucyxcbiAgICAgIHVvbSxcbiAgICAgIGJhc2VSYXRlLFxuICAgICAgcmVnaW9uUmF0ZXMsXG4gICAgICBlZmZlY3RpdmVGcm9tOiBuZXcgRGF0ZShlZmZlY3RpdmVGcm9tKSxcbiAgICAgIHZhbGlkVXB0bzogdmFsaWRVcHRvID8gbmV3IERhdGUodmFsaWRVcHRvKSA6IG51bGwsXG4gICAgICBjb3N0QnJlYWt1cCxcbiAgICAgIHN0YXR1cyxcbiAgICAgIGNyZWF0ZWRCeSxcbiAgICB9LFxuICAgIGluY2x1ZGU6IHtcbiAgICAgIGNhdGVnb3J5OiB7XG4gICAgICAgIHNlbGVjdDoge1xuICAgICAgICAgIGlkOiB0cnVlLFxuICAgICAgICAgIG5hbWU6IHRydWUsXG4gICAgICAgIH0sXG4gICAgICB9LFxuICAgIH0sXG4gIH0pO1xuXG4gIHJldHVybiBlcXVpcG1lbnQ7XG59XG5cbi8qKlxuICogVXBkYXRlIGVxdWlwbWVudFxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdXBkYXRlRXF1aXBtZW50KGlkLCBlcXVpcG1lbnREYXRhLCB1cGRhdGVkQnkpIHtcbiAgY29uc3Qge1xuICAgIG5hbWUsXG4gICAgY29kZSxcbiAgICBjYXRlZ29yeUlkLFxuICAgIHN1YkNhdGVnb3J5LFxuICAgIGRlc2NyaXB0aW9uLFxuICAgIHNwZWNpZmljYXRpb25zLFxuICAgIHVvbSxcbiAgICBiYXNlUmF0ZSxcbiAgICByZWdpb25SYXRlcyxcbiAgICBlZmZlY3RpdmVGcm9tLFxuICAgIHZhbGlkVXB0byxcbiAgICBjb3N0QnJlYWt1cCxcbiAgICBzdGF0dXMsXG4gIH0gPSBlcXVpcG1lbnREYXRhO1xuXG4gIC8vIENoZWNrIGlmIGVxdWlwbWVudCBleGlzdHNcbiAgY29uc3QgZXhpc3RpbmdFcXVpcG1lbnQgPSBhd2FpdCBwcmlzbWEuZXF1aXBtZW50TWFzdGVyLmZpbmRVbmlxdWUoe1xuICAgIHdoZXJlOiB7IGlkOiBwYXJzZUludChpZCkgfSxcbiAgfSk7XG5cbiAgaWYgKCFleGlzdGluZ0VxdWlwbWVudCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkVxdWlwbWVudCBub3QgZm91bmRcIik7XG4gIH1cblxuICAvLyBDaGVjayBpZiBjb2RlIGlzIGJlaW5nIGNoYW5nZWQgYW5kIGlmIG5ldyBjb2RlIGFscmVhZHkgZXhpc3RzXG4gIGlmIChjb2RlICYmIGNvZGUgIT09IGV4aXN0aW5nRXF1aXBtZW50LmNvZGUpIHtcbiAgICBjb25zdCBjb2RlRXhpc3RzID0gYXdhaXQgcHJpc21hLmVxdWlwbWVudE1hc3Rlci5maW5kVW5pcXVlKHtcbiAgICAgIHdoZXJlOiB7IGNvZGUgfSxcbiAgICB9KTtcblxuICAgIGlmIChjb2RlRXhpc3RzKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJFcXVpcG1lbnQgd2l0aCB0aGlzIGNvZGUgYWxyZWFkeSBleGlzdHNcIik7XG4gICAgfVxuICB9XG5cbiAgLy8gVmVyaWZ5IGNhdGVnb3J5IGV4aXN0cyBpZiBiZWluZyBjaGFuZ2VkXG4gIGlmIChjYXRlZ29yeUlkICYmIGNhdGVnb3J5SWQgIT09IGV4aXN0aW5nRXF1aXBtZW50LmNhdGVnb3J5SWQpIHtcbiAgICBjb25zdCBjYXRlZ29yeSA9IGF3YWl0IHByaXNtYS5lcXVpcG1lbnRDYXRlZ29yeS5maW5kVW5pcXVlKHtcbiAgICAgIHdoZXJlOiB7IGlkOiBwYXJzZUludChjYXRlZ29yeUlkKSB9LFxuICAgIH0pO1xuXG4gICAgaWYgKCFjYXRlZ29yeSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRXF1aXBtZW50IGNhdGVnb3J5IG5vdCBmb3VuZFwiKTtcbiAgICB9XG4gIH1cblxuICAvLyBVcGRhdGUgZXF1aXBtZW50XG4gIGNvbnN0IGVxdWlwbWVudCA9IGF3YWl0IHByaXNtYS5lcXVpcG1lbnRNYXN0ZXIudXBkYXRlKHtcbiAgICB3aGVyZTogeyBpZDogcGFyc2VJbnQoaWQpIH0sXG4gICAgZGF0YToge1xuICAgICAgLi4uKG5hbWUgJiYgeyBuYW1lIH0pLFxuICAgICAgLi4uKGNvZGUgJiYgeyBjb2RlIH0pLFxuICAgICAgLi4uKGNhdGVnb3J5SWQgJiYgeyBjYXRlZ29yeUlkOiBwYXJzZUludChjYXRlZ29yeUlkKSB9KSxcbiAgICAgIC4uLihzdWJDYXRlZ29yeSAhPT0gdW5kZWZpbmVkICYmIHsgc3ViQ2F0ZWdvcnkgfSksXG4gICAgICAuLi4oZGVzY3JpcHRpb24gIT09IHVuZGVmaW5lZCAmJiB7IGRlc2NyaXB0aW9uIH0pLFxuICAgICAgLi4uKHNwZWNpZmljYXRpb25zICE9PSB1bmRlZmluZWQgJiYgeyBzcGVjaWZpY2F0aW9ucyB9KSxcbiAgICAgIC4uLih1b20gJiYgeyB1b20gfSksXG4gICAgICAuLi4oYmFzZVJhdGUgIT09IHVuZGVmaW5lZCAmJiB7IGJhc2VSYXRlIH0pLFxuICAgICAgLi4uKHJlZ2lvblJhdGVzICE9PSB1bmRlZmluZWQgJiYgeyByZWdpb25SYXRlcyB9KSxcbiAgICAgIC4uLihlZmZlY3RpdmVGcm9tICYmIHsgZWZmZWN0aXZlRnJvbTogbmV3IERhdGUoZWZmZWN0aXZlRnJvbSkgfSksXG4gICAgICAuLi4odmFsaWRVcHRvICE9PSB1bmRlZmluZWQgJiYge1xuICAgICAgICB2YWxpZFVwdG86IHZhbGlkVXB0byA/IG5ldyBEYXRlKHZhbGlkVXB0bykgOiBudWxsLFxuICAgICAgfSksXG4gICAgICAuLi4oY29zdEJyZWFrdXAgIT09IHVuZGVmaW5lZCAmJiB7IGNvc3RCcmVha3VwIH0pLFxuICAgICAgLi4uKHN0YXR1cyAhPT0gdW5kZWZpbmVkICYmIHsgc3RhdHVzIH0pLFxuICAgICAgdXBkYXRlZEJ5LFxuICAgIH0sXG4gICAgaW5jbHVkZToge1xuICAgICAgY2F0ZWdvcnk6IHtcbiAgICAgICAgc2VsZWN0OiB7XG4gICAgICAgICAgaWQ6IHRydWUsXG4gICAgICAgICAgbmFtZTogdHJ1ZSxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgfSxcbiAgfSk7XG5cbiAgcmV0dXJuIGVxdWlwbWVudDtcbn1cblxuLyoqXG4gKiBEZWxldGUgZXF1aXBtZW50XG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBkZWxldGVFcXVpcG1lbnQoaWQsIGRlbGV0ZWRCeSkge1xuICAvLyBDaGVjayBpZiBlcXVpcG1lbnQgZXhpc3RzXG4gIGNvbnN0IGVxdWlwbWVudCA9IGF3YWl0IHByaXNtYS5lcXVpcG1lbnRNYXN0ZXIuZmluZFVuaXF1ZSh7XG4gICAgd2hlcmU6IHsgaWQ6IHBhcnNlSW50KGlkKSB9LFxuICAgIGluY2x1ZGU6IHtcbiAgICAgIF9jb3VudDoge1xuICAgICAgICBzZWxlY3Q6IHtcbiAgICAgICAgICBjb21wb25lbnRzOiB0cnVlLFxuICAgICAgICB9LFxuICAgICAgfSxcbiAgICB9LFxuICB9KTtcblxuICBpZiAoIWVxdWlwbWVudCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkVxdWlwbWVudCBub3QgZm91bmRcIik7XG4gIH1cblxuICAvLyBDaGVjayBpZiBlcXVpcG1lbnQgaGFzIGNvbXBvbmVudHNcbiAgaWYgKGVxdWlwbWVudC5fY291bnQuY29tcG9uZW50cyA+IDApIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICBcIkNhbm5vdCBkZWxldGUgZXF1aXBtZW50IHRoYXQgaGFzIGNvbXBvbmVudHMgYXNzb2NpYXRlZCB3aXRoIGl0XCJcbiAgICApO1xuICB9XG5cbiAgLy8gRGVsZXRlIGVxdWlwbWVudFxuICBhd2FpdCBwcmlzbWEuZXF1aXBtZW50TWFzdGVyLmRlbGV0ZSh7XG4gICAgd2hlcmU6IHsgaWQ6IHBhcnNlSW50KGlkKSB9LFxuICB9KTtcblxuICByZXR1cm4geyBtZXNzYWdlOiBcIkVxdWlwbWVudCBkZWxldGVkIHN1Y2Nlc3NmdWxseVwiIH07XG59XG5cbi8qKlxuICogR2V0IGFsbCBhY3RpdmUgZXF1aXBtZW50IGNhdGVnb3JpZXMgZm9yIGRyb3Bkb3duXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRBY3RpdmVFcXVpcG1lbnRDYXRlZ29yaWVzKCkge1xuICBjb25zdCBjYXRlZ29yaWVzID0gYXdhaXQgcHJpc21hLmVxdWlwbWVudENhdGVnb3J5LmZpbmRNYW55KHtcbiAgICB3aGVyZTogeyBzdGF0dXM6IHRydWUgfSxcbiAgICBzZWxlY3Q6IHtcbiAgICAgIGlkOiB0cnVlLFxuICAgICAgbmFtZTogdHJ1ZSxcbiAgICB9LFxuICAgIG9yZGVyQnk6IHsgbmFtZTogXCJhc2NcIiB9LFxuICB9KTtcblxuICByZXR1cm4gY2F0ZWdvcmllcztcbn1cblxuLyoqXG4gKiBHZXQgZXF1aXBtZW50IHN0YXRpc3RpY3NcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldEVxdWlwbWVudFN0YXRzKCkge1xuICBjb25zdCBbXG4gICAgdG90YWxFcXVpcG1lbnQsXG4gICAgYWN0aXZlRXF1aXBtZW50LFxuICAgIGluYWN0aXZlRXF1aXBtZW50LFxuICAgIHRvdGFsQ2F0ZWdvcmllcyxcbiAgICBhY3RpdmVDYXRlZ29yaWVzLFxuICAgIHJlY2VudEVxdWlwbWVudCxcbiAgXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICBwcmlzbWEuZXF1aXBtZW50TWFzdGVyLmNvdW50KCksXG4gICAgcHJpc21hLmVxdWlwbWVudE1hc3Rlci5jb3VudCh7IHdoZXJlOiB7IHN0YXR1czogdHJ1ZSB9IH0pLFxuICAgIHByaXNtYS5lcXVpcG1lbnRNYXN0ZXIuY291bnQoeyB3aGVyZTogeyBzdGF0dXM6IGZhbHNlIH0gfSksXG4gICAgcHJpc21hLmVxdWlwbWVudENhdGVnb3J5LmNvdW50KCksXG4gICAgcHJpc21hLmVxdWlwbWVudENhdGVnb3J5LmNvdW50KHsgd2hlcmU6IHsgc3RhdHVzOiB0cnVlIH0gfSksXG4gICAgcHJpc21hLmVxdWlwbWVudE1hc3Rlci5jb3VudCh7XG4gICAgICB3aGVyZToge1xuICAgICAgICBjcmVhdGVkQXQ6IHtcbiAgICAgICAgICBndGU6IG5ldyBEYXRlKERhdGUubm93KCkgLSAzMCAqIDI0ICogNjAgKiA2MCAqIDEwMDApLCAvLyBMYXN0IDMwIGRheXNcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgfSksXG4gIF0pO1xuXG4gIHJldHVybiB7XG4gICAgdG90YWxFcXVpcG1lbnQsXG4gICAgYWN0aXZlRXF1aXBtZW50LFxuICAgIGluYWN0aXZlRXF1aXBtZW50LFxuICAgIHRvdGFsQ2F0ZWdvcmllcyxcbiAgICBhY3RpdmVDYXRlZ29yaWVzLFxuICAgIHJlY2VudEVxdWlwbWVudCxcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJwcmlzbWEiLCJnZXRFcXVpcG1lbnRDYXRlZ29yaWVzIiwiZmlsdGVycyIsInNlYXJjaCIsInN0YXR1cyIsInBhZ2UiLCJsaW1pdCIsInNvcnRCeSIsInNvcnRPcmRlciIsInNraXAiLCJ3aGVyZSIsIkFORCIsIk9SIiwibmFtZSIsImNvbnRhaW5zIiwibW9kZSIsImRlc2NyaXB0aW9uIiwidW5kZWZpbmVkIiwiY2F0ZWdvcmllcyIsInRvdGFsIiwiUHJvbWlzZSIsImFsbCIsImVxdWlwbWVudENhdGVnb3J5IiwiZmluZE1hbnkiLCJzZWxlY3QiLCJpZCIsImNyZWF0ZWRBdCIsInVwZGF0ZWRBdCIsIl9jb3VudCIsImVxdWlwbWVudCIsIm9yZGVyQnkiLCJ0YWtlIiwiY291bnQiLCJwYWdpbmF0aW9uIiwidG90YWxQYWdlcyIsIk1hdGgiLCJjZWlsIiwiZ2V0RXF1aXBtZW50Q2F0ZWdvcnlCeUlkIiwiY2F0ZWdvcnkiLCJmaW5kVW5pcXVlIiwicGFyc2VJbnQiLCJpbmNsdWRlIiwiY29kZSIsIkVycm9yIiwiY3JlYXRlRXF1aXBtZW50Q2F0ZWdvcnkiLCJjYXRlZ29yeURhdGEiLCJjcmVhdGVkQnkiLCJleGlzdGluZ0NhdGVnb3J5IiwiY3JlYXRlIiwiZGF0YSIsInVwZGF0ZUVxdWlwbWVudENhdGVnb3J5IiwidXBkYXRlZEJ5IiwibmFtZUV4aXN0cyIsInVwZGF0ZSIsImRlbGV0ZUVxdWlwbWVudENhdGVnb3J5IiwiZGVsZXRlZEJ5IiwiZGVsZXRlIiwibWVzc2FnZSIsImdldEVxdWlwbWVudCIsImNhdGVnb3J5SWQiLCJlcXVpcG1lbnRNYXN0ZXIiLCJzdWJDYXRlZ29yeSIsInVvbSIsImJhc2VSYXRlIiwiZWZmZWN0aXZlRnJvbSIsInZhbGlkVXB0byIsImNvbXBvbmVudHMiLCJnZXRFcXVpcG1lbnRCeUlkIiwiY3JlYXRlRXF1aXBtZW50IiwiZXF1aXBtZW50RGF0YSIsInNwZWNpZmljYXRpb25zIiwicmVnaW9uUmF0ZXMiLCJjb3N0QnJlYWt1cCIsImV4aXN0aW5nRXF1aXBtZW50IiwiRGF0ZSIsInVwZGF0ZUVxdWlwbWVudCIsImNvZGVFeGlzdHMiLCJkZWxldGVFcXVpcG1lbnQiLCJnZXRBY3RpdmVFcXVpcG1lbnRDYXRlZ29yaWVzIiwiZ2V0RXF1aXBtZW50U3RhdHMiLCJ0b3RhbEVxdWlwbWVudCIsImFjdGl2ZUVxdWlwbWVudCIsImluYWN0aXZlRXF1aXBtZW50IiwidG90YWxDYXRlZ29yaWVzIiwiYWN0aXZlQ2F0ZWdvcmllcyIsInJlY2VudEVxdWlwbWVudCIsImd0ZSIsIm5vdyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/features/equipment/services/equipmentService.js\n");

/***/ }),

/***/ "(rsc)/./src/features/estimation/services/estimationService.js":
/*!***************************************************************!*\
  !*** ./src/features/estimation/services/estimationService.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEstimationSheet: () => (/* binding */ createEstimationSheet),\n/* harmony export */   deleteEstimationSheet: () => (/* binding */ deleteEstimationSheet),\n/* harmony export */   finalizeEstimationSheet: () => (/* binding */ finalizeEstimationSheet),\n/* harmony export */   generateAutomaticEstimation: () => (/* binding */ generateAutomaticEstimation),\n/* harmony export */   getEstimationSheetById: () => (/* binding */ getEstimationSheetById),\n/* harmony export */   getEstimationSheets: () => (/* binding */ getEstimationSheets),\n/* harmony export */   getEstimationStats: () => (/* binding */ getEstimationStats),\n/* harmony export */   updateEstimationSheet: () => (/* binding */ updateEstimationSheet)\n/* harmony export */ });\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n\n/**\n * Get paginated estimation sheets list with filters\n */ async function getEstimationSheets(filters = {}) {\n    const { search = \"\", planId, status, page = 1, limit = 10, sortBy = \"generatedAt\", sortOrder = \"desc\" } = filters;\n    const skip = (page - 1) * limit;\n    // Build where clause\n    const where = {\n        AND: [\n            // Search filter\n            search ? {\n                OR: [\n                    {\n                        notes: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        plan: {\n                            planName: {\n                                contains: search,\n                                mode: \"insensitive\"\n                            }\n                        }\n                    }\n                ]\n            } : {},\n            // Plan filter\n            planId ? {\n                planId: parseInt(planId)\n            } : {},\n            // Status filter\n            status ? {\n                status\n            } : {}\n        ]\n    };\n    // Get estimation sheets with pagination\n    const [estimations, total] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findMany({\n            where,\n            select: {\n                id: true,\n                estimationVersion: true,\n                equipmentCost: true,\n                componentCost: true,\n                laborCost: true,\n                overheadCost: true,\n                discountPercent: true,\n                markupPercent: true,\n                totalCost: true,\n                status: true,\n                generatedAt: true,\n                plan: {\n                    select: {\n                        id: true,\n                        planName: true,\n                        conveyorType: true,\n                        totalLengthM: true,\n                        loadType: true\n                    }\n                }\n            },\n            orderBy: {\n                [sortBy]: sortOrder\n            },\n            skip,\n            take: limit\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where\n        })\n    ]);\n    return {\n        estimations,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit)\n        }\n    };\n}\n/**\n * Get estimation sheet by ID\n */ async function getEstimationSheetById(id) {\n    const estimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            plan: {\n                include: {\n                    estimationSheets: {\n                        select: {\n                            id: true,\n                            estimationVersion: true,\n                            totalCost: true,\n                            status: true,\n                            generatedAt: true\n                        },\n                        orderBy: {\n                            generatedAt: \"desc\"\n                        }\n                    }\n                }\n            }\n        }\n    });\n    if (!estimation) {\n        throw new Error(\"Estimation sheet not found\");\n    }\n    return estimation;\n}\n/**\n * Create new estimation sheet\n */ async function createEstimationSheet(estimationData, generatedBy) {\n    const { planId, estimationVersion, equipmentCost, componentCost, laborCost, overheadCost, discountPercent, markupPercent, totalCost, notes, status } = estimationData;\n    // Verify plan exists\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(planId)\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    // Get next version number if not provided\n    let version = estimationVersion;\n    if (!version) {\n        const lastEstimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findFirst({\n            where: {\n                planId: parseInt(planId)\n            },\n            orderBy: {\n                estimationVersion: \"desc\"\n            }\n        });\n        version = lastEstimation ? lastEstimation.estimationVersion + 1 : 1;\n    }\n    // Create estimation sheet\n    const estimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.create({\n        data: {\n            planId: parseInt(planId),\n            estimationVersion: version,\n            equipmentCost,\n            componentCost,\n            laborCost,\n            overheadCost,\n            discountPercent,\n            markupPercent,\n            totalCost,\n            notes,\n            status,\n            generatedBy\n        },\n        include: {\n            plan: {\n                select: {\n                    id: true,\n                    planName: true,\n                    conveyorType: true\n                }\n            }\n        }\n    });\n    return estimation;\n}\n/**\n * Update estimation sheet\n */ async function updateEstimationSheet(id, estimationData, updatedBy) {\n    const { equipmentCost, componentCost, laborCost, overheadCost, discountPercent, markupPercent, totalCost, notes, status } = estimationData;\n    // Check if estimation exists\n    const existingEstimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!existingEstimation) {\n        throw new Error(\"Estimation sheet not found\");\n    }\n    // Update estimation sheet\n    const estimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            ...equipmentCost !== undefined && {\n                equipmentCost\n            },\n            ...componentCost !== undefined && {\n                componentCost\n            },\n            ...laborCost !== undefined && {\n                laborCost\n            },\n            ...overheadCost !== undefined && {\n                overheadCost\n            },\n            ...discountPercent !== undefined && {\n                discountPercent\n            },\n            ...markupPercent !== undefined && {\n                markupPercent\n            },\n            ...totalCost !== undefined && {\n                totalCost\n            },\n            ...notes !== undefined && {\n                notes\n            },\n            ...status && {\n                status\n            }\n        },\n        include: {\n            plan: {\n                select: {\n                    id: true,\n                    planName: true,\n                    conveyorType: true\n                }\n            }\n        }\n    });\n    return estimation;\n}\n/**\n * Delete estimation sheet\n */ async function deleteEstimationSheet(id, deletedBy) {\n    // Check if estimation exists\n    const estimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!estimation) {\n        throw new Error(\"Estimation sheet not found\");\n    }\n    // Delete estimation sheet\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.delete({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    return {\n        message: \"Estimation sheet deleted successfully\"\n    };\n}\n/**\n * Generate automatic estimation based on plan\n */ async function generateAutomaticEstimation(planId, generatedBy) {\n    // Get plan details\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(planId)\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    // Get equipment based on conveyor type\n    const equipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findMany({\n        where: {\n            status: true,\n            OR: [\n                {\n                    name: {\n                        contains: plan.conveyorType.split(\" \")[0],\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    category: {\n                        name: {\n                            contains: \"Conveyor\",\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ]\n        },\n        take: 5 // Limit to top 5 relevant equipment\n    });\n    // Get components for the equipment\n    const components = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findMany({\n        where: {\n            status: true,\n            equipment: {\n                id: {\n                    in: equipment.map((e)=>e.id)\n                }\n            }\n        },\n        take: 10 // Limit to top 10 relevant components\n    });\n    // Calculate costs\n    const equipmentCost = equipment.reduce((total, item)=>{\n        // Simple calculation: base rate * length factor\n        const lengthFactor = Math.max(1, plan.totalLengthM / 100);\n        return total + parseFloat(item.baseRate) * lengthFactor;\n    }, 0);\n    const componentCost = components.reduce((total, item)=>{\n        // Simple calculation: base rate * length factor\n        const lengthFactor = Math.max(1, plan.totalLengthM / 50);\n        return total + parseFloat(item.baseRate) * lengthFactor;\n    }, 0);\n    const materialCost = equipmentCost + componentCost;\n    const laborCost = materialCost * 0.15 // 15% of material cost\n    ;\n    const overheadCost = (materialCost + laborCost) * 0.10 // 10% of material + labor\n    ;\n    const subtotal = materialCost + laborCost + overheadCost;\n    const markupPercent = 20 // 20% markup\n    ;\n    const totalCost = subtotal * (1 + markupPercent / 100);\n    // Get next version number\n    const lastEstimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findFirst({\n        where: {\n            planId: parseInt(planId)\n        },\n        orderBy: {\n            estimationVersion: \"desc\"\n        }\n    });\n    const version = lastEstimation ? lastEstimation.estimationVersion + 1 : 1;\n    // Create estimation\n    const estimation = await createEstimationSheet({\n        planId: parseInt(planId),\n        estimationVersion: version,\n        equipmentCost,\n        componentCost,\n        laborCost,\n        overheadCost,\n        discountPercent: 0,\n        markupPercent,\n        totalCost,\n        notes: \"Auto-generated estimation based on plan specifications\",\n        status: \"Draft\"\n    }, generatedBy);\n    return estimation;\n}\n/**\n * Finalize estimation sheet\n */ async function finalizeEstimationSheet(id, finalizedBy) {\n    const estimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!estimation) {\n        throw new Error(\"Estimation sheet not found\");\n    }\n    if (estimation.status !== \"Draft\") {\n        throw new Error(\"Only draft estimations can be finalized\");\n    }\n    const finalizedEstimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            status: \"Final\"\n        }\n    });\n    return finalizedEstimation;\n}\n/**\n * Get estimation statistics\n */ async function getEstimationStats() {\n    const [totalEstimations, draftEstimations, finalEstimations, sentEstimations, approvedEstimations, recentEstimations] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count(),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where: {\n                status: \"Draft\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where: {\n                status: \"Final\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where: {\n                status: \"Sent\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where: {\n                status: \"Approved\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where: {\n                generatedAt: {\n                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                }\n            }\n        })\n    ]);\n    return {\n        totalEstimations,\n        draftEstimations,\n        finalEstimations,\n        sentEstimations,\n        approvedEstimations,\n        recentEstimations\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/estimation/services/estimationService.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/response.js":
/*!*********************************!*\
  !*** ./src/lib/api/response.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiResponse: () => (/* binding */ ApiResponse),\n/* harmony export */   createdResponse: () => (/* binding */ createdResponse),\n/* harmony export */   errorResponse: () => (/* binding */ errorResponse),\n/* harmony export */   forbiddenResponse: () => (/* binding */ forbiddenResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   noContentResponse: () => (/* binding */ noContentResponse),\n/* harmony export */   notFoundResponse: () => (/* binding */ notFoundResponse),\n/* harmony export */   paginatedResponse: () => (/* binding */ paginatedResponse),\n/* harmony export */   serverErrorResponse: () => (/* binding */ serverErrorResponse),\n/* harmony export */   successResponse: () => (/* binding */ successResponse),\n/* harmony export */   unauthorizedResponse: () => (/* binding */ unauthorizedResponse),\n/* harmony export */   validationErrorResponse: () => (/* binding */ validationErrorResponse),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\n/**\n * Standard API response structure\n */ class ApiResponse {\n    constructor(success = true, data = null, message = null, errors = null, meta = null){\n        this.success = success;\n        this.data = data;\n        this.message = message;\n        this.errors = errors;\n        this.meta = meta;\n        this.timestamp = new Date().toISOString();\n    }\n}\n/**\n * Success response helper\n */ function successResponse(data = null, message = null, meta = null, status = 200) {\n    const response = new ApiResponse(true, data, message, null, meta);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Error response helper\n */ function errorResponse(message, errors = null, status = 400) {\n    const response = new ApiResponse(false, null, message, errors);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Validation error response\n */ function validationErrorResponse(errors, message = \"Validation failed\") {\n    return errorResponse(message, errors, 422);\n}\n/**\n * Unauthorized response\n */ function unauthorizedResponse(message = \"Unauthorized\") {\n    return errorResponse(message, null, 401);\n}\n/**\n * Forbidden response\n */ function forbiddenResponse(message = \"Forbidden\") {\n    return errorResponse(message, null, 403);\n}\n/**\n * Not found response\n */ function notFoundResponse(message = \"Resource not found\") {\n    return errorResponse(message, null, 404);\n}\n/**\n * Internal server error response\n */ function serverErrorResponse(message = \"Internal server error\") {\n    return errorResponse(message, null, 500);\n}\n/**\n * Created response\n */ function createdResponse(data, message = \"Resource created successfully\") {\n    return successResponse(data, message, null, 201);\n}\n/**\n * No content response\n */ function noContentResponse() {\n    return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](null, {\n        status: 204\n    });\n}\n/**\n * Paginated response helper\n */ function paginatedResponse(data, pagination, message = null) {\n    const meta = {\n        pagination: {\n            page: pagination.page,\n            limit: pagination.limit,\n            total: pagination.total,\n            totalPages: Math.ceil(pagination.total / pagination.limit),\n            hasNext: pagination.page < Math.ceil(pagination.total / pagination.limit),\n            hasPrev: pagination.page > 1\n        }\n    };\n    return successResponse(data, message, meta);\n}\n/**\n * Handle API errors consistently\n */ function handleApiError(error) {\n    console.error(\"API Error:\", error);\n    // Prisma errors\n    if (error.code === \"P2002\") {\n        return errorResponse(\"Resource already exists\", null, 409);\n    }\n    if (error.code === \"P2025\") {\n        return notFoundResponse(\"Resource not found\");\n    }\n    // Validation errors (Zod)\n    if (error.name === \"ZodError\") {\n        const validationErrors = error.errors.map((err)=>({\n                field: err.path.join(\".\"),\n                message: err.message,\n                code: err.code\n            }));\n        return validationErrorResponse(validationErrors);\n    }\n    // JWT errors\n    if (error.name === \"JsonWebTokenError\") {\n        return unauthorizedResponse(\"Invalid token\");\n    }\n    if (error.name === \"TokenExpiredError\") {\n        return unauthorizedResponse(\"Token expired\");\n    }\n    // Custom application errors\n    if (error.message === \"Invalid credentials\") {\n        return unauthorizedResponse(\"Invalid email or password\");\n    }\n    if (error.message === \"User not found\") {\n        return notFoundResponse(\"User not found\");\n    }\n    if (error.message === \"Insufficient permissions\") {\n        return forbiddenResponse(\"Insufficient permissions\");\n    }\n    // Default server error\n    return serverErrorResponse();\n}\n/**\n * Async error handler wrapper\n */ function withErrorHandling(handler) {\n    return async (request, context)=>{\n        try {\n            return await handler(request, context);\n        } catch (error) {\n            return handleApiError(error);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/response.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/session.js":
/*!*********************************!*\
  !*** ./src/lib/auth/session.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   clearAuthCookie: () => (/* binding */ clearAuthCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   deleteAllUserSessions: () => (/* binding */ deleteAllUserSessions),\n/* harmony export */   deleteSession: () => (/* binding */ deleteSession),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   setAuthCookie: () => (/* binding */ setAuthCookie),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n/* harmony import */ var _lib_config_env__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/config/env */ \"(rsc)/./src/lib/config/env.js\");\n\n\n\n\nconst JWT_SECRET = _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].JWT_SECRET;\nconst TOKEN_EXPIRY = \"7d\" // 7 days\n;\nconst COOKIE_NAME = \"auth-token\";\n/**\n * Generate JWT token for user\n */ function generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: TOKEN_EXPIRY,\n        issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n        audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n    });\n}\n/**\n * Verify JWT token\n */ function verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET, {\n            issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n            audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n        });\n    } catch (error) {\n        throw new Error(\"Invalid token\");\n    }\n}\n/**\n * Create session for user\n */ async function createSession(userId, userAgent = null, ipAddress = null) {\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now\n    ;\n    // Generate JWT token\n    const tokenPayload = {\n        userId,\n        sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    };\n    const token = generateToken(tokenPayload);\n    // Store session in database\n    const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.create({\n        data: {\n            userId,\n            token,\n            expiresAt,\n            userAgent,\n            ipAddress\n        }\n    });\n    return {\n        session,\n        token\n    };\n}\n/**\n * Get session from token\n */ async function getSession(token) {\n    if (!token) return null;\n    try {\n        // Verify JWT token\n        const payload = verifyToken(token);\n        // Get session from database\n        const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.findUnique({\n            where: {\n                token\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        role: true,\n                        status: true,\n                        avatar: true\n                    }\n                }\n            }\n        });\n        if (!session || session.expiresAt < new Date()) {\n            // Session expired or not found\n            if (session) {\n                await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n                    where: {\n                        id: session.id\n                    }\n                });\n            }\n            return null;\n        }\n        return session;\n    } catch (error) {\n        return null;\n    }\n}\n/**\n * Delete session\n */ async function deleteSession(token) {\n    if (!token) return;\n    try {\n        await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n            where: {\n                token\n            }\n        });\n    } catch (error) {\n    // Session might not exist, ignore error\n    }\n}\n/**\n * Delete all sessions for user\n */ async function deleteAllUserSessions(userId) {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            userId\n        }\n    });\n}\n/**\n * Clean up expired sessions\n */ async function cleanupExpiredSessions() {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            expiresAt: {\n                lt: new Date()\n            }\n        }\n    });\n}\n/**\n * Set auth cookie\n */ function setAuthCookie(token) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.set(COOKIE_NAME, token, {\n        httpOnly: true,\n        secure: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].NODE_ENV === \"production\",\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\n/**\n * Clear auth cookie\n */ function clearAuthCookie() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.delete(COOKIE_NAME);\n}\n/**\n * Get auth token from cookies\n */ function getAuthToken() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return cookieStore.get(COOKIE_NAME)?.value;\n}\n/**\n * Get current user from session\n */ async function getCurrentUser() {\n    const token = getAuthToken();\n    if (!token) return null;\n    const session = await getSession(token);\n    return session?.user || null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/session.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/config/env.js":
/*!*******************************!*\
  !*** ./src/lib/config/env.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   env: () => (/* binding */ env)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\nconst envSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"development\",\n        \"production\",\n        \"test\"\n    ]).default(\"development\"),\n    DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Database URL is required\"),\n    NEXTAUTH_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"NextAuth secret is required\"),\n    NEXTAUTH_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    JWT_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"JWT secret is required\"),\n    APP_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().default(\"http://localhost:3000\"),\n    APP_NAME: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default(\"Coveyor POC\"),\n    // Email configuration (optional)\n    SMTP_HOST: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_USER: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PASS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    FROM_EMAIL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email().optional()\n});\nfunction validateEnv() {\n    try {\n        return envSchema.parse(process.env);\n    } catch (error) {\n        console.error(\"❌ Invalid environment variables:\");\n        console.error(error.errors);\n        throw new Error(\"Environment validation failed\");\n    }\n}\nconst env = validateEnv();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (env);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2NvbmZpZy9lbnYuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXVCO0FBRXZCLE1BQU1DLFlBQVlELHlDQUFRLENBQUM7SUFDekJHLFVBQVVILDBDQUFNLENBQUM7UUFBQztRQUFlO1FBQWM7S0FBTyxFQUFFSyxPQUFPLENBQUM7SUFDaEVDLGNBQWNOLHlDQUFRLEdBQUdRLEdBQUcsQ0FBQyxHQUFHO0lBQ2hDQyxpQkFBaUJULHlDQUFRLEdBQUdRLEdBQUcsQ0FBQyxHQUFHO0lBQ25DRSxjQUFjVix5Q0FBUSxHQUFHVyxHQUFHLEdBQUdDLFFBQVE7SUFDdkNDLFlBQVliLHlDQUFRLEdBQUdRLEdBQUcsQ0FBQyxHQUFHO0lBQzlCTSxTQUFTZCx5Q0FBUSxHQUFHVyxHQUFHLEdBQUdOLE9BQU8sQ0FBQztJQUNsQ1UsVUFBVWYseUNBQVEsR0FBR0ssT0FBTyxDQUFDO0lBRTdCLGlDQUFpQztJQUNqQ1csV0FBV2hCLHlDQUFRLEdBQUdZLFFBQVE7SUFDOUJLLFdBQVdqQix5Q0FBUSxHQUFHWSxRQUFRO0lBQzlCTSxXQUFXbEIseUNBQVEsR0FBR1ksUUFBUTtJQUM5Qk8sV0FBV25CLHlDQUFRLEdBQUdZLFFBQVE7SUFDOUJRLFlBQVlwQix5Q0FBUSxHQUFHcUIsS0FBSyxHQUFHVCxRQUFRO0FBQ3pDO0FBRUEsU0FBU1U7SUFDUCxJQUFJO1FBQ0YsT0FBT3JCLFVBQVVzQixLQUFLLENBQUNDLFFBQVFDLEdBQUc7SUFDcEMsRUFBRSxPQUFPQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQztRQUNkQyxRQUFRRCxLQUFLLENBQUNBLE1BQU1FLE1BQU07UUFDMUIsTUFBTSxJQUFJQyxNQUFNO0lBQ2xCO0FBQ0Y7QUFFTyxNQUFNSixNQUFNSCxjQUFhO0FBRWhDLGlFQUFlRyxHQUFHQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY292ZXlvci1wb2MvLi9zcmMvbGliL2NvbmZpZy9lbnYuanM/ZmYxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB6IH0gZnJvbSAnem9kJ1xuXG5jb25zdCBlbnZTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIE5PREVfRU5WOiB6LmVudW0oWydkZXZlbG9wbWVudCcsICdwcm9kdWN0aW9uJywgJ3Rlc3QnXSkuZGVmYXVsdCgnZGV2ZWxvcG1lbnQnKSxcbiAgREFUQUJBU0VfVVJMOiB6LnN0cmluZygpLm1pbigxLCAnRGF0YWJhc2UgVVJMIGlzIHJlcXVpcmVkJyksXG4gIE5FWFRBVVRIX1NFQ1JFVDogei5zdHJpbmcoKS5taW4oMSwgJ05leHRBdXRoIHNlY3JldCBpcyByZXF1aXJlZCcpLFxuICBORVhUQVVUSF9VUkw6IHouc3RyaW5nKCkudXJsKCkub3B0aW9uYWwoKSxcbiAgSldUX1NFQ1JFVDogei5zdHJpbmcoKS5taW4oMSwgJ0pXVCBzZWNyZXQgaXMgcmVxdWlyZWQnKSxcbiAgQVBQX1VSTDogei5zdHJpbmcoKS51cmwoKS5kZWZhdWx0KCdodHRwOi8vbG9jYWxob3N0OjMwMDAnKSxcbiAgQVBQX05BTUU6IHouc3RyaW5nKCkuZGVmYXVsdCgnQ292ZXlvciBQT0MnKSxcbiAgXG4gIC8vIEVtYWlsIGNvbmZpZ3VyYXRpb24gKG9wdGlvbmFsKVxuICBTTVRQX0hPU1Q6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgU01UUF9QT1JUOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIFNNVFBfVVNFUjogei5zdHJpbmcoKS5vcHRpb25hbCgpLFxuICBTTVRQX1BBU1M6IHouc3RyaW5nKCkub3B0aW9uYWwoKSxcbiAgRlJPTV9FTUFJTDogei5zdHJpbmcoKS5lbWFpbCgpLm9wdGlvbmFsKCksXG59KVxuXG5mdW5jdGlvbiB2YWxpZGF0ZUVudigpIHtcbiAgdHJ5IHtcbiAgICByZXR1cm4gZW52U2NoZW1hLnBhcnNlKHByb2Nlc3MuZW52KVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBJbnZhbGlkIGVudmlyb25tZW50IHZhcmlhYmxlczonKVxuICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IuZXJyb3JzKVxuICAgIHRocm93IG5ldyBFcnJvcignRW52aXJvbm1lbnQgdmFsaWRhdGlvbiBmYWlsZWQnKVxuICB9XG59XG5cbmV4cG9ydCBjb25zdCBlbnYgPSB2YWxpZGF0ZUVudigpXG5cbmV4cG9ydCBkZWZhdWx0IGVudlxuIl0sIm5hbWVzIjpbInoiLCJlbnZTY2hlbWEiLCJvYmplY3QiLCJOT0RFX0VOViIsImVudW0iLCJkZWZhdWx0IiwiREFUQUJBU0VfVVJMIiwic3RyaW5nIiwibWluIiwiTkVYVEFVVEhfU0VDUkVUIiwiTkVYVEFVVEhfVVJMIiwidXJsIiwib3B0aW9uYWwiLCJKV1RfU0VDUkVUIiwiQVBQX1VSTCIsIkFQUF9OQU1FIiwiU01UUF9IT1NUIiwiU01UUF9QT1JUIiwiU01UUF9VU0VSIiwiU01UUF9QQVNTIiwiRlJPTV9FTUFJTCIsImVtYWlsIiwidmFsaWRhdGVFbnYiLCJwYXJzZSIsInByb2Nlc3MiLCJlbnYiLCJlcnJvciIsImNvbnNvbGUiLCJlcnJvcnMiLCJFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config/env.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/prisma.js":
/*!******************************!*\
  !*** ./src/lib/db/prisma.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3ByaXNtYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUU7QUFFbEUsSUFBSUksSUFBeUIsRUFBYztJQUN6Q0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBQzNCO0FBRUEsaUVBQWVBLE1BQU1BLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3ZleW9yLXBvYy8uL3NyYy9saWIvZGIvcHJpc21hLmpzP2QyZTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXNcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbn1cblxuZXhwb3J0IGRlZmF1bHQgcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/prisma.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute&page=%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Fconveyor-stats%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();