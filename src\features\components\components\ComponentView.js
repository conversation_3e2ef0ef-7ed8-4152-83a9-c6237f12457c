'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Edit, ArrowLeft, Package2, Link } from 'lucide-react'

export default function ComponentView({ component, onEdit, onClose }) {
  if (!component) return null

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatDateTime = (date) => {
    return new Date(date).toLocaleString('en-IN')
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onClose}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to List
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{component.name}</h1>
            <p className="text-gray-600 mt-1">Component Code: {component.code}</p>
          </div>
        </div>
        <Button onClick={onEdit} className="btn-gradient-primary">
          <Edit className="w-4 h-4 mr-2" />
          Edit Component
        </Button>
      </div>

      {/* Basic Information */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package2 className="w-5 h-5 mr-2" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">Component Name</label>
              <p className="text-lg font-semibold text-gray-900">{component.name}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Component Code</label>
              <p className="text-lg font-mono text-gray-900">{component.code}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Category</label>
              <div className="mt-1">
                <Badge variant="outline">{component.category}</Badge>
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Unit of Measure</label>
              <p className="text-lg text-gray-900">{component.uom}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Status</label>
              <div className="mt-1">
                <Badge variant={component.status ? 'success' : 'secondary'}>
                  {component.status ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>

            {component.vendorInfo && (
              <div>
                <label className="text-sm font-medium text-gray-500">Vendor Information</label>
                <p className="text-lg text-gray-900">{component.vendorInfo}</p>
              </div>
            )}
          </div>

          {component.description && (
            <div className="mt-6">
              <label className="text-sm font-medium text-gray-500">Description</label>
              <p className="text-gray-900 mt-1">{component.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Linked Equipment */}
      {component.equipment && (
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Link className="w-5 h-5 mr-2" />
              Linked Equipment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold text-blue-900">{component.equipment.name}</h3>
                  <p className="text-blue-700 font-mono text-sm">{component.equipment.code}</p>
                  {component.equipment.category && (
                    <p className="text-blue-600 text-sm mt-1">
                      Category: {component.equipment.category.name}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Pricing Information */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle>Pricing Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">Base Rate</label>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(component.baseRate)}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Rate Effective From</label>
              <p className="text-lg text-gray-900">{formatDate(component.rateEffectiveFrom)}</p>
            </div>
            
            {component.validUpto && (
              <div>
                <label className="text-sm font-medium text-gray-500">Valid Upto</label>
                <p className="text-lg text-gray-900">{formatDate(component.validUpto)}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Audit Information */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle>Audit Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">Created At</label>
              <p className="text-gray-900">{formatDateTime(component.createdAt)}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Last Updated</label>
              <p className="text-gray-900">{formatDateTime(component.updatedAt)}</p>
            </div>
            
            {component.createdBy && (
              <div>
                <label className="text-sm font-medium text-gray-500">Created By</label>
                <p className="text-gray-900">{component.createdBy}</p>
              </div>
            )}
            
            {component.updatedBy && (
              <div>
                <label className="text-sm font-medium text-gray-500">Updated By</label>
                <p className="text-gray-900">{component.updatedBy}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
