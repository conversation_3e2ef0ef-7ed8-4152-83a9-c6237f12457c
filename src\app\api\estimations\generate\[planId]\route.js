import { getCurrentUser } from '@/lib/auth/session'
import { generateAutomaticEstimation } from '@/features/estimation/services/estimationService'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function POST(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_CREATE)) {
      return forbiddenResponse('Insufficient permissions to generate estimation sheets')
    }

    const planId = params.planId
    
    // Generate automatic estimation
    const estimation = await generateAutomaticEstimation(planId, user.id)
    
    return successResponse(estimation, 'Estimation sheet generated successfully', null, 201)
    
  } catch (error) {
    if (error.message === 'Conveyor plan not found') {
      return notFoundResponse('Conveyor plan not found')
    }
    return handleApiError(error)
  }
}
