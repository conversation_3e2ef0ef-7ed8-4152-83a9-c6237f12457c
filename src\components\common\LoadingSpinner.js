import { cn } from "@/lib/utils/cn"

export function LoadingSpinner({ className, size = "default", ...props }) {
  const sizeClasses = {
    sm: "h-4 w-4",
    default: "h-6 w-6",
    lg: "h-8 w-8",
    xl: "h-12 w-12",
  }

  return (
    <div
      className={cn(
        "animate-spin rounded-full border-2 border-gray-300 border-t-brand-500",
        sizeClasses[size],
        className
      )}
      {...props}
    />
  )
}

export function LoadingCard({ className, ...props }) {
  return (
    <div
      className={cn(
        "card-vibrant p-6 space-y-4",
        className
      )}
      {...props}
    >
      <div className="loading-pulse h-4 w-3/4 rounded"></div>
      <div className="loading-pulse h-4 w-1/2 rounded"></div>
      <div className="loading-pulse h-20 w-full rounded"></div>
    </div>
  )
}

export function LoadingTable({ rows = 5, cols = 4, className, ...props }) {
  return (
    <div className={cn("space-y-3", className)} {...props}>
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex space-x-4">
          {Array.from({ length: cols }).map((_, j) => (
            <div
              key={j}
              className="loading-pulse h-4 flex-1 rounded"
            />
          ))}
        </div>
      ))}
    </div>
  )
}

export function LoadingButton({ children, isLoading, ...props }) {
  return (
    <button
      disabled={isLoading}
      className={cn(
        "inline-flex items-center justify-center",
        isLoading && "opacity-50 cursor-not-allowed"
      )}
      {...props}
    >
      {isLoading && (
        <LoadingSpinner size="sm" className="mr-2" />
      )}
      {children}
    </button>
  )
}
