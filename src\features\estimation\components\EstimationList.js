'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  CheckCircle,
  Calculator,
  FileText
} from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { useToast } from '@/hooks/useToast'

export default function EstimationList({ onCreateNew, onEdit, onView, onGenerateFromPlan }) {
  const [estimations, setEstimations] = useState([])
  const [plans, setPlans] = useState([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({
    search: '',
    planId: '',
    status: '',
    page: 1,
    limit: 10
  })
  const [pagination, setPagination] = useState({})

  const { apiCall } = useApi()
  const { showToast } = useToast()

  // Fetch estimations data
  const fetchEstimations = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value)
      })

      const response = await apiCall(`/api/estimations?${params}`)
      
      if (response.success) {
        setEstimations(response.data)
        setPagination(response.pagination || {})
      }
    } catch (error) {
      showToast('Failed to fetch estimation sheets', 'error')
    } finally {
      setLoading(false)
    }
  }

  // Fetch approved plans for filter
  const fetchPlans = async () => {
    try {
      const response = await apiCall('/api/conveyor-plans?status=Approved&limit=100')
      if (response.success) {
        setPlans(response.data)
      }
    } catch (error) {
      console.error('Failed to fetch plans:', error)
    }
  }

  useEffect(() => {
    fetchEstimations()
  }, [filters])

  useEffect(() => {
    fetchPlans()
  }, [])

  const handleSearch = (value) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }))
  }

  const handleFilter = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }))
  }

  const handleFinalize = async (id) => {
    if (!confirm('Are you sure you want to finalize this estimation? This action cannot be undone.')) return

    try {
      const response = await apiCall(`/api/estimations/${id}/finalize`, 'PUT')
      
      if (response.success) {
        showToast('Estimation finalized successfully', 'success')
        fetchEstimations()
      }
    } catch (error) {
      showToast('Failed to finalize estimation', 'error')
    }
  }

  const handleDelete = async (id) => {
    if (!confirm('Are you sure you want to delete this estimation?')) return

    try {
      const response = await apiCall(`/api/estimations/${id}`, 'DELETE')
      
      if (response.success) {
        showToast('Estimation deleted successfully', 'success')
        fetchEstimations()
      }
    } catch (error) {
      showToast('Failed to delete estimation', 'error')
    }
  }

  const getStatusBadge = (status) => {
    const variants = {
      'Draft': 'secondary',
      'Final': 'success',
      'Sent': 'warning',
      'Approved': 'success'
    }
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-IN')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Estimation Sheets</h1>
          <p className="text-gray-600 mt-1">Generate and manage cost estimations for conveyor projects</p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={onGenerateFromPlan} variant="outline">
            <Calculator className="w-4 h-4 mr-2" />
            Generate from Plan
          </Button>
          <Button onClick={onCreateNew} className="btn-gradient-primary">
            <Plus className="w-4 h-4 mr-2" />
            Create Manual
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="card-vibrant">
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search estimations..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <select
              value={filters.planId}
              onChange={(e) => handleFilter('planId', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Plans</option>
              {plans.map(plan => (
                <option key={plan.id} value={plan.id}>
                  {plan.planName}
                </option>
              ))}
            </select>

            <select
              value={filters.status}
              onChange={(e) => handleFilter('status', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="Draft">Draft</option>
              <option value="Final">Final</option>
              <option value="Sent">Sent</option>
              <option value="Approved">Approved</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Estimations Table */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Estimation Sheets
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan Name</TableHead>
                    <TableHead>Version</TableHead>
                    <TableHead>Conveyor Type</TableHead>
                    <TableHead>Equipment Cost</TableHead>
                    <TableHead>Total Cost</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Generated</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {estimations.map((estimation) => (
                    <TableRow key={estimation.id}>
                      <TableCell className="font-medium">{estimation.plan?.planName}</TableCell>
                      <TableCell>v{estimation.estimationVersion}</TableCell>
                      <TableCell>{estimation.plan?.conveyorType}</TableCell>
                      <TableCell>
                        {estimation.equipmentCost ? formatCurrency(estimation.equipmentCost) : '-'}
                      </TableCell>
                      <TableCell className="font-semibold">
                        {estimation.totalCost ? formatCurrency(estimation.totalCost) : '-'}
                      </TableCell>
                      <TableCell>{getStatusBadge(estimation.status)}</TableCell>
                      <TableCell>{formatDate(estimation.generatedAt)}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onView(estimation)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </DropdownMenuItem>
                            {estimation.status === 'Draft' && (
                              <DropdownMenuItem onClick={() => onEdit(estimation)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                            )}
                            {estimation.status === 'Draft' && (
                              <DropdownMenuItem onClick={() => handleFinalize(estimation.id)}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Finalize
                              </DropdownMenuItem>
                            )}
                            {estimation.status === 'Draft' && (
                              <DropdownMenuItem 
                                onClick={() => handleDelete(estimation.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {estimations.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No estimation sheets found
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
