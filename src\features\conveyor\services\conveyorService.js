import prisma from "@/lib/db/prisma";

/**
 * Get paginated conveyor plans list with filters
 */
export async function getConveyorPlans(filters = {}) {
  const {
    search = "",
    conveyorType,
    loadType,
    status,
    region,
    page = 1,
    limit = 10,
    sortBy = "createdAt",
    sortOrder = "desc",
  } = filters;

  const skip = (page - 1) * limit;

  // Build where clause
  const where = {
    AND: [
      // Search filter
      search
        ? {
            OR: [
              { planName: { contains: search, mode: "insensitive" } },
              { conveyorType: { contains: search, mode: "insensitive" } },
              { siteConditions: { contains: search, mode: "insensitive" } },
              {
                specialRequirements: { contains: search, mode: "insensitive" },
              },
            ],
          }
        : {},
      // Conveyor type filter
      conveyorType
        ? { conveyorType: { contains: conveyorType, mode: "insensitive" } }
        : {},
      // Load type filter
      loadType ? { loadType } : {},
      // Status filter
      status ? { status } : {},
      // Region filter
      region ? { region: { contains: region, mode: "insensitive" } } : {},
    ],
  };

  // Get plans with pagination
  const [plans, total] = await Promise.all([
    prisma.conveyorPlan.findMany({
      where,
      select: {
        id: true,
        planName: true,
        conveyorType: true,
        totalLengthM: true,
        loadType: true,
        capacityTph: true,
        inclinationAngle: true,
        driveType: true,
        environment: true,
        region: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            estimationSheets: true,
          },
        },
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit,
    }),
    prisma.conveyorPlan.count({ where }),
  ]);

  return {
    plans,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    },
  };
}

/**
 * Get conveyor plan by ID
 */
export async function getConveyorPlanById(id) {
  const plan = await prisma.conveyorPlan.findUnique({
    where: { id: parseInt(id) },
    include: {
      planComponents: {
        include: {
          component: true,
        },
      },
      estimationSheets: {
        select: {
          id: true,
          estimationVersion: true,
          totalCost: true,
          status: true,
          generatedAt: true,
        },
        orderBy: { generatedAt: "desc" },
      },
    },
  });

  if (!plan) {
    throw new Error("Conveyor plan not found");
  }

  return plan;
}

/**
 * Create new conveyor plan
 */
export async function createConveyorPlan(planData, createdBy) {
  const {
    planName,
    conveyorType,
    totalLengthM,
    loadType,
    capacityTph,
    inclinationAngle,
    driveType,
    environment,
    region,
    siteConditions,
    specialRequirements,
    status,
    components = [],
  } = planData;

  // Create plan with components in a transaction
  const plan = await prisma.$transaction(async (tx) => {
    // Create the plan
    const newPlan = await tx.conveyorPlan.create({
      data: {
        planName,
        conveyorType,
        totalLengthM,
        loadType,
        capacityTph,
        inclinationAngle,
        driveType,
        environment,
        region,
        siteConditions,
        specialRequirements,
        status,
        createdBy,
      },
    });

    // Create plan components if any
    if (components.length > 0) {
      await tx.planComponent.createMany({
        data: components.map((comp) => ({
          planId: newPlan.id,
          componentId: comp.componentId,
          quantity: comp.quantity,
          unitPrice: comp.unitPrice,
          totalPrice: comp.totalPrice,
          notes: comp.notes,
        })),
      });
    }

    // Return plan with components
    return await tx.conveyorPlan.findUnique({
      where: { id: newPlan.id },
      include: {
        planComponents: {
          include: {
            component: true,
          },
        },
      },
    });
  });

  return plan;
}

/**
 * Update conveyor plan
 */
export async function updateConveyorPlan(id, planData, updatedBy) {
  const {
    planName,
    conveyorType,
    totalLengthM,
    loadType,
    capacityTph,
    inclinationAngle,
    driveType,
    environment,
    region,
    siteConditions,
    specialRequirements,
    status,
    components = [],
  } = planData;

  // Check if plan exists
  const existingPlan = await prisma.conveyorPlan.findUnique({
    where: { id: parseInt(id) },
  });

  if (!existingPlan) {
    throw new Error("Conveyor plan not found");
  }

  // Update plan with components in a transaction
  const plan = await prisma.$transaction(async (tx) => {
    // Update the plan
    const updatedPlan = await tx.conveyorPlan.update({
      where: { id: parseInt(id) },
      data: {
        ...(planName && { planName }),
        ...(conveyorType && { conveyorType }),
        ...(totalLengthM !== undefined && { totalLengthM }),
        ...(loadType && { loadType }),
        ...(capacityTph !== undefined && { capacityTph }),
        ...(inclinationAngle !== undefined && { inclinationAngle }),
        ...(driveType !== undefined && { driveType }),
        ...(environment !== undefined && { environment }),
        ...(region !== undefined && { region }),
        ...(siteConditions !== undefined && { siteConditions }),
        ...(specialRequirements !== undefined && { specialRequirements }),
        ...(status && { status }),
        updatedBy,
      },
    });

    // Update components - delete existing and create new ones
    await tx.planComponent.deleteMany({
      where: { planId: parseInt(id) },
    });

    if (components.length > 0) {
      await tx.planComponent.createMany({
        data: components.map((comp) => ({
          planId: parseInt(id),
          componentId: comp.componentId,
          quantity: comp.quantity,
          unitPrice: comp.unitPrice,
          totalPrice: comp.totalPrice,
          notes: comp.notes,
        })),
      });
    }

    // Return plan with components
    return await tx.conveyorPlan.findUnique({
      where: { id: parseInt(id) },
      include: {
        planComponents: {
          include: {
            component: true,
          },
        },
      },
    });
  });

  return plan;
}

/**
 * Delete conveyor plan
 */
export async function deleteConveyorPlan(id, deletedBy) {
  // Check if plan exists
  const plan = await prisma.conveyorPlan.findUnique({
    where: { id: parseInt(id) },
    include: {
      _count: {
        select: {
          estimationSheets: true,
        },
      },
    },
  });

  if (!plan) {
    throw new Error("Conveyor plan not found");
  }

  // Check if plan has estimation sheets
  if (plan._count.estimationSheets > 0) {
    throw new Error(
      "Cannot delete plan that has estimation sheets associated with it"
    );
  }

  // Delete plan
  await prisma.conveyorPlan.delete({
    where: { id: parseInt(id) },
  });

  return { message: "Conveyor plan deleted successfully" };
}

/**
 * Submit conveyor plan for approval
 */
export async function submitConveyorPlan(id, submittedBy) {
  const plan = await prisma.conveyorPlan.findUnique({
    where: { id: parseInt(id) },
  });

  if (!plan) {
    throw new Error("Conveyor plan not found");
  }

  if (plan.status !== "Draft") {
    throw new Error("Only draft plans can be submitted");
  }

  const updatedPlan = await prisma.conveyorPlan.update({
    where: { id: parseInt(id) },
    data: {
      status: "Submitted",
      updatedBy: submittedBy,
    },
  });

  return updatedPlan;
}

/**
 * Approve conveyor plan
 */
export async function approveConveyorPlan(id, approvedBy) {
  const plan = await prisma.conveyorPlan.findUnique({
    where: { id: parseInt(id) },
  });

  if (!plan) {
    throw new Error("Conveyor plan not found");
  }

  if (plan.status !== "Submitted") {
    throw new Error("Only submitted plans can be approved");
  }

  const updatedPlan = await prisma.conveyorPlan.update({
    where: { id: parseInt(id) },
    data: {
      status: "Approved",
      updatedBy: approvedBy,
    },
  });

  return updatedPlan;
}

/**
 * Reject conveyor plan
 */
export async function rejectConveyorPlan(id, rejectedBy) {
  const plan = await prisma.conveyorPlan.findUnique({
    where: { id: parseInt(id) },
  });

  if (!plan) {
    throw new Error("Conveyor plan not found");
  }

  if (plan.status !== "Submitted") {
    throw new Error("Only submitted plans can be rejected");
  }

  const updatedPlan = await prisma.conveyorPlan.update({
    where: { id: parseInt(id) },
    data: {
      status: "Rejected",
      updatedBy: rejectedBy,
    },
  });

  return updatedPlan;
}

/**
 * Get conveyor plan statistics
 */
export async function getConveyorPlanStats() {
  const [
    totalPlans,
    draftPlans,
    submittedPlans,
    approvedPlans,
    rejectedPlans,
    recentPlans,
  ] = await Promise.all([
    prisma.conveyorPlan.count(),
    prisma.conveyorPlan.count({ where: { status: "Draft" } }),
    prisma.conveyorPlan.count({ where: { status: "Submitted" } }),
    prisma.conveyorPlan.count({ where: { status: "Approved" } }),
    prisma.conveyorPlan.count({ where: { status: "Rejected" } }),
    prisma.conveyorPlan.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
      },
    }),
  ]);

  return {
    totalPlans,
    draftPlans,
    submittedPlans,
    approvedPlans,
    rejectedPlans,
    recentPlans,
  };
}
