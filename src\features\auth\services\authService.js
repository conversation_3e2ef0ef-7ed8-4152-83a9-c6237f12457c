import prisma from '@/lib/db/prisma'
import { hashPassword, verifyPassword } from '@/lib/utils/encryption'
import { createSession, deleteSession, deleteAllUserSessions } from '@/lib/auth/session'

/**
 * Authenticate user with email and password
 */
export async function authenticateUser(email, password, userAgent = null, ipAddress = null) {
  // Find user by email
  const user = await prisma.user.findUnique({
    where: { email: email.toLowerCase() },
    select: {
      id: true,
      email: true,
      password: true,
      firstName: true,
      lastName: true,
      role: true,
      status: true,
      avatar: true,
    }
  })

  if (!user) {
    throw new Error('Invalid credentials')
  }

  // Check if user is active
  if (user.status !== 'ACTIVE') {
    throw new Error('Account is not active')
  }

  // Verify password
  const isValidPassword = await verifyPassword(password, user.password)
  if (!isValidPassword) {
    throw new Error('Invalid credentials')
  }

  // Create session
  const { session, token } = await createSession(user.id, userAgent, ipAddress)

  // Update last login time
  await prisma.user.update({
    where: { id: user.id },
    data: { lastLoginAt: new Date() }
  })

  // Log authentication
  await prisma.auditLog.create({
    data: {
      userId: user.id,
      action: 'LOGIN',
      resource: 'AUTH',
      details: { method: 'email' },
      ipAddress,
      userAgent,
    }
  })

  // Return user data without password
  const { password: _, ...userWithoutPassword } = user

  return {
    user: userWithoutPassword,
    session,
    token
  }
}

/**
 * Create new user account
 */
export async function createUser(userData) {
  const { email, password, firstName, lastName, role = 'USER', invitedBy = null } = userData

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email: email.toLowerCase() }
  })

  if (existingUser) {
    throw new Error('User already exists')
  }

  // Hash password
  const hashedPassword = await hashPassword(password)

  // Create user
  const user = await prisma.user.create({
    data: {
      email: email.toLowerCase(),
      password: hashedPassword,
      firstName,
      lastName,
      role,
      status: 'ACTIVE',
      invitedBy,
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      status: true,
      avatar: true,
      createdAt: true,
    }
  })

  // Log user creation
  await prisma.auditLog.create({
    data: {
      userId: invitedBy,
      action: 'CREATE',
      resource: 'USER',
      resourceId: user.id,
      details: { 
        email: user.email,
        role: user.role 
      },
    }
  })

  return user
}

/**
 * Logout user
 */
export async function logoutUser(token, userId = null) {
  // Delete session
  await deleteSession(token)

  // Log logout
  if (userId) {
    await prisma.auditLog.create({
      data: {
        userId,
        action: 'LOGOUT',
        resource: 'AUTH',
        details: { method: 'manual' },
      }
    })
  }
}

/**
 * Logout user from all devices
 */
export async function logoutUserFromAllDevices(userId) {
  // Delete all user sessions
  await deleteAllUserSessions(userId)

  // Log logout from all devices
  await prisma.auditLog.create({
    data: {
      userId,
      action: 'LOGOUT_ALL',
      resource: 'AUTH',
      details: { method: 'all_devices' },
    }
  })
}

/**
 * Change user password
 */
export async function changeUserPassword(userId, currentPassword, newPassword) {
  // Get user with current password
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { id: true, password: true }
  })

  if (!user) {
    throw new Error('User not found')
  }

  // Verify current password
  const isValidPassword = await verifyPassword(currentPassword, user.password)
  if (!isValidPassword) {
    throw new Error('Current password is incorrect')
  }

  // Hash new password
  const hashedNewPassword = await hashPassword(newPassword)

  // Update password
  await prisma.user.update({
    where: { id: userId },
    data: { password: hashedNewPassword }
  })

  // Log password change
  await prisma.auditLog.create({
    data: {
      userId,
      action: 'PASSWORD_CHANGE',
      resource: 'AUTH',
      details: { method: 'self' },
    }
  })

  // Logout from all other devices for security
  await logoutUserFromAllDevices(userId)
}

/**
 * Update user profile
 */
export async function updateUserProfile(userId, profileData) {
  const { firstName, lastName, email } = profileData

  // Check if email is already taken by another user
  if (email) {
    const existingUser = await prisma.user.findFirst({
      where: {
        email: email.toLowerCase(),
        NOT: { id: userId }
      }
    })

    if (existingUser) {
      throw new Error('Email is already taken')
    }
  }

  // Update user profile
  const updatedUser = await prisma.user.update({
    where: { id: userId },
    data: {
      firstName,
      lastName,
      email: email?.toLowerCase(),
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      status: true,
      avatar: true,
    }
  })

  // Log profile update
  await prisma.auditLog.create({
    data: {
      userId,
      action: 'PROFILE_UPDATE',
      resource: 'USER',
      resourceId: userId,
      details: { 
        fields: Object.keys(profileData)
      },
    }
  })

  return updatedUser
}
