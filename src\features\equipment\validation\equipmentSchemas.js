import { z } from 'zod'

/**
 * Equipment Category validation schema
 */
export const createEquipmentCategorySchema = z.object({
  name: z
    .string()
    .min(1, 'Category name is required')
    .min(2, 'Category name must be at least 2 characters')
    .max(100, 'Category name must be less than 100 characters'),
  description: z
    .string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  status: z.boolean().default(true),
})

export const updateEquipmentCategorySchema = createEquipmentCategorySchema.partial()

/**
 * Equipment Master validation schema
 */
export const createEquipmentSchema = z.object({
  name: z
    .string()
    .min(1, 'Equipment name is required')
    .min(2, 'Equipment name must be at least 2 characters')
    .max(150, 'Equipment name must be less than 150 characters'),
  code: z
    .string()
    .min(1, 'Equipment code is required')
    .min(2, 'Equipment code must be at least 2 characters')
    .max(100, 'Equipment code must be less than 100 characters')
    .regex(/^[A-Z0-9_-]+$/, 'Equipment code must contain only uppercase letters, numbers, underscores, and hyphens'),
  categoryId: z
    .number()
    .int()
    .positive('Category is required'),
  subCategory: z
    .string()
    .max(100, 'Sub-category must be less than 100 characters')
    .optional(),
  description: z
    .string()
    .max(1000, 'Description must be less than 1000 characters')
    .optional(),
  specifications: z
    .record(z.any())
    .optional(),
  uom: z
    .string()
    .min(1, 'Unit of measure is required')
    .max(50, 'Unit of measure must be less than 50 characters'),
  baseRate: z
    .number()
    .positive('Base rate must be a positive number')
    .max(999999999999, 'Base rate is too large'),
  regionRates: z
    .record(z.number().positive())
    .optional(),
  effectiveFrom: z
    .string()
    .datetime('Invalid effective from date')
    .or(z.date()),
  validUpto: z
    .string()
    .datetime('Invalid valid upto date')
    .or(z.date())
    .optional(),
  costBreakup: z
    .object({
      material: z.number().optional(),
      labor: z.number().optional(),
      overhead: z.number().optional(),
      profit: z.number().optional(),
    })
    .optional(),
  status: z.boolean().default(true),
})

export const updateEquipmentSchema = createEquipmentSchema.partial()

/**
 * Equipment search/filter validation schema
 */
export const equipmentFilterSchema = z.object({
  search: z.string().optional(),
  categoryId: z.coerce.number().int().positive().optional(),
  status: z.boolean().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  sortBy: z.enum(['name', 'code', 'baseRate', 'effectiveFrom', 'createdAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

/**
 * Equipment Category filter validation schema
 */
export const equipmentCategoryFilterSchema = z.object({
  search: z.string().optional(),
  status: z.boolean().optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  sortBy: z.enum(['name', 'createdAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})
