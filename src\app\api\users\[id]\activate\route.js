import { getCurrentUser } from '@/lib/auth/session'
import { activateUser } from '@/features/users/services/userService'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_ACTIVATE)) {
      return forbiddenResponse('Insufficient permissions to activate user')
    }

    const userId = params.id
    
    // Activate user
    const activatedUser = await activateUser(userId, user.id)
    
    return successResponse(activatedUser, 'User activated successfully')
    
  } catch (error) {
    if (error.message === 'User not found') {
      return notFoundResponse('User not found')
    }
    return handleApiError(error)
  }
}
