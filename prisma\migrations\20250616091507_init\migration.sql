-- CreateEnum
CREATE TYPE "UserRole" AS ENUM ('ADMIN', 'MANAGER', 'USER');

-- CreateEnum
CREATE TYPE "UserStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'PENDING', 'SUSPENDED');

-- CreateEnum
CREATE TYPE "ConveyorType" AS ENUM ('BELT', 'ROLLER', 'SCREW', 'CHAIN', 'PNEUMATIC');

-- CreateEnum
CREATE TYPE "LoadType" AS ENUM ('LIGHT', 'MEDIUM', 'HEAVY');

-- CreateEnum
CREATE TYPE "DriveType" AS ENUM ('MOTORIZED', 'MANUAL', 'GRAVITY');

-- Create<PERSON>num
CREATE TYPE "PlanStatus" AS ENUM ('DRAFT', 'SUBMITTED', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "EstimationStatus" AS ENUM ('DRAFT', 'FINAL', 'SENT', 'APPROVED');

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "first_name" TEXT,
    "last_name" TEXT,
    "role" "UserRole" NOT NULL DEFAULT 'USER',
    "status" "UserStatus" NOT NULL DEFAULT 'ACTIVE',
    "avatar" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "last_login_at" TIMESTAMP(3),
    "invited_by" TEXT,

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sessions" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires_at" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "user_agent" TEXT,
    "ip_address" TEXT,

    CONSTRAINT "sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "audit_logs" (
    "id" TEXT NOT NULL,
    "user_id" TEXT,
    "action" TEXT NOT NULL,
    "resource" TEXT NOT NULL,
    "resource_id" TEXT,
    "details" JSONB,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "audit_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "equipment_categories" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "status" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "equipment_categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "equipment_master" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "category_id" INTEGER NOT NULL,
    "sub_category" TEXT,
    "description" TEXT,
    "specifications" JSONB,
    "uom" TEXT NOT NULL,
    "base_rate" DECIMAL(12,2) NOT NULL,
    "region_rates" JSONB,
    "effective_from" TIMESTAMP(3) NOT NULL,
    "valid_upto" TIMESTAMP(3),
    "cost_breakup" JSONB,
    "status" BOOLEAN NOT NULL DEFAULT true,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "equipment_master_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "component_master" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "equipment_id" INTEGER NOT NULL,
    "description" TEXT,
    "uom" TEXT NOT NULL,
    "base_rate" DECIMAL(12,2) NOT NULL,
    "rate_effective_from" TIMESTAMP(3) NOT NULL,
    "valid_upto" TIMESTAMP(3),
    "vendor_info" TEXT,
    "status" BOOLEAN NOT NULL DEFAULT true,
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "component_master_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "conveyor_plans" (
    "id" SERIAL NOT NULL,
    "plan_name" TEXT NOT NULL,
    "conveyor_type" TEXT NOT NULL,
    "total_length_m" DECIMAL(10,2) NOT NULL,
    "load_type" TEXT NOT NULL,
    "capacity_tph" DECIMAL(10,2),
    "inclination_angle" DECIMAL(5,2),
    "drive_type" TEXT,
    "environment" TEXT,
    "region" TEXT,
    "site_conditions" TEXT,
    "special_requirements" TEXT,
    "status" TEXT NOT NULL DEFAULT 'Draft',
    "created_by" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by" TEXT,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "conveyor_plans_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "estimation_sheets" (
    "id" SERIAL NOT NULL,
    "plan_id" INTEGER NOT NULL,
    "estimation_version" INTEGER NOT NULL DEFAULT 1,
    "equipment_cost" DECIMAL(14,2),
    "component_cost" DECIMAL(14,2),
    "labor_cost" DECIMAL(14,2),
    "overhead_cost" DECIMAL(14,2),
    "discount_percent" DECIMAL(5,2),
    "markup_percent" DECIMAL(5,2),
    "total_cost" DECIMAL(14,2),
    "notes" TEXT,
    "status" TEXT NOT NULL DEFAULT 'Draft',
    "generated_by" TEXT,
    "generated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "estimation_sheets_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "sessions_token_key" ON "sessions"("token");

-- CreateIndex
CREATE UNIQUE INDEX "equipment_categories_name_key" ON "equipment_categories"("name");

-- CreateIndex
CREATE UNIQUE INDEX "equipment_master_code_key" ON "equipment_master"("code");

-- CreateIndex
CREATE UNIQUE INDEX "component_master_code_key" ON "component_master"("code");

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_invited_by_fkey" FOREIGN KEY ("invited_by") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "equipment_master" ADD CONSTRAINT "equipment_master_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "equipment_categories"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "component_master" ADD CONSTRAINT "component_master_equipment_id_fkey" FOREIGN KEY ("equipment_id") REFERENCES "equipment_master"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "estimation_sheets" ADD CONSTRAINT "estimation_sheets_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "conveyor_plans"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
