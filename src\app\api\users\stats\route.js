import { getCurrentUser } from '@/lib/auth/session'
import { getUserStats } from '@/features/users/services/userService'
import { successResponse, unauthorizedResponse, forbiddenResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission - only managers and admins can view stats
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view user statistics')
    }

    // Get user statistics
    const stats = await getUserStats()
    
    return successResponse(stats, 'User statistics retrieved successfully')
    
  } catch (error) {
    return handleApiError(error)
  }
}
