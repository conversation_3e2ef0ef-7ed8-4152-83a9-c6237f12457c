import { getCurrentUser } from '@/lib/auth/session'
import { successResponse, unauthorizedResponse, handleApiError } from '@/lib/api/response'

export async function GET(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Not authenticated')
    }
    
    return successResponse(user, 'User retrieved successfully')
    
  } catch (error) {
    return handleApiError(error)
  }
}
