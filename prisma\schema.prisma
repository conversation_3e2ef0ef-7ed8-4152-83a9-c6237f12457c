// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String?  @map("first_name")
  lastName  String?  @map("last_name")
  role      UserRole @default(USER)
  status    UserStatus @default(ACTIVE)
  avatar    String?
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  lastLoginAt DateTime? @map("last_login_at")
  
  // Relations
  sessions Session[]
  invitedBy String? @map("invited_by")
  inviter   User?   @relation("UserInvitations", fields: [invitedBy], references: [id])
  invitees  User[]  @relation("UserInvitations")
  
  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  token     String   @unique
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  // Device/browser info
  userAgent String? @map("user_agent")
  ipAddress String? @map("ip_address")
  
  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?  @map("user_id")
  action    String
  resource  String
  resourceId String? @map("resource_id")
  details   Json?
  ipAddress String? @map("ip_address")
  userAgent String? @map("user_agent")
  createdAt DateTime @default(now()) @map("created_at")
  
  @@map("audit_logs")
}

enum UserRole {
  ADMIN
  MANAGER
  USER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
}
