import { getCurrentUser } from '@/lib/auth/session'
import { finalizeEstimationSheet } from '@/features/estimation/services/estimationService'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_UPDATE)) {
      return forbiddenResponse('Insufficient permissions to finalize estimation sheet')
    }

    const estimationId = params.id
    
    // Finalize estimation sheet
    const finalizedEstimation = await finalizeEstimationSheet(estimationId, user.id)
    
    return successResponse(finalizedEstimation, 'Estimation sheet finalized successfully')
    
  } catch (error) {
    if (error.message === 'Estimation sheet not found') {
      return notFoundResponse('Estimation sheet not found')
    }
    return handleApiError(error)
  }
}
