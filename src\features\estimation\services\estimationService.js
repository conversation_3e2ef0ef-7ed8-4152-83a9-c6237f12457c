import prisma from '@/lib/db/prisma'

/**
 * Get paginated estimation sheets list with filters
 */
export async function getEstimationSheets(filters = {}) {
  const {
    search = '',
    planId,
    status,
    page = 1,
    limit = 10,
    sortBy = 'generatedAt',
    sortOrder = 'desc'
  } = filters

  const skip = (page - 1) * limit

  // Build where clause
  const where = {
    AND: [
      // Search filter
      search ? {
        OR: [
          { notes: { contains: search, mode: 'insensitive' } },
          { plan: { planName: { contains: search, mode: 'insensitive' } } },
        ]
      } : {},
      // Plan filter
      planId ? { planId: parseInt(planId) } : {},
      // Status filter
      status ? { status } : {},
    ]
  }

  // Get estimation sheets with pagination
  const [estimations, total] = await Promise.all([
    prisma.estimationSheet.findMany({
      where,
      select: {
        id: true,
        estimationVersion: true,
        equipmentCost: true,
        componentCost: true,
        laborCost: true,
        overheadCost: true,
        discountPercent: true,
        markupPercent: true,
        totalCost: true,
        status: true,
        generatedAt: true,
        plan: {
          select: {
            id: true,
            planName: true,
            conveyorType: true,
            totalLengthM: true,
            loadType: true,
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit,
    }),
    prisma.estimationSheet.count({ where })
  ])

  return {
    estimations,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    }
  }
}

/**
 * Get estimation sheet by ID
 */
export async function getEstimationSheetById(id) {
  const estimation = await prisma.estimationSheet.findUnique({
    where: { id: parseInt(id) },
    include: {
      plan: {
        include: {
          estimationSheets: {
            select: {
              id: true,
              estimationVersion: true,
              totalCost: true,
              status: true,
              generatedAt: true,
            },
            orderBy: { generatedAt: 'desc' }
          }
        }
      }
    }
  })

  if (!estimation) {
    throw new Error('Estimation sheet not found')
  }

  return estimation
}

/**
 * Create new estimation sheet
 */
export async function createEstimationSheet(estimationData, generatedBy) {
  const {
    planId,
    estimationVersion,
    equipmentCost,
    componentCost,
    laborCost,
    overheadCost,
    discountPercent,
    markupPercent,
    totalCost,
    notes,
    status
  } = estimationData

  // Verify plan exists
  const plan = await prisma.conveyorPlan.findUnique({
    where: { id: parseInt(planId) }
  })

  if (!plan) {
    throw new Error('Conveyor plan not found')
  }

  // Get next version number if not provided
  let version = estimationVersion
  if (!version) {
    const lastEstimation = await prisma.estimationSheet.findFirst({
      where: { planId: parseInt(planId) },
      orderBy: { estimationVersion: 'desc' }
    })
    version = lastEstimation ? lastEstimation.estimationVersion + 1 : 1
  }

  // Create estimation sheet
  const estimation = await prisma.estimationSheet.create({
    data: {
      planId: parseInt(planId),
      estimationVersion: version,
      equipmentCost,
      componentCost,
      laborCost,
      overheadCost,
      discountPercent,
      markupPercent,
      totalCost,
      notes,
      status,
      generatedBy,
    },
    include: {
      plan: {
        select: {
          id: true,
          planName: true,
          conveyorType: true,
        }
      }
    }
  })

  return estimation
}

/**
 * Update estimation sheet
 */
export async function updateEstimationSheet(id, estimationData, updatedBy) {
  const {
    equipmentCost,
    componentCost,
    laborCost,
    overheadCost,
    discountPercent,
    markupPercent,
    totalCost,
    notes,
    status
  } = estimationData

  // Check if estimation exists
  const existingEstimation = await prisma.estimationSheet.findUnique({
    where: { id: parseInt(id) }
  })

  if (!existingEstimation) {
    throw new Error('Estimation sheet not found')
  }

  // Update estimation sheet
  const estimation = await prisma.estimationSheet.update({
    where: { id: parseInt(id) },
    data: {
      ...(equipmentCost !== undefined && { equipmentCost }),
      ...(componentCost !== undefined && { componentCost }),
      ...(laborCost !== undefined && { laborCost }),
      ...(overheadCost !== undefined && { overheadCost }),
      ...(discountPercent !== undefined && { discountPercent }),
      ...(markupPercent !== undefined && { markupPercent }),
      ...(totalCost !== undefined && { totalCost }),
      ...(notes !== undefined && { notes }),
      ...(status && { status }),
    },
    include: {
      plan: {
        select: {
          id: true,
          planName: true,
          conveyorType: true,
        }
      }
    }
  })

  return estimation
}

/**
 * Delete estimation sheet
 */
export async function deleteEstimationSheet(id, deletedBy) {
  // Check if estimation exists
  const estimation = await prisma.estimationSheet.findUnique({
    where: { id: parseInt(id) }
  })

  if (!estimation) {
    throw new Error('Estimation sheet not found')
  }

  // Delete estimation sheet
  await prisma.estimationSheet.delete({
    where: { id: parseInt(id) }
  })

  return { message: 'Estimation sheet deleted successfully' }
}

/**
 * Generate automatic estimation based on plan
 */
export async function generateAutomaticEstimation(planId, generatedBy) {
  // Get plan details
  const plan = await prisma.conveyorPlan.findUnique({
    where: { id: parseInt(planId) }
  })

  if (!plan) {
    throw new Error('Conveyor plan not found')
  }

  // Get equipment based on conveyor type
  const equipment = await prisma.equipmentMaster.findMany({
    where: {
      status: true,
      OR: [
        { name: { contains: plan.conveyorType.split(' ')[0], mode: 'insensitive' } },
        { category: { name: { contains: 'Conveyor', mode: 'insensitive' } } }
      ]
    },
    take: 5 // Limit to top 5 relevant equipment
  })

  // Get components for the equipment
  const components = await prisma.componentMaster.findMany({
    where: {
      status: true,
      equipment: {
        id: { in: equipment.map(e => e.id) }
      }
    },
    take: 10 // Limit to top 10 relevant components
  })

  // Calculate costs
  const equipmentCost = equipment.reduce((total, item) => {
    // Simple calculation: base rate * length factor
    const lengthFactor = Math.max(1, plan.totalLengthM / 100)
    return total + (parseFloat(item.baseRate) * lengthFactor)
  }, 0)

  const componentCost = components.reduce((total, item) => {
    // Simple calculation: base rate * length factor
    const lengthFactor = Math.max(1, plan.totalLengthM / 50)
    return total + (parseFloat(item.baseRate) * lengthFactor)
  }, 0)

  const materialCost = equipmentCost + componentCost
  const laborCost = materialCost * 0.15 // 15% of material cost
  const overheadCost = (materialCost + laborCost) * 0.10 // 10% of material + labor

  const subtotal = materialCost + laborCost + overheadCost
  const markupPercent = 20 // 20% markup
  const totalCost = subtotal * (1 + markupPercent / 100)

  // Get next version number
  const lastEstimation = await prisma.estimationSheet.findFirst({
    where: { planId: parseInt(planId) },
    orderBy: { estimationVersion: 'desc' }
  })
  const version = lastEstimation ? lastEstimation.estimationVersion + 1 : 1

  // Create estimation
  const estimation = await createEstimationSheet({
    planId: parseInt(planId),
    estimationVersion: version,
    equipmentCost,
    componentCost,
    laborCost,
    overheadCost,
    discountPercent: 0,
    markupPercent,
    totalCost,
    notes: 'Auto-generated estimation based on plan specifications',
    status: 'Draft'
  }, generatedBy)

  return estimation
}

/**
 * Finalize estimation sheet
 */
export async function finalizeEstimationSheet(id, finalizedBy) {
  const estimation = await prisma.estimationSheet.findUnique({
    where: { id: parseInt(id) }
  })

  if (!estimation) {
    throw new Error('Estimation sheet not found')
  }

  if (estimation.status !== 'Draft') {
    throw new Error('Only draft estimations can be finalized')
  }

  const finalizedEstimation = await prisma.estimationSheet.update({
    where: { id: parseInt(id) },
    data: { status: 'Final' }
  })

  return finalizedEstimation
}

/**
 * Get estimation statistics
 */
export async function getEstimationStats() {
  const [
    totalEstimations,
    draftEstimations,
    finalEstimations,
    sentEstimations,
    approvedEstimations,
    recentEstimations
  ] = await Promise.all([
    prisma.estimationSheet.count(),
    prisma.estimationSheet.count({ where: { status: 'Draft' } }),
    prisma.estimationSheet.count({ where: { status: 'Final' } }),
    prisma.estimationSheet.count({ where: { status: 'Sent' } }),
    prisma.estimationSheet.count({ where: { status: 'Approved' } }),
    prisma.estimationSheet.count({
      where: {
        generatedAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      }
    })
  ])

  return {
    totalEstimations,
    draftEstimations,
    finalEstimations,
    sentEstimations,
    approvedEstimations,
    recentEstimations
  }
}
