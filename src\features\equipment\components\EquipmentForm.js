'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { createEquipmentSchema, updateEquipmentSchema } from '@/features/equipment/validation/equipmentSchemas'
import { useApi } from '@/hooks/useApi'
import { useToast } from '@/hooks/useToast'

export default function EquipmentForm({ equipment, onSave, onCancel }) {
  const [categories, setCategories] = useState([])
  const [loading, setLoading] = useState(false)
  const [specificationsJson, setSpecificationsJson] = useState('')
  const [regionRatesJson, setRegionRatesJson] = useState('')
  const [costBreakupJson, setCostBreakupJson] = useState('')

  const isEdit = !!equipment
  const schema = isEdit ? updateEquipmentSchema : createEquipmentSchema

  const { apiCall } = useApi()
  const { showToast } = useToast()

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: equipment || {
      status: true,
      effectiveFrom: new Date().toISOString().split('T')[0]
    }
  })

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await apiCall('/api/equipment/categories/active')
        if (response.success) {
          setCategories(response.data)
        }
      } catch (error) {
        console.error('Failed to fetch categories:', error)
      }
    }
    fetchCategories()
  }, [])

  // Initialize JSON fields for edit mode
  useEffect(() => {
    if (equipment) {
      setSpecificationsJson(equipment.specifications ? JSON.stringify(equipment.specifications, null, 2) : '')
      setRegionRatesJson(equipment.regionRates ? JSON.stringify(equipment.regionRates, null, 2) : '')
      setCostBreakupJson(equipment.costBreakup ? JSON.stringify(equipment.costBreakup, null, 2) : '')
    }
  }, [equipment])

  const onSubmit = async (data) => {
    try {
      setLoading(true)

      // Parse JSON fields
      let specifications = null
      let regionRates = null
      let costBreakup = null

      if (specificationsJson.trim()) {
        try {
          specifications = JSON.parse(specificationsJson)
        } catch (e) {
          showToast('Invalid specifications JSON format', 'error')
          return
        }
      }

      if (regionRatesJson.trim()) {
        try {
          regionRates = JSON.parse(regionRatesJson)
        } catch (e) {
          showToast('Invalid region rates JSON format', 'error')
          return
        }
      }

      if (costBreakupJson.trim()) {
        try {
          costBreakup = JSON.parse(costBreakupJson)
        } catch (e) {
          showToast('Invalid cost breakup JSON format', 'error')
          return
        }
      }

      const payload = {
        ...data,
        specifications,
        regionRates,
        costBreakup,
        baseRate: parseFloat(data.baseRate),
        categoryId: parseInt(data.categoryId)
      }

      const url = isEdit ? `/api/equipment/${equipment.id}` : '/api/equipment'
      const method = isEdit ? 'PUT' : 'POST'

      const response = await apiCall(url, method, payload)

      if (response.success) {
        showToast(
          isEdit ? 'Equipment updated successfully' : 'Equipment created successfully',
          'success'
        )
        onSave(response.data)
      }
    } catch (error) {
      showToast('Failed to save equipment', 'error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isEdit ? 'Edit Equipment' : 'Add New Equipment'}
          </h1>
          <p className="text-gray-600 mt-1">
            {isEdit ? 'Update equipment details' : 'Create a new equipment entry'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Equipment Name *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Enter equipment name"
                />
                {errors.name && (
                  <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="code">Equipment Code *</Label>
                <Input
                  id="code"
                  {...register('code')}
                  placeholder="Enter equipment code (e.g., BELT_001)"
                  className="font-mono"
                />
                {errors.code && (
                  <p className="text-red-500 text-sm mt-1">{errors.code.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="categoryId">Category *</Label>
                <select
                  id="categoryId"
                  {...register('categoryId')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
                {errors.categoryId && (
                  <p className="text-red-500 text-sm mt-1">{errors.categoryId.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="subCategory">Sub Category</Label>
                <Input
                  id="subCategory"
                  {...register('subCategory')}
                  placeholder="Enter sub category (optional)"
                />
              </div>

              <div>
                <Label htmlFor="uom">Unit of Measure *</Label>
                <Input
                  id="uom"
                  {...register('uom')}
                  placeholder="e.g., meter, piece, kg"
                />
                {errors.uom && (
                  <p className="text-red-500 text-sm mt-1">{errors.uom.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="baseRate">Base Rate (₹) *</Label>
                <Input
                  id="baseRate"
                  type="number"
                  step="0.01"
                  {...register('baseRate')}
                  placeholder="Enter base rate"
                />
                {errors.baseRate && (
                  <p className="text-red-500 text-sm mt-1">{errors.baseRate.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="effectiveFrom">Effective From *</Label>
                <Input
                  id="effectiveFrom"
                  type="date"
                  {...register('effectiveFrom')}
                />
                {errors.effectiveFrom && (
                  <p className="text-red-500 text-sm mt-1">{errors.effectiveFrom.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="validUpto">Valid Upto</Label>
                <Input
                  id="validUpto"
                  type="date"
                  {...register('validUpto')}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Enter equipment description"
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="status"
                checked={watch('status')}
                onCheckedChange={(checked) => setValue('status', checked)}
              />
              <Label htmlFor="status">Active Status</Label>
            </div>
          </CardContent>
        </Card>

        {/* Advanced Information */}
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Advanced Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="specifications">Specifications (JSON)</Label>
              <Textarea
                id="specifications"
                value={specificationsJson}
                onChange={(e) => setSpecificationsJson(e.target.value)}
                placeholder='{"voltage": "220V", "power": "5HP", "size": "1000x500mm"}'
                rows={4}
                className="font-mono text-sm"
              />
              <p className="text-gray-500 text-sm mt-1">
                Enter specifications in JSON format (optional)
              </p>
            </div>

            <div>
              <Label htmlFor="regionRates">Region-specific Rates (JSON)</Label>
              <Textarea
                id="regionRates"
                value={regionRatesJson}
                onChange={(e) => setRegionRatesJson(e.target.value)}
                placeholder='{"North": 1000, "South": 950, "East": 980, "West": 1020}'
                rows={4}
                className="font-mono text-sm"
              />
              <p className="text-gray-500 text-sm mt-1">
                Enter region-specific pricing in JSON format (optional)
              </p>
            </div>

            <div>
              <Label htmlFor="costBreakup">Cost Breakup (JSON)</Label>
              <Textarea
                id="costBreakup"
                value={costBreakupJson}
                onChange={(e) => setCostBreakupJson(e.target.value)}
                placeholder='{"material": 500, "labor": 200, "overhead": 100, "profit": 200}'
                rows={4}
                className="font-mono text-sm"
              />
              <p className="text-gray-500 text-sm mt-1">
                Enter cost breakup in JSON format (optional)
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading} className="btn-gradient-primary">
            {loading ? 'Saving...' : (isEdit ? 'Update Equipment' : 'Create Equipment')}
          </Button>
        </div>
      </form>
    </div>
  )
}
