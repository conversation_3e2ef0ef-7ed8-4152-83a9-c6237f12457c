import prisma from '@/lib/db/prisma'

/**
 * Get paginated components list with filters
 */
export async function getComponents(filters = {}) {
  const {
    search = '',
    category,
    equipmentId,
    status,
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = filters

  const skip = (page - 1) * limit

  // Build where clause
  const where = {
    AND: [
      // Search filter
      search ? {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { code: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { vendorInfo: { contains: search, mode: 'insensitive' } },
        ]
      } : {},
      // Category filter
      category ? { category: { contains: category, mode: 'insensitive' } } : {},
      // Equipment filter
      equipmentId ? { equipmentId: parseInt(equipmentId) } : {},
      // Status filter
      status !== undefined ? { status } : {},
    ]
  }

  // Get components with pagination
  const [components, total] = await Promise.all([
    prisma.componentMaster.findMany({
      where,
      select: {
        id: true,
        name: true,
        code: true,
        category: true,
        description: true,
        uom: true,
        baseRate: true,
        rateEffectiveFrom: true,
        validUpto: true,
        vendorInfo: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        equipment: {
          select: {
            id: true,
            name: true,
            code: true,
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit,
    }),
    prisma.componentMaster.count({ where })
  ])

  return {
    components,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    }
  }
}

/**
 * Get component by ID
 */
export async function getComponentById(id) {
  const component = await prisma.componentMaster.findUnique({
    where: { id: parseInt(id) },
    include: {
      equipment: {
        select: {
          id: true,
          name: true,
          code: true,
          category: {
            select: {
              id: true,
              name: true,
            }
          }
        }
      }
    }
  })

  if (!component) {
    throw new Error('Component not found')
  }

  return component
}

/**
 * Create new component
 */
export async function createComponent(componentData, createdBy) {
  const {
    name,
    code,
    category,
    equipmentId,
    description,
    uom,
    baseRate,
    rateEffectiveFrom,
    validUpto,
    vendorInfo,
    status
  } = componentData

  // Check if component code already exists
  const existingComponent = await prisma.componentMaster.findUnique({
    where: { code }
  })

  if (existingComponent) {
    throw new Error('Component with this code already exists')
  }

  // Verify equipment exists
  const equipment = await prisma.equipmentMaster.findUnique({
    where: { id: parseInt(equipmentId) }
  })

  if (!equipment) {
    throw new Error('Equipment not found')
  }

  // Create component
  const component = await prisma.componentMaster.create({
    data: {
      name,
      code,
      category,
      equipmentId: parseInt(equipmentId),
      description,
      uom,
      baseRate,
      rateEffectiveFrom: new Date(rateEffectiveFrom),
      validUpto: validUpto ? new Date(validUpto) : null,
      vendorInfo,
      status,
      createdBy,
    },
    include: {
      equipment: {
        select: {
          id: true,
          name: true,
          code: true,
        }
      }
    }
  })

  return component
}

/**
 * Update component
 */
export async function updateComponent(id, componentData, updatedBy) {
  const {
    name,
    code,
    category,
    equipmentId,
    description,
    uom,
    baseRate,
    rateEffectiveFrom,
    validUpto,
    vendorInfo,
    status
  } = componentData

  // Check if component exists
  const existingComponent = await prisma.componentMaster.findUnique({
    where: { id: parseInt(id) }
  })

  if (!existingComponent) {
    throw new Error('Component not found')
  }

  // Check if code is being changed and if new code already exists
  if (code && code !== existingComponent.code) {
    const codeExists = await prisma.componentMaster.findUnique({
      where: { code }
    })

    if (codeExists) {
      throw new Error('Component with this code already exists')
    }
  }

  // Verify equipment exists if being changed
  if (equipmentId && equipmentId !== existingComponent.equipmentId) {
    const equipment = await prisma.equipmentMaster.findUnique({
      where: { id: parseInt(equipmentId) }
    })

    if (!equipment) {
      throw new Error('Equipment not found')
    }
  }

  // Update component
  const component = await prisma.componentMaster.update({
    where: { id: parseInt(id) },
    data: {
      ...(name && { name }),
      ...(code && { code }),
      ...(category && { category }),
      ...(equipmentId && { equipmentId: parseInt(equipmentId) }),
      ...(description !== undefined && { description }),
      ...(uom && { uom }),
      ...(baseRate !== undefined && { baseRate }),
      ...(rateEffectiveFrom && { rateEffectiveFrom: new Date(rateEffectiveFrom) }),
      ...(validUpto !== undefined && { validUpto: validUpto ? new Date(validUpto) : null }),
      ...(vendorInfo !== undefined && { vendorInfo }),
      ...(status !== undefined && { status }),
      updatedBy,
    },
    include: {
      equipment: {
        select: {
          id: true,
          name: true,
          code: true,
        }
      }
    }
  })

  return component
}

/**
 * Delete component
 */
export async function deleteComponent(id, deletedBy) {
  // Check if component exists
  const component = await prisma.componentMaster.findUnique({
    where: { id: parseInt(id) }
  })

  if (!component) {
    throw new Error('Component not found')
  }

  // Delete component
  await prisma.componentMaster.delete({
    where: { id: parseInt(id) }
  })

  return { message: 'Component deleted successfully' }
}

/**
 * Get components by equipment ID
 */
export async function getComponentsByEquipment(equipmentId) {
  const components = await prisma.componentMaster.findMany({
    where: { 
      equipmentId: parseInt(equipmentId),
      status: true 
    },
    select: {
      id: true,
      name: true,
      code: true,
      category: true,
      baseRate: true,
      uom: true,
    },
    orderBy: { name: 'asc' }
  })

  return components
}

/**
 * Get component statistics
 */
export async function getComponentStats() {
  const [
    totalComponents,
    activeComponents,
    inactiveComponents,
    categoriesCount,
    recentComponents
  ] = await Promise.all([
    prisma.componentMaster.count(),
    prisma.componentMaster.count({ where: { status: true } }),
    prisma.componentMaster.count({ where: { status: false } }),
    prisma.componentMaster.groupBy({
      by: ['category'],
      _count: { category: true }
    }),
    prisma.componentMaster.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      }
    })
  ])

  return {
    totalComponents,
    activeComponents,
    inactiveComponents,
    categoriesCount: categoriesCount.length,
    recentComponents
  }
}
