import prisma from '@/lib/db/prisma'
import { hashPassword } from '@/lib/utils/encryption'
import { canManageUser } from '@/lib/auth/permissions'

/**
 * Get paginated users list with filters
 */
export async function getUsers(filters = {}) {
  const {
    search = '',
    role,
    status,
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = filters

  const skip = (page - 1) * limit

  // Build where clause
  const where = {
    AND: [
      // Search filter
      search ? {
        OR: [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ]
      } : {},
      // Role filter
      role ? { role } : {},
      // Status filter
      status ? { status } : {},
    ]
  }

  // Get users with pagination
  const [users, total] = await Promise.all([
    prisma.user.findMany({
      where,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        role: true,
        status: true,
        avatar: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true,
        inviter: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit,
    }),
    prisma.user.count({ where })
  ])

  return {
    users,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
    }
  }
}

/**
 * Get user by ID
 */
export async function getUserById(id) {
  const user = await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      status: true,
      avatar: true,
      createdAt: true,
      updatedAt: true,
      lastLoginAt: true,
      inviter: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        }
      },
      invitees: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          role: true,
          status: true,
          createdAt: true,
        }
      }
    }
  })

  if (!user) {
    throw new Error('User not found')
  }

  return user
}

/**
 * Create new user
 */
export async function createUser(userData, createdBy) {
  const { email, firstName, lastName, role, password } = userData

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email: email.toLowerCase() }
  })

  if (existingUser) {
    throw new Error('User with this email already exists')
  }

  // Hash password
  const hashedPassword = await hashPassword(password)

  // Create user
  const user = await prisma.user.create({
    data: {
      email: email.toLowerCase(),
      firstName,
      lastName,
      role,
      password: hashedPassword,
      status: 'ACTIVE',
      invitedBy: createdBy,
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      status: true,
      avatar: true,
      createdAt: true,
    }
  })

  // Log user creation
  await prisma.auditLog.create({
    data: {
      userId: createdBy,
      action: 'CREATE',
      resource: 'USER',
      resourceId: user.id,
      details: {
        email: user.email,
        role: user.role,
        firstName: user.firstName,
        lastName: user.lastName,
      },
    }
  })

  return user
}

/**
 * Update user
 */
export async function updateUser(id, updateData, updatedBy) {
  const { email, firstName, lastName, role, status } = updateData

  // Get current user
  const currentUser = await prisma.user.findUnique({
    where: { id },
    select: { id: true, email: true, role: true, status: true }
  })

  if (!currentUser) {
    throw new Error('User not found')
  }

  // Check if email is already taken by another user
  if (email && email !== currentUser.email) {
    const existingUser = await prisma.user.findFirst({
      where: {
        email: email.toLowerCase(),
        NOT: { id }
      }
    })

    if (existingUser) {
      throw new Error('Email is already taken by another user')
    }
  }

  // Update user
  const updatedUser = await prisma.user.update({
    where: { id },
    data: {
      ...(email && { email: email.toLowerCase() }),
      ...(firstName && { firstName }),
      ...(lastName && { lastName }),
      ...(role && { role }),
      ...(status && { status }),
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      status: true,
      avatar: true,
      createdAt: true,
      updatedAt: true,
    }
  })

  // Log user update
  await prisma.auditLog.create({
    data: {
      userId: updatedBy,
      action: 'UPDATE',
      resource: 'USER',
      resourceId: id,
      details: {
        changes: updateData,
        previousRole: currentUser.role,
        previousStatus: currentUser.status,
      },
    }
  })

  return updatedUser
}

/**
 * Delete user
 */
export async function deleteUser(id, deletedBy, managerRole) {
  // Get user to delete
  const userToDelete = await prisma.user.findUnique({
    where: { id },
    select: { id: true, email: true, role: true, firstName: true, lastName: true }
  })

  if (!userToDelete) {
    throw new Error('User not found')
  }

  // Check if manager can delete this user
  if (!canManageUser(managerRole, userToDelete.role)) {
    throw new Error('Insufficient permissions to delete this user')
  }

  // Delete user (this will cascade delete sessions due to schema)
  await prisma.user.delete({
    where: { id }
  })

  // Log user deletion
  await prisma.auditLog.create({
    data: {
      userId: deletedBy,
      action: 'DELETE',
      resource: 'USER',
      resourceId: id,
      details: {
        email: userToDelete.email,
        role: userToDelete.role,
        firstName: userToDelete.firstName,
        lastName: userToDelete.lastName,
      },
    }
  })

  return { success: true }
}

/**
 * Activate user
 */
export async function activateUser(id, activatedBy) {
  const user = await prisma.user.update({
    where: { id },
    data: { status: 'ACTIVE' },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      status: true,
    }
  })

  // Log user activation
  await prisma.auditLog.create({
    data: {
      userId: activatedBy,
      action: 'ACTIVATE',
      resource: 'USER',
      resourceId: id,
      details: { status: 'ACTIVE' },
    }
  })

  return user
}

/**
 * Deactivate user
 */
export async function deactivateUser(id, deactivatedBy) {
  const user = await prisma.user.update({
    where: { id },
    data: { status: 'INACTIVE' },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      status: true,
    }
  })

  // Delete all user sessions when deactivating
  await prisma.session.deleteMany({
    where: { userId: id }
  })

  // Log user deactivation
  await prisma.auditLog.create({
    data: {
      userId: deactivatedBy,
      action: 'DEACTIVATE',
      resource: 'USER',
      resourceId: id,
      details: { status: 'INACTIVE' },
    }
  })

  return user
}

/**
 * Get user statistics
 */
export async function getUserStats() {
  const [
    totalUsers,
    activeUsers,
    inactiveUsers,
    pendingUsers,
    adminUsers,
    managerUsers,
    regularUsers,
    recentUsers
  ] = await Promise.all([
    prisma.user.count(),
    prisma.user.count({ where: { status: 'ACTIVE' } }),
    prisma.user.count({ where: { status: 'INACTIVE' } }),
    prisma.user.count({ where: { status: 'PENDING' } }),
    prisma.user.count({ where: { role: 'ADMIN' } }),
    prisma.user.count({ where: { role: 'MANAGER' } }),
    prisma.user.count({ where: { role: 'USER' } }),
    prisma.user.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      }
    })
  ])

  return {
    total: totalUsers,
    byStatus: {
      active: activeUsers,
      inactive: inactiveUsers,
      pending: pendingUsers,
    },
    byRole: {
      admin: adminUsers,
      manager: managerUsers,
      user: regularUsers,
    },
    recent: recentUsers,
  }
}
