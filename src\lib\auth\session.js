import jwt from 'jsonwebtoken'
import { cookies } from 'next/headers'
import prisma from '@/lib/db/prisma'
import env from '@/lib/config/env'

const JWT_SECRET = env.JWT_SECRET
const TOKEN_EXPIRY = '7d' // 7 days
const COOKIE_NAME = 'auth-token'

/**
 * Generate JWT token for user
 */
export function generateToken(payload) {
  return jwt.sign(payload, JWT_SECRET, { 
    expiresIn: TOKEN_EXPIRY,
    issuer: env.APP_NAME,
    audience: env.APP_URL
  })
}

/**
 * Verify JWT token
 */
export function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET, {
      issuer: env.APP_NAME,
      audience: env.APP_URL
    })
  } catch (error) {
    throw new Error('Invalid token')
  }
}

/**
 * Create session for user
 */
export async function createSession(userId, userAgent = null, ipAddress = null) {
  const expiresAt = new Date()
  expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now

  // Generate JWT token
  const tokenPayload = {
    userId,
    sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
  
  const token = generateToken(tokenPayload)

  // Store session in database
  const session = await prisma.session.create({
    data: {
      userId,
      token,
      expiresAt,
      userAgent,
      ipAddress,
    },
  })

  return { session, token }
}

/**
 * Get session from token
 */
export async function getSession(token) {
  if (!token) return null

  try {
    // Verify JWT token
    const payload = verifyToken(token)
    
    // Get session from database
    const session = await prisma.session.findUnique({
      where: { token },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            role: true,
            status: true,
            avatar: true,
          }
        }
      }
    })

    if (!session || session.expiresAt < new Date()) {
      // Session expired or not found
      if (session) {
        await prisma.session.delete({ where: { id: session.id } })
      }
      return null
    }

    return session
  } catch (error) {
    return null
  }
}

/**
 * Delete session
 */
export async function deleteSession(token) {
  if (!token) return

  try {
    await prisma.session.delete({
      where: { token }
    })
  } catch (error) {
    // Session might not exist, ignore error
  }
}

/**
 * Delete all sessions for user
 */
export async function deleteAllUserSessions(userId) {
  await prisma.session.deleteMany({
    where: { userId }
  })
}

/**
 * Clean up expired sessions
 */
export async function cleanupExpiredSessions() {
  await prisma.session.deleteMany({
    where: {
      expiresAt: {
        lt: new Date()
      }
    }
  })
}

/**
 * Set auth cookie
 */
export function setAuthCookie(token) {
  const cookieStore = cookies()
  cookieStore.set(COOKIE_NAME, token, {
    httpOnly: true,
    secure: env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 60 * 60 * 24 * 7, // 7 days
    path: '/',
  })
}

/**
 * Clear auth cookie
 */
export function clearAuthCookie() {
  const cookieStore = cookies()
  cookieStore.delete(COOKIE_NAME)
}

/**
 * Get auth token from cookies
 */
export function getAuthToken() {
  const cookieStore = cookies()
  return cookieStore.get(COOKIE_NAME)?.value
}

/**
 * Get current user from session
 */
export async function getCurrentUser() {
  const token = getAuthToken()
  if (!token) return null

  const session = await getSession(token)
  return session?.user || null
}
