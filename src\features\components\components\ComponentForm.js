'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { createComponentSchema, updateComponentSchema, componentCategories } from '@/features/components/validation/componentSchemas'
import { useApi } from '@/hooks/useApi'
import { useToast } from '@/hooks/useToast'

export default function ComponentForm({ component, onSave, onCancel }) {
  const [equipment, setEquipment] = useState([])
  const [loading, setLoading] = useState(false)

  const isEdit = !!component
  const schema = isEdit ? updateComponentSchema : createComponentSchema

  const { apiCall } = useApi()
  const { showToast } = useToast()

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: component || {
      status: true,
      rateEffectiveFrom: new Date().toISOString().split('T')[0]
    }
  })

  // Fetch equipment for dropdown
  useEffect(() => {
    const fetchEquipment = async () => {
      try {
        const response = await apiCall('/api/equipment?limit=100&status=true')
        if (response.success) {
          setEquipment(response.data)
        }
      } catch (error) {
        console.error('Failed to fetch equipment:', error)
      }
    }
    fetchEquipment()
  }, [])

  const onSubmit = async (data) => {
    try {
      setLoading(true)

      const payload = {
        ...data,
        baseRate: parseFloat(data.baseRate),
        equipmentId: parseInt(data.equipmentId)
      }

      const url = isEdit ? `/api/components/${component.id}` : '/api/components'
      const method = isEdit ? 'PUT' : 'POST'

      const response = await apiCall(url, method, payload)

      if (response.success) {
        showToast(
          isEdit ? 'Component updated successfully' : 'Component created successfully',
          'success'
        )
        onSave(response.data)
      }
    } catch (error) {
      showToast('Failed to save component', 'error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isEdit ? 'Edit Component' : 'Add New Component'}
          </h1>
          <p className="text-gray-600 mt-1">
            {isEdit ? 'Update component details' : 'Create a new component entry'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Component Name *</Label>
                <Input
                  id="name"
                  {...register('name')}
                  placeholder="Enter component name"
                />
                {errors.name && (
                  <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="code">Component Code *</Label>
                <Input
                  id="code"
                  {...register('code')}
                  placeholder="Enter component code (e.g., STEEL_001)"
                  className="font-mono"
                />
                {errors.code && (
                  <p className="text-red-500 text-sm mt-1">{errors.code.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="category">Category *</Label>
                <select
                  id="category"
                  {...register('category')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Category</option>
                  {componentCategories.map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
                {errors.category && (
                  <p className="text-red-500 text-sm mt-1">{errors.category.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="equipmentId">Linked Equipment *</Label>
                <select
                  id="equipmentId"
                  {...register('equipmentId')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Equipment</option>
                  {equipment.map(item => (
                    <option key={item.id} value={item.id}>
                      {item.name} ({item.code})
                    </option>
                  ))}
                </select>
                {errors.equipmentId && (
                  <p className="text-red-500 text-sm mt-1">{errors.equipmentId.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="uom">Unit of Measure *</Label>
                <Input
                  id="uom"
                  {...register('uom')}
                  placeholder="e.g., kg, meter, piece"
                />
                {errors.uom && (
                  <p className="text-red-500 text-sm mt-1">{errors.uom.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="baseRate">Base Rate (₹) *</Label>
                <Input
                  id="baseRate"
                  type="number"
                  step="0.01"
                  {...register('baseRate')}
                  placeholder="Enter base rate"
                />
                {errors.baseRate && (
                  <p className="text-red-500 text-sm mt-1">{errors.baseRate.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="rateEffectiveFrom">Rate Effective From *</Label>
                <Input
                  id="rateEffectiveFrom"
                  type="date"
                  {...register('rateEffectiveFrom')}
                />
                {errors.rateEffectiveFrom && (
                  <p className="text-red-500 text-sm mt-1">{errors.rateEffectiveFrom.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="validUpto">Valid Upto</Label>
                <Input
                  id="validUpto"
                  type="date"
                  {...register('validUpto')}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Enter component description"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="vendorInfo">Vendor Information</Label>
              <Input
                id="vendorInfo"
                {...register('vendorInfo')}
                placeholder="Enter vendor details (optional)"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="status"
                checked={watch('status')}
                onCheckedChange={(checked) => setValue('status', checked)}
              />
              <Label htmlFor="status">Active Status</Label>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading} className="btn-gradient-primary">
            {loading ? 'Saving...' : (isEdit ? 'Update Component' : 'Create Component')}
          </Button>
        </div>
      </form>
    </div>
  )
}
