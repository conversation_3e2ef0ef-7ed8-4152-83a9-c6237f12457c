import { getCurrentUser } from '@/lib/auth/session'
import { getEstimationSheetById, updateEstimationSheet, deleteEstimationSheet } from '@/features/estimation/services/estimationService'
import { updateEstimationSchema } from '@/features/estimation/validation/estimationSchemas'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view estimation sheet')
    }

    const estimationId = params.id
    const estimation = await getEstimationSheetById(estimationId)

    return successResponse(estimation, 'Estimation sheet retrieved successfully')

  } catch (error) {
    if (error.message === 'Estimation sheet not found') {
      return notFoundResponse('Estimation sheet not found')
    }
    return handleApiError(error)
  }
}

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_UPDATE)) {
      return forbiddenResponse('Insufficient permissions to update estimation sheet')
    }

    const estimationId = params.id
    const body = await request.json()
    
    // Validate request body
    const validatedData = updateEstimationSchema.parse(body)
    
    // Update estimation sheet
    const updatedEstimation = await updateEstimationSheet(estimationId, validatedData, user.id)
    
    return successResponse(updatedEstimation, 'Estimation sheet updated successfully')
    
  } catch (error) {
    if (error.message === 'Estimation sheet not found') {
      return notFoundResponse('Estimation sheet not found')
    }
    return handleApiError(error)
  }
}

export async function DELETE(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_DELETE)) {
      return forbiddenResponse('Insufficient permissions to delete estimation sheet')
    }

    const estimationId = params.id
    
    // Delete estimation sheet
    const result = await deleteEstimationSheet(estimationId, user.id)
    
    return successResponse(result, 'Estimation sheet deleted successfully')
    
  } catch (error) {
    if (error.message === 'Estimation sheet not found') {
      return notFoundResponse('Estimation sheet not found')
    }
    return handleApiError(error)
  }
}
