import { getCurrentUser } from '@/lib/auth/session'
import { submitConveyorPlan } from '@/features/conveyor/services/conveyorService'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_UPDATE)) {
      return forbiddenResponse('Insufficient permissions to submit conveyor plan')
    }

    const planId = params.id
    
    // Submit plan
    const submittedPlan = await submitConveyorPlan(planId, user.id)
    
    return successResponse(submittedPlan, 'Conveyor plan submitted successfully')
    
  } catch (error) {
    if (error.message === 'Conveyor plan not found') {
      return notFoundResponse('Conveyor plan not found')
    }
    return handleApiError(error)
  }
}
