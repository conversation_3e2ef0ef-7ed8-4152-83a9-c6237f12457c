# Database
DATABASE_URL="postgresql://username:password@localhost:5432/coveyor_poc?schema=public"

# Authentication
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"
JWT_SECRET="your-jwt-secret-here"

# App Configuration
NODE_ENV="development"
APP_URL="http://localhost:3000"
APP_NAME="Coveyor POC"

# Email Configuration (for user invitations)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"

# Optional: External Services
# REDIS_URL="redis://localhost:6379"
# UPLOAD_DIR="./uploads"
