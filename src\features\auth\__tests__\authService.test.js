import { authenticateUser, createUser, logoutUser } from '../services/authService'
import { createMockPrisma, mockUsers } from '../../../__tests__/utils'
import * as encryptionUtils from '../../../lib/utils/encryption'
import * as sessionUtils from '../../../lib/auth/session'

// Mock dependencies
jest.mock('../../../lib/db/prisma', () => createMockPrisma())
jest.mock('../../../lib/utils/encryption')
jest.mock('../../../lib/auth/session')

const mockPrisma = require('../../../lib/db/prisma').default

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('authenticateUser', () => {
    it('successfully authenticates user with valid credentials', async () => {
      const mockUser = {
        ...mockUsers.admin,
        password: 'hashedpassword',
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      encryptionUtils.verifyPassword.mockResolvedValue(true)
      sessionUtils.createSession.mockResolvedValue({
        session: { id: 'session-1', expiresAt: new Date() },
        token: 'mock-token',
      })
      mockPrisma.user.update.mockResolvedValue(mockUser)
      mockPrisma.auditLog.create.mockResolvedValue({})

      const result = await authenticateUser('<EMAIL>', 'password123')

      expect(result).toHaveProperty('user')
      expect(result).toHaveProperty('session')
      expect(result).toHaveProperty('token')
      expect(result.user).not.toHaveProperty('password')
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
        select: expect.any(Object),
      })
      expect(encryptionUtils.verifyPassword).toHaveBeenCalledWith('password123', 'hashedpassword')
      expect(sessionUtils.createSession).toHaveBeenCalledWith(mockUser.id, null, null)
    })

    it('throws error for non-existent user', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null)

      await expect(authenticateUser('<EMAIL>', 'password123'))
        .rejects.toThrow('Invalid credentials')
    })

    it('throws error for inactive user', async () => {
      const inactiveUser = {
        ...mockUsers.user,
        status: 'INACTIVE',
        password: 'hashedpassword',
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(inactiveUser)

      await expect(authenticateUser('<EMAIL>', 'password123'))
        .rejects.toThrow('Account is not active')
    })

    it('throws error for invalid password', async () => {
      const mockUser = {
        ...mockUsers.admin,
        password: 'hashedpassword',
      }
      
      mockPrisma.user.findUnique.mockResolvedValue(mockUser)
      encryptionUtils.verifyPassword.mockResolvedValue(false)

      await expect(authenticateUser('<EMAIL>', 'wrongpassword'))
        .rejects.toThrow('Invalid credentials')
    })
  })

  describe('createUser', () => {
    it('successfully creates new user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: 'USER',
      }

      mockPrisma.user.findUnique.mockResolvedValue(null) // User doesn't exist
      encryptionUtils.hashPassword.mockResolvedValue('hashedpassword')
      mockPrisma.user.create.mockResolvedValue({
        id: 'new-user-id',
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role,
        status: 'ACTIVE',
        createdAt: new Date(),
      })
      mockPrisma.auditLog.create.mockResolvedValue({})

      const result = await createUser(userData, 'creator-id')

      expect(result).toHaveProperty('id')
      expect(result.email).toBe(userData.email)
      expect(mockPrisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: userData.email.toLowerCase() },
      })
      expect(encryptionUtils.hashPassword).toHaveBeenCalledWith(userData.password)
      expect(mockPrisma.user.create).toHaveBeenCalledWith({
        data: {
          email: userData.email.toLowerCase(),
          password: 'hashedpassword',
          firstName: userData.firstName,
          lastName: userData.lastName,
          role: userData.role,
          status: 'ACTIVE',
          invitedBy: 'creator-id',
        },
        select: expect.any(Object),
      })
    })

    it('throws error when user already exists', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Existing',
        lastName: 'User',
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.user)

      await expect(createUser(userData, 'creator-id'))
        .rejects.toThrow('User already exists')
    })
  })

  describe('logoutUser', () => {
    it('successfully logs out user', async () => {
      sessionUtils.deleteSession.mockResolvedValue()
      mockPrisma.auditLog.create.mockResolvedValue({})

      await logoutUser('mock-token', 'user-id')

      expect(sessionUtils.deleteSession).toHaveBeenCalledWith('mock-token')
      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: {
          userId: 'user-id',
          action: 'LOGOUT',
          resource: 'AUTH',
          details: { method: 'manual' },
        },
      })
    })

    it('handles logout without user ID', async () => {
      sessionUtils.deleteSession.mockResolvedValue()

      await logoutUser('mock-token')

      expect(sessionUtils.deleteSession).toHaveBeenCalledWith('mock-token')
      expect(mockPrisma.auditLog.create).not.toHaveBeenCalled()
    })
  })
})
