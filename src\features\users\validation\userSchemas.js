import { z } from 'zod'

/**
 * User creation validation schema
 */
export const createUserSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
  firstName: z
    .string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters'),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters'),
  role: z
    .enum(['USER', 'MANAGER', 'ADMIN'], {
      required_error: 'Role is required',
      invalid_type_error: 'Invalid role selected',
    }),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 
      'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
})

/**
 * User update validation schema
 */
export const updateUserSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .optional(),
  firstName: z
    .string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters')
    .optional(),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters')
    .optional(),
  role: z
    .enum(['USER', 'MANAGER', 'ADMIN'])
    .optional(),
  status: z
    .enum(['ACTIVE', 'INACTIVE', 'PENDING', 'SUSPENDED'])
    .optional(),
})

/**
 * User invitation validation schema
 */
export const inviteUserSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
  firstName: z
    .string()
    .min(1, 'First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be less than 50 characters'),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be less than 50 characters'),
  role: z
    .enum(['USER', 'MANAGER', 'ADMIN'], {
      required_error: 'Role is required',
      invalid_type_error: 'Invalid role selected',
    }),
  message: z
    .string()
    .max(500, 'Message must be less than 500 characters')
    .optional(),
})

/**
 * User search/filter validation schema
 */
export const userFilterSchema = z.object({
  search: z.string().optional(),
  role: z.enum(['USER', 'MANAGER', 'ADMIN']).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'PENDING', 'SUSPENDED']).optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  sortBy: z.enum(['firstName', 'lastName', 'email', 'role', 'status', 'createdAt', 'lastLoginAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

/**
 * Bulk user action validation schema
 */
export const bulkUserActionSchema = z.object({
  userIds: z
    .array(z.string().min(1, 'User ID is required'))
    .min(1, 'At least one user must be selected'),
  action: z.enum(['activate', 'deactivate', 'delete'], {
    required_error: 'Action is required',
    invalid_type_error: 'Invalid action selected',
  }),
})

/**
 * User role assignment validation schema
 */
export const assignRoleSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  role: z.enum(['USER', 'MANAGER', 'ADMIN'], {
    required_error: 'Role is required',
    invalid_type_error: 'Invalid role selected',
  }),
})
