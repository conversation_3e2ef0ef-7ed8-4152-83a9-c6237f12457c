'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { createEstimationSchema, updateEstimationSchema } from '@/features/estimation/validation/estimationSchemas'
import { useApi } from '@/hooks/useApi'
import { useToast } from '@/hooks/useToast'

export default function EstimationForm({ estimation, onSave, onCancel }) {
  const [plans, setPlans] = useState([])
  const [loading, setLoading] = useState(false)
  const [calculatedTotal, setCalculatedTotal] = useState(0)

  const isEdit = !!estimation
  const schema = isEdit ? updateEstimationSchema : createEstimationSchema

  const { apiCall } = useApi()
  const { showToast } = useToast()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: estimation || {
      status: 'Draft',
      discountPercent: 0,
      markupPercent: 20
    }
  })

  // Watch form values for automatic calculation
  const watchedValues = watch(['equipmentCost', 'componentCost', 'laborCost', 'overheadCost', 'discountPercent', 'markupPercent'])

  // Fetch approved plans for dropdown
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        const response = await apiCall('/api/conveyor-plans?status=Approved&limit=100')
        if (response.success) {
          setPlans(response.data)
        }
      } catch (error) {
        console.error('Failed to fetch plans:', error)
      }
    }
    fetchPlans()
  }, [])

  // Calculate total cost automatically
  useEffect(() => {
    const [equipmentCost, componentCost, laborCost, overheadCost, discountPercent, markupPercent] = watchedValues

    const equipment = parseFloat(equipmentCost) || 0
    const component = parseFloat(componentCost) || 0
    const labor = parseFloat(laborCost) || 0
    const overhead = parseFloat(overheadCost) || 0
    const discount = parseFloat(discountPercent) || 0
    const markup = parseFloat(markupPercent) || 0

    const subtotal = equipment + component + labor + overhead
    const discountAmount = subtotal * (discount / 100)
    const afterDiscount = subtotal - discountAmount
    const markupAmount = afterDiscount * (markup / 100)
    const total = afterDiscount + markupAmount

    setCalculatedTotal(total)
    setValue('totalCost', total)
  }, [watchedValues, setValue])

  const onSubmit = async (data) => {
    try {
      setLoading(true)

      const payload = {
        ...data,
        planId: parseInt(data.planId),
        equipmentCost: parseFloat(data.equipmentCost) || 0,
        componentCost: parseFloat(data.componentCost) || 0,
        laborCost: parseFloat(data.laborCost) || 0,
        overheadCost: parseFloat(data.overheadCost) || 0,
        discountPercent: parseFloat(data.discountPercent) || 0,
        markupPercent: parseFloat(data.markupPercent) || 0,
        totalCost: parseFloat(data.totalCost) || 0,
      }

      const url = isEdit ? `/api/estimations/${estimation.id}` : '/api/estimations'
      const method = isEdit ? 'PUT' : 'POST'

      const response = await apiCall(url, method, payload)

      if (response.success) {
        showToast(
          isEdit ? 'Estimation updated successfully' : 'Estimation created successfully',
          'success'
        )
        onSave(response.data)
      }
    } catch (error) {
      showToast('Failed to save estimation', 'error')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isEdit ? 'Edit Estimation Sheet' : 'Create New Estimation Sheet'}
          </h1>
          <p className="text-gray-600 mt-1">
            {isEdit ? 'Update estimation details' : 'Create a manual cost estimation'}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="planId">Conveyor Plan *</Label>
                <select
                  id="planId"
                  {...register('planId')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={isEdit}
                >
                  <option value="">Select Plan</option>
                  {plans.map(plan => (
                    <option key={plan.id} value={plan.id}>
                      {plan.planName} - {plan.conveyorType}
                    </option>
                  ))}
                </select>
                {errors.planId && (
                  <p className="text-red-500 text-sm mt-1">{errors.planId.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="estimationVersion">Version</Label>
                <Input
                  id="estimationVersion"
                  type="number"
                  {...register('estimationVersion')}
                  placeholder="Version number"
                  disabled={isEdit}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Cost Breakdown */}
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Cost Breakdown</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="equipmentCost">Equipment Cost (₹)</Label>
                <Input
                  id="equipmentCost"
                  type="number"
                  step="0.01"
                  {...register('equipmentCost')}
                  placeholder="0.00"
                />
              </div>

              <div>
                <Label htmlFor="componentCost">Component Cost (₹)</Label>
                <Input
                  id="componentCost"
                  type="number"
                  step="0.01"
                  {...register('componentCost')}
                  placeholder="0.00"
                />
              </div>

              <div>
                <Label htmlFor="laborCost">Labor Cost (₹)</Label>
                <Input
                  id="laborCost"
                  type="number"
                  step="0.01"
                  {...register('laborCost')}
                  placeholder="0.00"
                />
              </div>

              <div>
                <Label htmlFor="overheadCost">Overhead Cost (₹)</Label>
                <Input
                  id="overheadCost"
                  type="number"
                  step="0.01"
                  {...register('overheadCost')}
                  placeholder="0.00"
                />
              </div>

              <div>
                <Label htmlFor="discountPercent">Discount (%)</Label>
                <Input
                  id="discountPercent"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  {...register('discountPercent')}
                  placeholder="0.00"
                />
              </div>

              <div>
                <Label htmlFor="markupPercent">Markup (%)</Label>
                <Input
                  id="markupPercent"
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('markupPercent')}
                  placeholder="20.00"
                />
              </div>
            </div>

            {/* Total Cost Display */}
            <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex justify-between items-center">
                <span className="text-lg font-medium text-green-800">Total Cost:</span>
                <span className="text-2xl font-bold text-green-900">
                  {formatCurrency(calculatedTotal)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                {...register('notes')}
                placeholder="Add any notes or comments about this estimation"
                rows={4}
              />
            </div>

            <div>
              <Label htmlFor="status">Status</Label>
              <select
                id="status"
                {...register('status')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Draft">Draft</option>
                <option value="Final">Final</option>
                <option value="Sent">Sent</option>
                <option value="Approved">Approved</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading} className="btn-gradient-primary">
            {loading ? 'Saving...' : (isEdit ? 'Update Estimation' : 'Create Estimation')}
          </Button>
        </div>
      </form>
    </div>
  )
}
