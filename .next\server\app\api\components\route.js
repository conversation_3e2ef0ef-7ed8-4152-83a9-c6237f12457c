"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/components/route";
exports.ids = ["app/api/components/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomponents%2Froute&page=%2Fapi%2Fcomponents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomponents%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomponents%2Froute&page=%2Fapi%2Fcomponents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomponents%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_MobioProjects_coveyor_poc_src_app_api_components_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/components/route.js */ \"(rsc)/./src/app/api/components/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/components/route\",\n        pathname: \"/api/components\",\n        filename: \"route\",\n        bundlePath: \"app/api/components/route\"\n    },\n    resolvedPagePath: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\api\\\\components\\\\route.js\",\n    nextConfigOutput,\n    userland: D_MobioProjects_coveyor_poc_src_app_api_components_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/components/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomponents%2Froute&page=%2Fapi%2Fcomponents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomponents%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/components/route.js":
/*!*****************************************!*\
  !*** ./src/app/api/components/route.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_session__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth/session */ \"(rsc)/./src/lib/auth/session.js\");\n/* harmony import */ var _features_components_services_componentService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/components/services/componentService */ \"(rsc)/./src/features/components/services/componentService.js\");\n/* harmony import */ var _features_components_validation_componentSchemas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/components/validation/componentSchemas */ \"(rsc)/./src/features/components/validation/componentSchemas.js\");\n/* harmony import */ var _lib_api_response__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/response */ \"(rsc)/./src/lib/api/response.js\");\n/* harmony import */ var _lib_auth_permissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth/permissions */ \"(rsc)/./src/lib/auth/permissions.js\");\n\n\n\n\n\nasync function GET(request) {\n    try {\n        const user = await (0,_lib_auth_session__WEBPACK_IMPORTED_MODULE_0__.getCurrentUser)();\n        if (!user) {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_3__.unauthorizedResponse)(\"Authentication required\");\n        }\n        // Check permission\n        if (!(0,_lib_auth_permissions__WEBPACK_IMPORTED_MODULE_4__.hasPermission)(user.role, _lib_auth_permissions__WEBPACK_IMPORTED_MODULE_4__.PERMISSIONS.USER_READ)) {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_3__.forbiddenResponse)(\"Insufficient permissions to view components\");\n        }\n        // Parse query parameters\n        const { searchParams } = new URL(request.url);\n        const filters = _features_components_validation_componentSchemas__WEBPACK_IMPORTED_MODULE_2__.componentFilterSchema.parse({\n            search: searchParams.get(\"search\") || \"\",\n            category: searchParams.get(\"category\") || undefined,\n            equipmentId: searchParams.get(\"equipmentId\") || undefined,\n            status: searchParams.get(\"status\") ? searchParams.get(\"status\") === \"true\" : undefined,\n            page: searchParams.get(\"page\") || \"1\",\n            limit: searchParams.get(\"limit\") || \"10\",\n            sortBy: searchParams.get(\"sortBy\") || \"createdAt\",\n            sortOrder: searchParams.get(\"sortOrder\") || \"desc\"\n        });\n        // Get components\n        const result = await (0,_features_components_services_componentService__WEBPACK_IMPORTED_MODULE_1__.getComponents)(filters);\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_3__.paginatedResponse)(result.components, result.pagination, \"Components retrieved successfully\");\n    } catch (error) {\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n    }\n}\nasync function POST(request) {\n    try {\n        const user = await (0,_lib_auth_session__WEBPACK_IMPORTED_MODULE_0__.getCurrentUser)();\n        if (!user) {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_3__.unauthorizedResponse)(\"Authentication required\");\n        }\n        // Check permission\n        if (!(0,_lib_auth_permissions__WEBPACK_IMPORTED_MODULE_4__.hasPermission)(user.role, _lib_auth_permissions__WEBPACK_IMPORTED_MODULE_4__.PERMISSIONS.USER_CREATE)) {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_3__.forbiddenResponse)(\"Insufficient permissions to create components\");\n        }\n        const body = await request.json();\n        // Validate request body\n        const validatedData = _features_components_validation_componentSchemas__WEBPACK_IMPORTED_MODULE_2__.createComponentSchema.parse(body);\n        // Create component\n        const newComponent = await (0,_features_components_services_componentService__WEBPACK_IMPORTED_MODULE_1__.createComponent)(validatedData, user.id);\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_3__.successResponse)(newComponent, \"Component created successfully\", null, 201);\n    } catch (error) {\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_3__.handleApiError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/components/route.js\n");

/***/ }),

/***/ "(rsc)/./src/features/components/services/componentService.js":
/*!**************************************************************!*\
  !*** ./src/features/components/services/componentService.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createComponent: () => (/* binding */ createComponent),\n/* harmony export */   deleteComponent: () => (/* binding */ deleteComponent),\n/* harmony export */   getComponentById: () => (/* binding */ getComponentById),\n/* harmony export */   getComponentStats: () => (/* binding */ getComponentStats),\n/* harmony export */   getComponents: () => (/* binding */ getComponents),\n/* harmony export */   getComponentsByEquipment: () => (/* binding */ getComponentsByEquipment),\n/* harmony export */   updateComponent: () => (/* binding */ updateComponent)\n/* harmony export */ });\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n\n/**\n * Get paginated components list with filters\n */ async function getComponents(filters = {}) {\n    const { search = \"\", category, equipmentId, status, page = 1, limit = 10, sortBy = \"createdAt\", sortOrder = \"desc\" } = filters;\n    const skip = (page - 1) * limit;\n    // Build where clause\n    const where = {\n        AND: [\n            // Search filter\n            search ? {\n                OR: [\n                    {\n                        name: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        code: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        description: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        vendorInfo: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    }\n                ]\n            } : {},\n            // Category filter\n            category ? {\n                category: {\n                    contains: category,\n                    mode: \"insensitive\"\n                }\n            } : {},\n            // Equipment filter\n            equipmentId ? {\n                equipmentId: parseInt(equipmentId)\n            } : {},\n            // Status filter\n            status !== undefined ? {\n                status\n            } : {}\n        ]\n    };\n    // Get components with pagination\n    const [components, total] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findMany({\n            where,\n            select: {\n                id: true,\n                name: true,\n                code: true,\n                category: true,\n                description: true,\n                uom: true,\n                baseRate: true,\n                rateEffectiveFrom: true,\n                validUpto: true,\n                vendorInfo: true,\n                status: true,\n                createdAt: true,\n                updatedAt: true,\n                equipment: {\n                    select: {\n                        id: true,\n                        name: true,\n                        code: true\n                    }\n                }\n            },\n            orderBy: {\n                [sortBy]: sortOrder\n            },\n            skip,\n            take: limit\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.count({\n            where\n        })\n    ]);\n    return {\n        components,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit)\n        }\n    };\n}\n/**\n * Get component by ID\n */ async function getComponentById(id) {\n    const component = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            equipment: {\n                select: {\n                    id: true,\n                    name: true,\n                    code: true,\n                    category: {\n                        select: {\n                            id: true,\n                            name: true\n                        }\n                    }\n                }\n            }\n        }\n    });\n    if (!component) {\n        throw new Error(\"Component not found\");\n    }\n    return component;\n}\n/**\n * Create new component\n */ async function createComponent(componentData, createdBy) {\n    const { name, code, category, equipmentId, description, uom, baseRate, rateEffectiveFrom, validUpto, vendorInfo, status } = componentData;\n    // Check if component code already exists\n    const existingComponent = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findUnique({\n        where: {\n            code\n        }\n    });\n    if (existingComponent) {\n        throw new Error(\"Component with this code already exists\");\n    }\n    // Verify equipment exists\n    const equipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findUnique({\n        where: {\n            id: parseInt(equipmentId)\n        }\n    });\n    if (!equipment) {\n        throw new Error(\"Equipment not found\");\n    }\n    // Create component\n    const component = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.create({\n        data: {\n            name,\n            code,\n            category,\n            equipmentId: parseInt(equipmentId),\n            description,\n            uom,\n            baseRate,\n            rateEffectiveFrom: new Date(rateEffectiveFrom),\n            validUpto: validUpto ? new Date(validUpto) : null,\n            vendorInfo,\n            status,\n            createdBy\n        },\n        include: {\n            equipment: {\n                select: {\n                    id: true,\n                    name: true,\n                    code: true\n                }\n            }\n        }\n    });\n    return component;\n}\n/**\n * Update component\n */ async function updateComponent(id, componentData, updatedBy) {\n    const { name, code, category, equipmentId, description, uom, baseRate, rateEffectiveFrom, validUpto, vendorInfo, status } = componentData;\n    // Check if component exists\n    const existingComponent = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!existingComponent) {\n        throw new Error(\"Component not found\");\n    }\n    // Check if code is being changed and if new code already exists\n    if (code && code !== existingComponent.code) {\n        const codeExists = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findUnique({\n            where: {\n                code\n            }\n        });\n        if (codeExists) {\n            throw new Error(\"Component with this code already exists\");\n        }\n    }\n    // Verify equipment exists if being changed\n    if (equipmentId && equipmentId !== existingComponent.equipmentId) {\n        const equipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findUnique({\n            where: {\n                id: parseInt(equipmentId)\n            }\n        });\n        if (!equipment) {\n            throw new Error(\"Equipment not found\");\n        }\n    }\n    // Update component\n    const component = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            ...name && {\n                name\n            },\n            ...code && {\n                code\n            },\n            ...category && {\n                category\n            },\n            ...equipmentId && {\n                equipmentId: parseInt(equipmentId)\n            },\n            ...description !== undefined && {\n                description\n            },\n            ...uom && {\n                uom\n            },\n            ...baseRate !== undefined && {\n                baseRate\n            },\n            ...rateEffectiveFrom && {\n                rateEffectiveFrom: new Date(rateEffectiveFrom)\n            },\n            ...validUpto !== undefined && {\n                validUpto: validUpto ? new Date(validUpto) : null\n            },\n            ...vendorInfo !== undefined && {\n                vendorInfo\n            },\n            ...status !== undefined && {\n                status\n            },\n            updatedBy\n        },\n        include: {\n            equipment: {\n                select: {\n                    id: true,\n                    name: true,\n                    code: true\n                }\n            }\n        }\n    });\n    return component;\n}\n/**\n * Delete component\n */ async function deleteComponent(id, deletedBy) {\n    // Check if component exists\n    const component = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!component) {\n        throw new Error(\"Component not found\");\n    }\n    // Delete component\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.delete({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    return {\n        message: \"Component deleted successfully\"\n    };\n}\n/**\n * Get components by equipment ID\n */ async function getComponentsByEquipment(equipmentId) {\n    const components = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findMany({\n        where: {\n            equipmentId: parseInt(equipmentId),\n            status: true\n        },\n        select: {\n            id: true,\n            name: true,\n            code: true,\n            category: true,\n            baseRate: true,\n            uom: true\n        },\n        orderBy: {\n            name: \"asc\"\n        }\n    });\n    return components;\n}\n/**\n * Get component statistics\n */ async function getComponentStats() {\n    const [totalComponents, activeComponents, inactiveComponents, categoriesCount, recentComponents] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.count(),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.count({\n            where: {\n                status: true\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.count({\n            where: {\n                status: false\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.groupBy({\n            by: [\n                \"category\"\n            ],\n            _count: {\n                category: true\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.count({\n            where: {\n                createdAt: {\n                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                }\n            }\n        })\n    ]);\n    return {\n        totalComponents,\n        activeComponents,\n        inactiveComponents,\n        categoriesCount: categoriesCount.length,\n        recentComponents\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/components/services/componentService.js\n");

/***/ }),

/***/ "(rsc)/./src/features/components/validation/componentSchemas.js":
/*!****************************************************************!*\
  !*** ./src/features/components/validation/componentSchemas.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   componentCategories: () => (/* binding */ componentCategories),\n/* harmony export */   componentFilterSchema: () => (/* binding */ componentFilterSchema),\n/* harmony export */   createComponentSchema: () => (/* binding */ createComponentSchema),\n/* harmony export */   updateComponentSchema: () => (/* binding */ updateComponentSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n/**\n * Component Master validation schema\n */ const createComponentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Component name is required\").min(2, \"Component name must be at least 2 characters\").max(150, \"Component name must be less than 150 characters\"),\n    code: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Component code is required\").min(2, \"Component code must be at least 2 characters\").max(100, \"Component code must be less than 100 characters\").regex(/^[A-Z0-9_-]+$/, \"Component code must contain only uppercase letters, numbers, underscores, and hyphens\"),\n    category: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Category is required\").max(100, \"Category must be less than 100 characters\"),\n    equipmentId: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().positive(\"Equipment is required\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().max(1000, \"Description must be less than 1000 characters\").optional(),\n    uom: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Unit of measure is required\").max(50, \"Unit of measure must be less than 50 characters\"),\n    baseRate: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive(\"Base rate must be a positive number\").max(999999999999, \"Base rate is too large\"),\n    rateEffectiveFrom: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().datetime(\"Invalid effective from date\").or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()),\n    validUpto: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().datetime(\"Invalid valid upto date\").or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date()).optional(),\n    vendorInfo: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().max(255, \"Vendor info must be less than 255 characters\").optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().default(true)\n});\nconst updateComponentSchema = createComponentSchema.partial();\n/**\n * Component search/filter validation schema\n */ const componentFilterSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    search: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    category: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    equipmentId: zod__WEBPACK_IMPORTED_MODULE_0__.z.coerce.number().int().positive().optional(),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional(),\n    page: zod__WEBPACK_IMPORTED_MODULE_0__.z.coerce.number().min(1).default(1),\n    limit: zod__WEBPACK_IMPORTED_MODULE_0__.z.coerce.number().min(1).max(100).default(10),\n    sortBy: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"name\",\n        \"code\",\n        \"category\",\n        \"baseRate\",\n        \"rateEffectiveFrom\",\n        \"createdAt\"\n    ]).default(\"createdAt\"),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"asc\",\n        \"desc\"\n    ]).default(\"desc\")\n});\n/**\n * Component categories for dropdown\n */ const componentCategories = [\n    \"Steel\",\n    \"Rubber\",\n    \"Plastic\",\n    \"Electronics\",\n    \"Mechanical\",\n    \"Hydraulic\",\n    \"Pneumatic\",\n    \"Electrical\",\n    \"Fasteners\",\n    \"Bearings\",\n    \"Motors\",\n    \"Sensors\",\n    \"Other\"\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/components/validation/componentSchemas.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/response.js":
/*!*********************************!*\
  !*** ./src/lib/api/response.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiResponse: () => (/* binding */ ApiResponse),\n/* harmony export */   createdResponse: () => (/* binding */ createdResponse),\n/* harmony export */   errorResponse: () => (/* binding */ errorResponse),\n/* harmony export */   forbiddenResponse: () => (/* binding */ forbiddenResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   noContentResponse: () => (/* binding */ noContentResponse),\n/* harmony export */   notFoundResponse: () => (/* binding */ notFoundResponse),\n/* harmony export */   paginatedResponse: () => (/* binding */ paginatedResponse),\n/* harmony export */   serverErrorResponse: () => (/* binding */ serverErrorResponse),\n/* harmony export */   successResponse: () => (/* binding */ successResponse),\n/* harmony export */   unauthorizedResponse: () => (/* binding */ unauthorizedResponse),\n/* harmony export */   validationErrorResponse: () => (/* binding */ validationErrorResponse),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\n/**\n * Standard API response structure\n */ class ApiResponse {\n    constructor(success = true, data = null, message = null, errors = null, meta = null){\n        this.success = success;\n        this.data = data;\n        this.message = message;\n        this.errors = errors;\n        this.meta = meta;\n        this.timestamp = new Date().toISOString();\n    }\n}\n/**\n * Success response helper\n */ function successResponse(data = null, message = null, meta = null, status = 200) {\n    const response = new ApiResponse(true, data, message, null, meta);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Error response helper\n */ function errorResponse(message, errors = null, status = 400) {\n    const response = new ApiResponse(false, null, message, errors);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Validation error response\n */ function validationErrorResponse(errors, message = \"Validation failed\") {\n    return errorResponse(message, errors, 422);\n}\n/**\n * Unauthorized response\n */ function unauthorizedResponse(message = \"Unauthorized\") {\n    return errorResponse(message, null, 401);\n}\n/**\n * Forbidden response\n */ function forbiddenResponse(message = \"Forbidden\") {\n    return errorResponse(message, null, 403);\n}\n/**\n * Not found response\n */ function notFoundResponse(message = \"Resource not found\") {\n    return errorResponse(message, null, 404);\n}\n/**\n * Internal server error response\n */ function serverErrorResponse(message = \"Internal server error\") {\n    return errorResponse(message, null, 500);\n}\n/**\n * Created response\n */ function createdResponse(data, message = \"Resource created successfully\") {\n    return successResponse(data, message, null, 201);\n}\n/**\n * No content response\n */ function noContentResponse() {\n    return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](null, {\n        status: 204\n    });\n}\n/**\n * Paginated response helper\n */ function paginatedResponse(data, pagination, message = null) {\n    const meta = {\n        pagination: {\n            page: pagination.page,\n            limit: pagination.limit,\n            total: pagination.total,\n            totalPages: Math.ceil(pagination.total / pagination.limit),\n            hasNext: pagination.page < Math.ceil(pagination.total / pagination.limit),\n            hasPrev: pagination.page > 1\n        }\n    };\n    return successResponse(data, message, meta);\n}\n/**\n * Handle API errors consistently\n */ function handleApiError(error) {\n    console.error(\"API Error:\", error);\n    // Prisma errors\n    if (error.code === \"P2002\") {\n        return errorResponse(\"Resource already exists\", null, 409);\n    }\n    if (error.code === \"P2025\") {\n        return notFoundResponse(\"Resource not found\");\n    }\n    // Validation errors (Zod)\n    if (error.name === \"ZodError\") {\n        const validationErrors = error.errors.map((err)=>({\n                field: err.path.join(\".\"),\n                message: err.message,\n                code: err.code\n            }));\n        return validationErrorResponse(validationErrors);\n    }\n    // JWT errors\n    if (error.name === \"JsonWebTokenError\") {\n        return unauthorizedResponse(\"Invalid token\");\n    }\n    if (error.name === \"TokenExpiredError\") {\n        return unauthorizedResponse(\"Token expired\");\n    }\n    // Custom application errors\n    if (error.message === \"Invalid credentials\") {\n        return unauthorizedResponse(\"Invalid email or password\");\n    }\n    if (error.message === \"User not found\") {\n        return notFoundResponse(\"User not found\");\n    }\n    if (error.message === \"Insufficient permissions\") {\n        return forbiddenResponse(\"Insufficient permissions\");\n    }\n    // Default server error\n    return serverErrorResponse();\n}\n/**\n * Async error handler wrapper\n */ function withErrorHandling(handler) {\n    return async (request, context)=>{\n        try {\n            return await handler(request, context);\n        } catch (error) {\n            return handleApiError(error);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/response.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/permissions.js":
/*!*************************************!*\
  !*** ./src/lib/auth/permissions.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES),\n/* harmony export */   canAccessResource: () => (/* binding */ canAccessResource),\n/* harmony export */   canManageUser: () => (/* binding */ canManageUser),\n/* harmony export */   filterByPermissions: () => (/* binding */ filterByPermissions),\n/* harmony export */   getRolePermissions: () => (/* binding */ getRolePermissions),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRoleLevel: () => (/* binding */ hasRoleLevel),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole)\n/* harmony export */ });\n/**\n * User roles hierarchy\n */ const USER_ROLES = {\n    ADMIN: \"ADMIN\",\n    MANAGER: \"MANAGER\",\n    USER: \"USER\"\n};\n/**\n * Role hierarchy levels (higher number = more permissions)\n */ const ROLE_LEVELS = {\n    [USER_ROLES.USER]: 1,\n    [USER_ROLES.MANAGER]: 2,\n    [USER_ROLES.ADMIN]: 3\n};\n/**\n * Permissions for different actions\n */ const PERMISSIONS = {\n    // User management\n    USER_CREATE: \"user:create\",\n    USER_READ: \"user:read\",\n    USER_UPDATE: \"user:update\",\n    USER_DELETE: \"user:delete\",\n    USER_INVITE: \"user:invite\",\n    USER_ACTIVATE: \"user:activate\",\n    USER_DEACTIVATE: \"user:deactivate\",\n    // Role management\n    ROLE_ASSIGN: \"role:assign\",\n    ROLE_VIEW: \"role:view\",\n    // System\n    AUDIT_VIEW: \"audit:view\",\n    SYSTEM_CONFIG: \"system:config\"\n};\n/**\n * Role-based permissions mapping\n */ const ROLE_PERMISSIONS = {\n    [USER_ROLES.ADMIN]: [\n        PERMISSIONS.USER_CREATE,\n        PERMISSIONS.USER_READ,\n        PERMISSIONS.USER_UPDATE,\n        PERMISSIONS.USER_DELETE,\n        PERMISSIONS.USER_INVITE,\n        PERMISSIONS.USER_ACTIVATE,\n        PERMISSIONS.USER_DEACTIVATE,\n        PERMISSIONS.ROLE_ASSIGN,\n        PERMISSIONS.ROLE_VIEW,\n        PERMISSIONS.AUDIT_VIEW,\n        PERMISSIONS.SYSTEM_CONFIG\n    ],\n    [USER_ROLES.MANAGER]: [\n        PERMISSIONS.USER_CREATE,\n        PERMISSIONS.USER_READ,\n        PERMISSIONS.USER_UPDATE,\n        PERMISSIONS.USER_INVITE,\n        PERMISSIONS.USER_ACTIVATE,\n        PERMISSIONS.USER_DEACTIVATE,\n        PERMISSIONS.ROLE_VIEW,\n        PERMISSIONS.AUDIT_VIEW\n    ],\n    [USER_ROLES.USER]: [\n        PERMISSIONS.USER_READ\n    ]\n};\n/**\n * Check if user has specific permission\n */ function hasPermission(userRole, permission) {\n    if (!userRole || !permission) return false;\n    const rolePermissions = ROLE_PERMISSIONS[userRole] || [];\n    return rolePermissions.includes(permission);\n}\n/**\n * Check if user has any of the specified permissions\n */ function hasAnyPermission(userRole, permissions) {\n    if (!userRole || !permissions || !Array.isArray(permissions)) return false;\n    return permissions.some((permission)=>hasPermission(userRole, permission));\n}\n/**\n * Check if user has all of the specified permissions\n */ function hasAllPermissions(userRole, permissions) {\n    if (!userRole || !permissions || !Array.isArray(permissions)) return false;\n    return permissions.every((permission)=>hasPermission(userRole, permission));\n}\n/**\n * Check if user role is higher than or equal to required role\n */ function hasRoleLevel(userRole, requiredRole) {\n    if (!userRole || !requiredRole) return false;\n    const userLevel = ROLE_LEVELS[userRole] || 0;\n    const requiredLevel = ROLE_LEVELS[requiredRole] || 0;\n    return userLevel >= requiredLevel;\n}\n/**\n * Check if user can manage another user (based on role hierarchy)\n */ function canManageUser(managerRole, targetRole) {\n    if (!managerRole || !targetRole) return false;\n    const managerLevel = ROLE_LEVELS[managerRole] || 0;\n    const targetLevel = ROLE_LEVELS[targetRole] || 0;\n    // Can manage users with lower or equal role level\n    return managerLevel >= targetLevel;\n}\n/**\n * Get all permissions for a role\n */ function getRolePermissions(role) {\n    return ROLE_PERMISSIONS[role] || [];\n}\n/**\n * Check if user can access resource\n */ function canAccessResource(userRole, resource, action = \"read\") {\n    const permission = `${resource}:${action}`;\n    return hasPermission(userRole, permission);\n}\n/**\n * Filter data based on user permissions\n */ function filterByPermissions(data, userRole, filterFn) {\n    if (!Array.isArray(data)) return data;\n    return data.filter((item)=>filterFn(item, userRole));\n}\n/**\n * Permission middleware helper\n */ function requirePermission(permission) {\n    return (userRole)=>{\n        if (!hasPermission(userRole, permission)) {\n            throw new Error(`Insufficient permissions. Required: ${permission}`);\n        }\n        return true;\n    };\n}\n/**\n * Role middleware helper\n */ function requireRole(requiredRole) {\n    return (userRole)=>{\n        if (!hasRoleLevel(userRole, requiredRole)) {\n            throw new Error(`Insufficient role level. Required: ${requiredRole}`);\n        }\n        return true;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/permissions.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/session.js":
/*!*********************************!*\
  !*** ./src/lib/auth/session.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   clearAuthCookie: () => (/* binding */ clearAuthCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   deleteAllUserSessions: () => (/* binding */ deleteAllUserSessions),\n/* harmony export */   deleteSession: () => (/* binding */ deleteSession),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   setAuthCookie: () => (/* binding */ setAuthCookie),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n/* harmony import */ var _lib_config_env__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/config/env */ \"(rsc)/./src/lib/config/env.js\");\n\n\n\n\nconst JWT_SECRET = _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].JWT_SECRET;\nconst TOKEN_EXPIRY = \"7d\" // 7 days\n;\nconst COOKIE_NAME = \"auth-token\";\n/**\n * Generate JWT token for user\n */ function generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: TOKEN_EXPIRY,\n        issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n        audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n    });\n}\n/**\n * Verify JWT token\n */ function verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET, {\n            issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n            audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n        });\n    } catch (error) {\n        throw new Error(\"Invalid token\");\n    }\n}\n/**\n * Create session for user\n */ async function createSession(userId, userAgent = null, ipAddress = null) {\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now\n    ;\n    // Generate JWT token\n    const tokenPayload = {\n        userId,\n        sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    };\n    const token = generateToken(tokenPayload);\n    // Store session in database\n    const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.create({\n        data: {\n            userId,\n            token,\n            expiresAt,\n            userAgent,\n            ipAddress\n        }\n    });\n    return {\n        session,\n        token\n    };\n}\n/**\n * Get session from token\n */ async function getSession(token) {\n    if (!token) return null;\n    try {\n        // Verify JWT token\n        const payload = verifyToken(token);\n        // Get session from database\n        const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.findUnique({\n            where: {\n                token\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        role: true,\n                        status: true,\n                        avatar: true\n                    }\n                }\n            }\n        });\n        if (!session || session.expiresAt < new Date()) {\n            // Session expired or not found\n            if (session) {\n                await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n                    where: {\n                        id: session.id\n                    }\n                });\n            }\n            return null;\n        }\n        return session;\n    } catch (error) {\n        return null;\n    }\n}\n/**\n * Delete session\n */ async function deleteSession(token) {\n    if (!token) return;\n    try {\n        await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n            where: {\n                token\n            }\n        });\n    } catch (error) {\n    // Session might not exist, ignore error\n    }\n}\n/**\n * Delete all sessions for user\n */ async function deleteAllUserSessions(userId) {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            userId\n        }\n    });\n}\n/**\n * Clean up expired sessions\n */ async function cleanupExpiredSessions() {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            expiresAt: {\n                lt: new Date()\n            }\n        }\n    });\n}\n/**\n * Set auth cookie\n */ function setAuthCookie(token) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.set(COOKIE_NAME, token, {\n        httpOnly: true,\n        secure: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].NODE_ENV === \"production\",\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\n/**\n * Clear auth cookie\n */ function clearAuthCookie() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.delete(COOKIE_NAME);\n}\n/**\n * Get auth token from cookies\n */ function getAuthToken() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return cookieStore.get(COOKIE_NAME)?.value;\n}\n/**\n * Get current user from session\n */ async function getCurrentUser() {\n    const token = getAuthToken();\n    if (!token) return null;\n    const session = await getSession(token);\n    return session?.user || null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/session.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/config/env.js":
/*!*******************************!*\
  !*** ./src/lib/config/env.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   env: () => (/* binding */ env)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\nconst envSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"development\",\n        \"production\",\n        \"test\"\n    ]).default(\"development\"),\n    DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Database URL is required\"),\n    NEXTAUTH_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"NextAuth secret is required\"),\n    NEXTAUTH_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    JWT_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"JWT secret is required\"),\n    APP_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().default(\"http://localhost:3000\"),\n    APP_NAME: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default(\"Coveyor POC\"),\n    // Email configuration (optional)\n    SMTP_HOST: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_USER: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PASS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    FROM_EMAIL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email().optional()\n});\nfunction validateEnv() {\n    try {\n        return envSchema.parse(process.env);\n    } catch (error) {\n        console.error(\"❌ Invalid environment variables:\");\n        console.error(error.errors);\n        throw new Error(\"Environment validation failed\");\n    }\n}\nconst env = validateEnv();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (env);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config/env.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/prisma.js":
/*!******************************!*\
  !*** ./src/lib/db/prisma.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3ByaXNtYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUU7QUFFbEUsSUFBSUksSUFBeUIsRUFBYztJQUN6Q0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBQzNCO0FBRUEsaUVBQWVBLE1BQU1BLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3ZleW9yLXBvYy8uL3NyYy9saWIvZGIvcHJpc21hLmpzP2QyZTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXNcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbn1cblxuZXhwb3J0IGRlZmF1bHQgcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/prisma.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomponents%2Froute&page=%2Fapi%2Fcomponents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomponents%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();