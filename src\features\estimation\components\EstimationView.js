'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Edit, ArrowLeft, FileText, CheckCircle, Calculator } from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { useToast } from '@/hooks/useToast'

export default function EstimationView({ estimation, onEdit, onClose }) {
  const { apiCall } = useApi()
  const { showToast } = useToast()

  if (!estimation) return null

  const getStatusBadge = (status) => {
    const variants = {
      'Draft': 'secondary',
      'Final': 'success',
      'Sent': 'warning',
      'Approved': 'success'
    }
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatDateTime = (date) => {
    return new Date(date).toLocaleString('en-IN')
  }

  const handleFinalize = async () => {
    if (!confirm('Are you sure you want to finalize this estimation? This action cannot be undone.')) return

    try {
      const response = await apiCall(`/api/estimations/${estimation.id}/finalize`, 'PUT')
      
      if (response.success) {
        showToast('Estimation finalized successfully', 'success')
        window.location.reload()
      }
    } catch (error) {
      showToast('Failed to finalize estimation', 'error')
    }
  }

  // Calculate breakdown percentages
  const totalMaterial = (estimation.equipmentCost || 0) + (estimation.componentCost || 0)
  const subtotal = totalMaterial + (estimation.laborCost || 0) + (estimation.overheadCost || 0)
  const discountAmount = subtotal * ((estimation.discountPercent || 0) / 100)
  const afterDiscount = subtotal - discountAmount
  const markupAmount = afterDiscount * ((estimation.markupPercent || 0) / 100)

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onClose}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to List
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Estimation Sheet v{estimation.estimationVersion}
            </h1>
            <p className="text-gray-600 mt-1">{estimation.plan?.planName}</p>
          </div>
        </div>
        <div className="flex space-x-2">
          {estimation.status === 'Draft' && (
            <>
              <Button onClick={onEdit} variant="outline">
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button onClick={handleFinalize} className="btn-gradient-success">
                <CheckCircle className="w-4 h-4 mr-2" />
                Finalize
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Plan Information */}
      {estimation.plan && (
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Plan Details
              </span>
              {getStatusBadge(estimation.status)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="text-sm font-medium text-gray-500">Plan Name</label>
                <p className="text-lg font-semibold text-gray-900">{estimation.plan.planName}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Conveyor Type</label>
                <p className="text-lg text-gray-900">{estimation.plan.conveyorType}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Total Length</label>
                <p className="text-lg text-gray-900">{estimation.plan.totalLengthM} meters</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Load Type</label>
                <p className="text-lg text-gray-900">{estimation.plan.loadType}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Estimation Version</label>
                <p className="text-lg text-gray-900">v{estimation.estimationVersion}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Generated Date</label>
                <p className="text-lg text-gray-900">{formatDate(estimation.generatedAt)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Cost Breakdown */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calculator className="w-5 h-5 mr-2" />
            Cost Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Material Costs */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <label className="text-sm font-medium text-blue-600">Equipment Cost</label>
                <p className="text-2xl font-bold text-blue-900">
                  {estimation.equipmentCost ? formatCurrency(estimation.equipmentCost) : '₹0.00'}
                </p>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <label className="text-sm font-medium text-green-600">Component Cost</label>
                <p className="text-2xl font-bold text-green-900">
                  {estimation.componentCost ? formatCurrency(estimation.componentCost) : '₹0.00'}
                </p>
              </div>
            </div>

            {/* Other Costs */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-yellow-50 p-4 rounded-lg">
                <label className="text-sm font-medium text-yellow-600">Labor Cost</label>
                <p className="text-2xl font-bold text-yellow-900">
                  {estimation.laborCost ? formatCurrency(estimation.laborCost) : '₹0.00'}
                </p>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg">
                <label className="text-sm font-medium text-purple-600">Overhead Cost</label>
                <p className="text-2xl font-bold text-purple-900">
                  {estimation.overheadCost ? formatCurrency(estimation.overheadCost) : '₹0.00'}
                </p>
              </div>
            </div>

            {/* Adjustments */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-red-50 p-4 rounded-lg">
                <label className="text-sm font-medium text-red-600">Discount</label>
                <p className="text-lg font-semibold text-red-900">
                  {estimation.discountPercent || 0}% ({formatCurrency(discountAmount)})
                </p>
              </div>
              
              <div className="bg-indigo-50 p-4 rounded-lg">
                <label className="text-sm font-medium text-indigo-600">Markup</label>
                <p className="text-lg font-semibold text-indigo-900">
                  {estimation.markupPercent || 0}% ({formatCurrency(markupAmount)})
                </p>
              </div>
            </div>

            {/* Total Cost */}
            <div className="mt-6 p-6 bg-gradient-to-r from-green-100 to-blue-100 rounded-lg border-2 border-green-300">
              <div className="flex justify-between items-center">
                <span className="text-xl font-semibold text-gray-800">Total Project Cost:</span>
                <span className="text-3xl font-bold text-green-800">
                  {estimation.totalCost ? formatCurrency(estimation.totalCost) : '₹0.00'}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notes */}
      {estimation.notes && (
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-900 whitespace-pre-wrap">{estimation.notes}</p>
          </CardContent>
        </Card>
      )}

      {/* Other Versions */}
      {estimation.plan?.estimationSheets && estimation.plan.estimationSheets.length > 1 && (
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Other Versions ({estimation.plan.estimationSheets.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {estimation.plan.estimationSheets
                .filter(sheet => sheet.id !== estimation.id)
                .map((sheet) => (
                <div key={sheet.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">Version {sheet.estimationVersion}</p>
                    <p className="text-sm text-gray-600">Generated: {formatDateTime(sheet.generatedAt)}</p>
                  </div>
                  <div className="text-right">
                    {sheet.totalCost && (
                      <p className="font-semibold text-gray-900">
                        {formatCurrency(sheet.totalCost)}
                      </p>
                    )}
                    <Badge variant={sheet.status === 'Final' ? 'success' : 'secondary'} className="text-xs">
                      {sheet.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Audit Information */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle>Audit Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">Generated At</label>
              <p className="text-gray-900">{formatDateTime(estimation.generatedAt)}</p>
            </div>
            
            {estimation.generatedBy && (
              <div>
                <label className="text-sm font-medium text-gray-500">Generated By</label>
                <p className="text-gray-900">{estimation.generatedBy}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
