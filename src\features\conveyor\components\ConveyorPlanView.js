"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Edit,
  ArrowLeft,
  Settings,
  FileText,
  Send,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { loadTypes } from "@/features/conveyor/validation/conveyorSchemas";
import { useApi } from "@/hooks/useApi";
import { useToast } from "@/hooks/useToast";
import { LoadingSpinner } from "@/components/common/LoadingSpinner";
import PlanComponentsManager from "./PlanComponentsManager";

export default function ConveyorPlanView({
  plan,
  onEdit,
  onClose,
  onGenerateEstimation,
}) {
  const [fullPlan, setFullPlan] = useState(null);
  const [loading, setLoading] = useState(true);
  const { apiCall } = useApi();
  const { showToast } = useToast();

  // Fetch full plan details including components
  useEffect(() => {
    const fetchPlanDetails = async () => {
      if (!plan?.id) return;

      try {
        setLoading(true);
        console.log("🔍 Fetching plan details for ID:", plan.id);
        const response = await apiCall(`/api/conveyor-plans/${plan.id}`);

        if (response.success) {
          console.log("✅ Plan details fetched:", response.data);
          setFullPlan(response.data);
        } else {
          console.log("❌ Failed to fetch plan details:", response);
          showToast("Failed to load plan details", "error");
        }
      } catch (error) {
        console.error("💥 Error fetching plan details:", error);
        showToast("Failed to load plan details", "error");
      } finally {
        setLoading(false);
      }
    };

    fetchPlanDetails();
  }, [plan?.id, apiCall, showToast]);

  if (!plan) return null;

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto flex items-center justify-center py-12">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Loading plan details...</p>
        </div>
      </div>
    );
  }

  const displayPlan = fullPlan || plan;

  const getLoadTypeLabel = (loadType) => {
    const type = loadTypes.find((t) => t.value === loadType);
    return type ? type.label : loadType;
  };

  const getStatusBadge = (status) => {
    const variants = {
      Draft: "secondary",
      Submitted: "warning",
      Approved: "success",
      Rejected: "destructive",
    };
    return <Badge variant={variants[status] || "secondary"}>{status}</Badge>;
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString("en-IN", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatDateTime = (date) => {
    return new Date(date).toLocaleString("en-IN");
  };

  const handleSubmit = async () => {
    if (!confirm("Are you sure you want to submit this plan for approval?"))
      return;

    try {
      const response = await apiCall(
        `/api/conveyor-plans/${displayPlan.id}/submit`,
        "PUT"
      );

      if (response.success) {
        showToast("Plan submitted successfully", "success");
        // Refresh the plan data
        window.location.reload();
      }
    } catch (error) {
      showToast("Failed to submit plan", "error");
    }
  };

  const handleApprove = async () => {
    if (!confirm("Are you sure you want to approve this plan?")) return;

    try {
      const response = await apiCall(
        `/api/conveyor-plans/${displayPlan.id}/approve`,
        "PUT"
      );

      if (response.success) {
        showToast("Plan approved successfully", "success");
        window.location.reload();
      }
    } catch (error) {
      showToast("Failed to approve plan", "error");
    }
  };

  const handleReject = async () => {
    if (!confirm("Are you sure you want to reject this plan?")) return;

    try {
      const response = await apiCall(
        `/api/conveyor-plans/${displayPlan.id}/reject`,
        "PUT"
      );

      if (response.success) {
        showToast("Plan rejected successfully", "success");
        window.location.reload();
      }
    } catch (error) {
      showToast("Failed to reject plan", "error");
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onClose}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to List
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {displayPlan.planName}
            </h1>
            <p className="text-gray-600 mt-1">Conveyor Plan Details</p>
          </div>
        </div>
        <div className="flex space-x-2">
          {displayPlan.status === "Draft" && (
            <>
              <Button onClick={onEdit} variant="outline">
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button onClick={handleSubmit} className="btn-gradient-primary">
                <Send className="w-4 h-4 mr-2" />
                Submit
              </Button>
            </>
          )}
          {displayPlan.status === "Submitted" && (
            <>
              <Button
                onClick={handleReject}
                variant="outline"
                className="text-red-600"
              >
                <XCircle className="w-4 h-4 mr-2" />
                Reject
              </Button>
              <Button onClick={handleApprove} className="btn-gradient-success">
                <CheckCircle className="w-4 h-4 mr-2" />
                Approve
              </Button>
            </>
          )}
          {displayPlan.status === "Approved" && (
            <Button
              onClick={() => onGenerateEstimation(displayPlan)}
              className="btn-gradient-primary"
            >
              <FileText className="w-4 h-4 mr-2" />
              Generate Estimation
            </Button>
          )}
        </div>
      </div>

      {/* Status and Basic Info */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Plan Overview
            </span>
            {getStatusBadge(displayPlan.status)}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Plan Name
              </label>
              <p className="text-lg font-semibold text-gray-900">
                {displayPlan.planName}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Conveyor Type
              </label>
              <p className="text-lg text-gray-900">
                {displayPlan.conveyorType}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Total Length
              </label>
              <p className="text-lg text-gray-900">
                {displayPlan.totalLengthM} meters
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Load Type
              </label>
              <p className="text-lg text-gray-900">
                {getLoadTypeLabel(displayPlan.loadType)}
              </p>
            </div>

            {displayPlan.capacityTph && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Capacity
                </label>
                <p className="text-lg text-gray-900">
                  {displayPlan.capacityTph} TPH
                </p>
              </div>
            )}

            {displayPlan.inclinationAngle !== null && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Inclination Angle
                </label>
                <p className="text-lg text-gray-900">
                  {displayPlan.inclinationAngle}°
                </p>
              </div>
            )}

            {displayPlan.driveType && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Drive Type
                </label>
                <p className="text-lg text-gray-900">{displayPlan.driveType}</p>
              </div>
            )}

            {displayPlan.environment && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Environment
                </label>
                <p className="text-lg text-gray-900">
                  {displayPlan.environment}
                </p>
              </div>
            )}

            {displayPlan.region && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Region
                </label>
                <p className="text-lg text-gray-900">{displayPlan.region}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Site Conditions and Requirements */}
      {(displayPlan.siteConditions || displayPlan.specialRequirements) && (
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Additional Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {displayPlan.siteConditions && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Site Conditions
                </label>
                <p className="text-gray-900 mt-1 whitespace-pre-wrap">
                  {displayPlan.siteConditions}
                </p>
              </div>
            )}

            {displayPlan.specialRequirements && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Special Requirements
                </label>
                <p className="text-gray-900 mt-1 whitespace-pre-wrap">
                  {displayPlan.specialRequirements}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Plan Components */}
      {displayPlan.planComponents && displayPlan.planComponents.length > 0 && (
        <PlanComponentsManager
          components={
            displayPlan.planComponents?.map((pc) => ({
              componentId: pc.componentId,
              component: pc.component,
              quantity: parseFloat(pc.quantity),
              unitPrice: parseFloat(pc.unitPrice),
              totalPrice: parseFloat(pc.totalPrice),
              notes: pc.notes,
            })) || []
          }
          onComponentsChange={() => {}} // Read-only in view mode
          disabled={true}
        />
      )}

      {/* Estimation Sheets */}
      {displayPlan.estimationSheets &&
        displayPlan.estimationSheets.length > 0 && (
          <Card className="card-vibrant">
            <CardHeader>
              <CardTitle>
                Estimation Sheets ({displayPlan.estimationSheets.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {displayPlan.estimationSheets.map((estimation) => (
                  <div
                    key={estimation.id}
                    className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                  >
                    <div>
                      <p className="font-medium text-gray-900">
                        Version {estimation.estimationVersion}
                      </p>
                      <p className="text-sm text-gray-600">
                        Generated: {formatDateTime(estimation.generatedAt)}
                      </p>
                    </div>
                    <div className="text-right">
                      {estimation.totalCost && (
                        <p className="font-semibold text-gray-900">
                          ₹
                          {new Intl.NumberFormat("en-IN").format(
                            estimation.totalCost
                          )}
                        </p>
                      )}
                      <Badge
                        variant={
                          estimation.status === "Final"
                            ? "success"
                            : "secondary"
                        }
                        className="text-xs"
                      >
                        {estimation.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

      {/* Audit Information */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle>Audit Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Created At
              </label>
              <p className="text-gray-900">
                {formatDateTime(displayPlan.createdAt)}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Last Updated
              </label>
              <p className="text-gray-900">
                {formatDateTime(displayPlan.updatedAt)}
              </p>
            </div>

            {displayPlan.createdBy && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Created By
                </label>
                <p className="text-gray-900">{displayPlan.createdBy}</p>
              </div>
            )}

            {displayPlan.updatedBy && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Updated By
                </label>
                <p className="text-gray-900">{displayPlan.updatedBy}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
