"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Edit,
  ArrowLeft,
  Settings,
  FileText,
  Send,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { loadTypes } from "@/features/conveyor/validation/conveyorSchemas";
import { useApi } from "@/hooks/useApi";
import { useToast } from "@/hooks/useToast";
import PlanComponentsManager from "./PlanComponentsManager";

export default function ConveyorPlanView({
  plan,
  onEdit,
  onClose,
  onGenerateEstimation,
}) {
  const { apiCall } = useApi();
  const { showToast } = useToast();

  if (!plan) return null;

  const getLoadTypeLabel = (loadType) => {
    const type = loadTypes.find((t) => t.value === loadType);
    return type ? type.label : loadType;
  };

  const getStatusBadge = (status) => {
    const variants = {
      Draft: "secondary",
      Submitted: "warning",
      Approved: "success",
      Rejected: "destructive",
    };
    return <Badge variant={variants[status] || "secondary"}>{status}</Badge>;
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString("en-IN", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatDateTime = (date) => {
    return new Date(date).toLocaleString("en-IN");
  };

  const handleSubmit = async () => {
    if (!confirm("Are you sure you want to submit this plan for approval?"))
      return;

    try {
      const response = await apiCall(
        `/api/conveyor-plans/${plan.id}/submit`,
        "PUT"
      );

      if (response.success) {
        showToast("Plan submitted successfully", "success");
        // Refresh the plan data
        window.location.reload();
      }
    } catch (error) {
      showToast("Failed to submit plan", "error");
    }
  };

  const handleApprove = async () => {
    if (!confirm("Are you sure you want to approve this plan?")) return;

    try {
      const response = await apiCall(
        `/api/conveyor-plans/${plan.id}/approve`,
        "PUT"
      );

      if (response.success) {
        showToast("Plan approved successfully", "success");
        window.location.reload();
      }
    } catch (error) {
      showToast("Failed to approve plan", "error");
    }
  };

  const handleReject = async () => {
    if (!confirm("Are you sure you want to reject this plan?")) return;

    try {
      const response = await apiCall(
        `/api/conveyor-plans/${plan.id}/reject`,
        "PUT"
      );

      if (response.success) {
        showToast("Plan rejected successfully", "success");
        window.location.reload();
      }
    } catch (error) {
      showToast("Failed to reject plan", "error");
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onClose}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to List
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {plan.planName}
            </h1>
            <p className="text-gray-600 mt-1">Conveyor Plan Details</p>
          </div>
        </div>
        <div className="flex space-x-2">
          {plan.status === "Draft" && (
            <>
              <Button onClick={onEdit} variant="outline">
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
              <Button onClick={handleSubmit} className="btn-gradient-primary">
                <Send className="w-4 h-4 mr-2" />
                Submit
              </Button>
            </>
          )}
          {plan.status === "Submitted" && (
            <>
              <Button
                onClick={handleReject}
                variant="outline"
                className="text-red-600"
              >
                <XCircle className="w-4 h-4 mr-2" />
                Reject
              </Button>
              <Button onClick={handleApprove} className="btn-gradient-success">
                <CheckCircle className="w-4 h-4 mr-2" />
                Approve
              </Button>
            </>
          )}
          {plan.status === "Approved" && (
            <Button
              onClick={() => onGenerateEstimation(plan)}
              className="btn-gradient-primary"
            >
              <FileText className="w-4 h-4 mr-2" />
              Generate Estimation
            </Button>
          )}
        </div>
      </div>

      {/* Status and Basic Info */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Plan Overview
            </span>
            {getStatusBadge(plan.status)}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Plan Name
              </label>
              <p className="text-lg font-semibold text-gray-900">
                {plan.planName}
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Conveyor Type
              </label>
              <p className="text-lg text-gray-900">{plan.conveyorType}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Total Length
              </label>
              <p className="text-lg text-gray-900">
                {plan.totalLengthM} meters
              </p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Load Type
              </label>
              <p className="text-lg text-gray-900">
                {getLoadTypeLabel(plan.loadType)}
              </p>
            </div>

            {plan.capacityTph && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Capacity
                </label>
                <p className="text-lg text-gray-900">{plan.capacityTph} TPH</p>
              </div>
            )}

            {plan.inclinationAngle !== null && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Inclination Angle
                </label>
                <p className="text-lg text-gray-900">
                  {plan.inclinationAngle}°
                </p>
              </div>
            )}

            {plan.driveType && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Drive Type
                </label>
                <p className="text-lg text-gray-900">{plan.driveType}</p>
              </div>
            )}

            {plan.environment && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Environment
                </label>
                <p className="text-lg text-gray-900">{plan.environment}</p>
              </div>
            )}

            {plan.region && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Region
                </label>
                <p className="text-lg text-gray-900">{plan.region}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Site Conditions and Requirements */}
      {(plan.siteConditions || plan.specialRequirements) && (
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Additional Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {plan.siteConditions && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Site Conditions
                </label>
                <p className="text-gray-900 mt-1 whitespace-pre-wrap">
                  {plan.siteConditions}
                </p>
              </div>
            )}

            {plan.specialRequirements && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Special Requirements
                </label>
                <p className="text-gray-900 mt-1 whitespace-pre-wrap">
                  {plan.specialRequirements}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Plan Components */}
      {plan.planComponents && (
        <PlanComponentsManager
          components={
            plan.planComponents?.map((pc) => ({
              componentId: pc.componentId,
              component: pc.component,
              quantity: parseFloat(pc.quantity),
              unitPrice: parseFloat(pc.unitPrice),
              totalPrice: parseFloat(pc.totalPrice),
              notes: pc.notes,
            })) || []
          }
          onComponentsChange={() => {}} // Read-only in view mode
          disabled={true}
        />
      )}

      {/* Estimation Sheets */}
      {plan.estimationSheets && plan.estimationSheets.length > 0 && (
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>
              Estimation Sheets ({plan.estimationSheets.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {plan.estimationSheets.map((estimation) => (
                <div
                  key={estimation.id}
                  className="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <p className="font-medium text-gray-900">
                      Version {estimation.estimationVersion}
                    </p>
                    <p className="text-sm text-gray-600">
                      Generated: {formatDateTime(estimation.generatedAt)}
                    </p>
                  </div>
                  <div className="text-right">
                    {estimation.totalCost && (
                      <p className="font-semibold text-gray-900">
                        ₹
                        {new Intl.NumberFormat("en-IN").format(
                          estimation.totalCost
                        )}
                      </p>
                    )}
                    <Badge
                      variant={
                        estimation.status === "Final" ? "success" : "secondary"
                      }
                      className="text-xs"
                    >
                      {estimation.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Audit Information */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle>Audit Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">
                Created At
              </label>
              <p className="text-gray-900">{formatDateTime(plan.createdAt)}</p>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-500">
                Last Updated
              </label>
              <p className="text-gray-900">{formatDateTime(plan.updatedAt)}</p>
            </div>

            {plan.createdBy && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Created By
                </label>
                <p className="text-gray-900">{plan.createdBy}</p>
              </div>
            )}

            {plan.updatedBy && (
              <div>
                <label className="text-sm font-medium text-gray-500">
                  Updated By
                </label>
                <p className="text-gray-900">{plan.updatedBy}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
