import { getCurrentUser } from "@/lib/auth/session";
import {
  getComponents,
  createComponent,
} from "@/features/components/services/componentService";
import {
  componentFilterSchema,
  createComponentSchema,
} from "@/features/components/validation/componentSchemas";
import {
  successResponse,
  unauthorizedResponse,
  forbiddenResponse,
  handleApiError,
  paginatedResponse,
} from "@/lib/api/response";
import { hasPermission, PERMISSIONS } from "@/lib/auth/permissions";

export async function GET(request) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return unauthorizedResponse("Authentication required");
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.COMPONENT_READ)) {
      return forbiddenResponse("Insufficient permissions to view components");
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const filters = componentFilterSchema.parse({
      search: searchParams.get("search") || "",
      category: searchParams.get("category") || undefined,
      equipmentId: searchParams.get("equipmentId") || undefined,
      status: searchParams.get("status")
        ? searchParams.get("status") === "true"
        : undefined,
      page: searchParams.get("page") || "1",
      limit: searchParams.get("limit") || "10",
      sortBy: searchParams.get("sortBy") || "createdAt",
      sortOrder: searchParams.get("sortOrder") || "desc",
    });

    // Get components
    const result = await getComponents(filters);

    return paginatedResponse(
      result.components,
      result.pagination,
      "Components retrieved successfully"
    );
  } catch (error) {
    return handleApiError(error);
  }
}

export async function POST(request) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return unauthorizedResponse("Authentication required");
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.COMPONENT_CREATE)) {
      return forbiddenResponse("Insufficient permissions to create components");
    }

    const body = await request.json();

    // Validate request body
    const validatedData = createComponentSchema.parse(body);

    // Create component
    const newComponent = await createComponent(validatedData, user.id);

    return successResponse(
      newComponent,
      "Component created successfully",
      null,
      201
    );
  } catch (error) {
    return handleApiError(error);
  }
}
