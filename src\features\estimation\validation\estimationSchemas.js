import { z } from 'zod'

/**
 * Estimation Sheet validation schema
 */
export const createEstimationSchema = z.object({
  planId: z
    .number()
    .int()
    .positive('Plan is required'),
  estimationVersion: z
    .number()
    .int()
    .positive('Version must be a positive number')
    .default(1),
  equipmentCost: z
    .number()
    .min(0, 'Equipment cost cannot be negative')
    .max(999999999999, 'Equipment cost is too large')
    .optional(),
  componentCost: z
    .number()
    .min(0, 'Component cost cannot be negative')
    .max(999999999999, 'Component cost is too large')
    .optional(),
  laborCost: z
    .number()
    .min(0, 'Labor cost cannot be negative')
    .max(999999999999, 'Labor cost is too large')
    .optional(),
  overheadCost: z
    .number()
    .min(0, 'Overhead cost cannot be negative')
    .max(999999999999, 'Overhead cost is too large')
    .optional(),
  discountPercent: z
    .number()
    .min(0, 'Discount cannot be negative')
    .max(100, 'Discount cannot exceed 100%')
    .optional(),
  markupPercent: z
    .number()
    .min(0, 'Markup cannot be negative')
    .max(1000, 'Markup cannot exceed 1000%')
    .optional(),
  totalCost: z
    .number()
    .min(0, 'Total cost cannot be negative')
    .max(999999999999, 'Total cost is too large')
    .optional(),
  notes: z
    .string()
    .max(2000, 'Notes must be less than 2000 characters')
    .optional(),
  status: z
    .enum(['Draft', 'Final', 'Sent', 'Approved'])
    .default('Draft'),
})

export const updateEstimationSchema = createEstimationSchema.partial()

/**
 * Estimation search/filter validation schema
 */
export const estimationFilterSchema = z.object({
  search: z.string().optional(),
  planId: z.coerce.number().int().positive().optional(),
  status: z.enum(['Draft', 'Final', 'Sent', 'Approved']).optional(),
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  sortBy: z.enum(['estimationVersion', 'totalCost', 'status', 'generatedAt']).default('generatedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
})

/**
 * Equipment selection for estimation
 */
export const equipmentSelectionSchema = z.object({
  equipmentId: z.number().int().positive(),
  quantity: z.number().positive('Quantity must be positive'),
  unitRate: z.number().positive('Unit rate must be positive'),
  totalCost: z.number().positive('Total cost must be positive'),
})

/**
 * Component selection for estimation
 */
export const componentSelectionSchema = z.object({
  componentId: z.number().int().positive(),
  quantity: z.number().positive('Quantity must be positive'),
  unitRate: z.number().positive('Unit rate must be positive'),
  totalCost: z.number().positive('Total cost must be positive'),
})

/**
 * Estimation calculation schema
 */
export const estimationCalculationSchema = z.object({
  planId: z.number().int().positive(),
  selectedEquipment: z.array(equipmentSelectionSchema).optional(),
  selectedComponents: z.array(componentSelectionSchema).optional(),
  laborCostPercentage: z.number().min(0).max(100).default(15),
  overheadCostPercentage: z.number().min(0).max(100).default(10),
  discountPercent: z.number().min(0).max(100).default(0),
  markupPercent: z.number().min(0).max(1000).default(20),
})

/**
 * Labor cost calculation methods
 */
export const laborCostMethods = [
  { value: 'percentage', label: 'Percentage of Material Cost', description: 'Calculate as % of equipment + component cost' },
  { value: 'fixed', label: 'Fixed Amount', description: 'Enter a fixed labor cost amount' },
  { value: 'hourly', label: 'Hourly Rate', description: 'Calculate based on hours and hourly rate' }
]

/**
 * Overhead cost calculation methods
 */
export const overheadCostMethods = [
  { value: 'percentage', label: 'Percentage of Total Cost', description: 'Calculate as % of material + labor cost' },
  { value: 'fixed', label: 'Fixed Amount', description: 'Enter a fixed overhead cost amount' }
]

/**
 * Standard labor rates by region (per hour)
 */
export const standardLaborRates = {
  'North India': 250,
  'South India': 200,
  'East India': 180,
  'West India': 280,
  'Central India': 200,
  'Northeast India': 150,
  'International': 500
}

/**
 * Standard overhead percentages by project type
 */
export const standardOverheadPercentages = {
  'Small Project': 8,
  'Medium Project': 12,
  'Large Project': 15,
  'Complex Project': 20
}
