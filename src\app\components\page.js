'use client'

import { useState } from 'react'
import { useAuth } from '@/features/auth/hooks/useAuth'
import ComponentList from '@/features/components/components/ComponentList'
import ComponentForm from '@/features/components/components/ComponentForm'
import ComponentView from '@/features/components/components/ComponentView'

export default function ComponentsPage() {
  const [currentView, setCurrentView] = useState('list') // 'list', 'create', 'edit', 'view'
  const [selectedComponent, setSelectedComponent] = useState(null)
  const { user } = useAuth()

  const handleCreateNew = () => {
    setSelectedComponent(null)
    setCurrentView('create')
  }

  const handleEdit = (component) => {
    setSelectedComponent(component)
    setCurrentView('edit')
  }

  const handleView = (component) => {
    setSelectedComponent(component)
    setCurrentView('view')
  }

  const handleSave = (component) => {
    setCurrentView('list')
    setSelectedComponent(null)
  }

  const handleCancel = () => {
    setCurrentView('list')
    setSelectedComponent(null)
  }

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {currentView === 'list' && (
        <ComponentList
          onCreateNew={handleCreateNew}
          onEdit={handleEdit}
          onView={handleView}
        />
      )}

      {(currentView === 'create' || currentView === 'edit') && (
        <ComponentForm
          component={selectedComponent}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      )}

      {currentView === 'view' && (
        <ComponentView
          component={selectedComponent}
          onEdit={() => handleEdit(selectedComponent)}
          onClose={handleCancel}
        />
      )}
    </div>
  )
}
