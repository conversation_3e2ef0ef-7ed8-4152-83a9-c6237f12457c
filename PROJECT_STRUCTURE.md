# Production-Ready Full-Stack Directory Structure

```
coveyor-poc/
├── .env.local                          # Environment variables for development
├── .env.example                        # Template for environment variables
├── .gitignore                          # Git ignore rules
├── package.json                        # Dependencies and scripts
├── next.config.js                      # Next.js configuration
├── tailwind.config.js                  # Tailwind CSS configuration
├── postcss.config.js                   # PostCSS configuration
├── jsconfig.json                       # JavaScript project configuration
├── middleware.js                       # Next.js middleware for route protection
├── README.md                           # Project documentation
│
├── prisma/                             # Database schema and migrations
│   ├── schema.prisma                   # Prisma schema definition
│   ├── migrations/                     # Database migration files
│   └── seed.js                         # Database seeding script
│
├── public/                             # Static assets
│   ├── images/                         # Image assets
│   ├── icons/                          # Icon assets
│   └── favicon.ico                     # Favicon
│
├── src/                                # Source code root
│   ├── app/                            # Next.js App Router pages
│   │   ├── globals.css                 # Global styles with Tailwind
│   │   ├── layout.js                   # Root layout component
│   │   ├── page.js                     # Home page (redirect to dashboard)
│   │   ├── loading.js                  # Global loading component
│   │   ├── error.js                    # Global error boundary
│   │   ├── not-found.js                # 404 page
│   │   │
│   │   ├── (auth)/                     # Auth route group
│   │   │   ├── login/
│   │   │   │   └── page.js             # Login page
│   │   │   └── layout.js               # Auth layout (no sidebar)
│   │   │
│   │   ├── (dashboard)/                # Protected dashboard routes
│   │   │   ├── dashboard/
│   │   │   │   └── page.js             # Main dashboard page
│   │   │   ├── users/                  # User management module
│   │   │   │   ├── page.js             # Users list page
│   │   │   │   ├── create/
│   │   │   │   │   └── page.js         # Create user page
│   │   │   │   └── [id]/
│   │   │   │       ├── page.js         # User detail/edit page
│   │   │   │       └── loading.js      # User detail loading
│   │   │   └── layout.js               # Dashboard layout (with sidebar)
│   │   │
│   │   └── api/                        # API routes
│   │       ├── auth/                   # Authentication endpoints
│   │       │   ├── login/
│   │       │   │   └── route.js        # POST /api/auth/login
│   │       │   ├── logout/
│   │       │   │   └── route.js        # POST /api/auth/logout
│   │       │   ├── me/
│   │       │   │   └── route.js        # GET /api/auth/me
│   │       │   └── refresh/
│   │       │       └── route.js        # POST /api/auth/refresh
│   │       │
│   │       └── users/                  # User management endpoints
│   │           ├── route.js            # GET /api/users, POST /api/users
│   │           ├── [id]/
│   │           │   └── route.js        # GET/PUT/DELETE /api/users/[id]
│   │           ├── invite/
│   │           │   └── route.js        # POST /api/users/invite
│   │           └── [id]/
│   │               ├── activate/
│   │               │   └── route.js    # PUT /api/users/[id]/activate
│   │               └── deactivate/
│   │                   └── route.js    # PUT /api/users/[id]/deactivate
│   │
│   ├── components/                     # Reusable UI components
│   │   ├── ui/                         # Shadcn UI base components
│   │   │   ├── button.js               # Base button component
│   │   │   ├── input.js                # Base input component
│   │   │   ├── card.js                 # Base card component
│   │   │   ├── dialog.js               # Base dialog/modal component
│   │   │   ├── alert.js                # Base alert component
│   │   │   ├── table.js                # Base table component
│   │   │   ├── form.js                 # Base form components
│   │   │   ├── badge.js                # Base badge component
│   │   │   ├── dropdown-menu.js        # Base dropdown component
│   │   │   └── toast.js                # Base toast component
│   │   │
│   │   ├── layout/                     # Layout components
│   │   │   ├── Header.js               # Main header component
│   │   │   ├── Sidebar.js              # Navigation sidebar
│   │   │   ├── Footer.js               # Footer component
│   │   │   └── Navigation.js           # Navigation menu component
│   │   │
│   │   └── common/                     # Shared common components
│   │       ├── LoadingSpinner.js       # Loading spinner component
│   │       ├── ErrorBoundary.js        # Error boundary wrapper
│   │       ├── ConfirmDialog.js        # Confirmation dialog
│   │       ├── DataTable.js            # Enhanced data table
│   │       ├── SearchInput.js          # Search input component
│   │       ├── StatusBadge.js          # Status indicator badge
│   │       └── PageHeader.js           # Page header with breadcrumbs
│   │
│   ├── features/                       # Feature-based modules
│   │   ├── auth/                       # Authentication feature
│   │   │   ├── components/             # Auth-specific UI components
│   │   │   │   ├── LoginForm.js        # Login form component
│   │   │   │   ├── AuthGuard.js        # Route protection component
│   │   │   │   └── SessionProvider.js  # Session context provider
│   │   │   ├── hooks/                  # Auth-related hooks
│   │   │   │   ├── useAuth.js          # Authentication hook
│   │   │   │   ├── useSession.js       # Session management hook
│   │   │   │   └── useLogin.js         # Login form hook
│   │   │   ├── services/               # Auth business logic
│   │   │   │   ├── authService.js      # Auth service layer
│   │   │   │   ├── sessionService.js   # Session management
│   │   │   │   └── tokenService.js     # Token handling
│   │   │   ├── validation/             # Auth validation schemas
│   │   │   │   └── authSchemas.js      # Zod schemas for auth
│   │   │   └── __tests__/              # Auth feature tests
│   │   │       ├── LoginForm.test.js   # Login form tests
│   │   │       └── authService.test.js # Auth service tests
│   │   │
│   │   └── users/                      # User management feature
│   │       ├── components/             # User-specific UI components
│   │       │   ├── UserList.js         # Users list component
│   │       │   ├── UserCard.js         # User card component
│   │       │   ├── UserForm.js         # User create/edit form
│   │       │   ├── UserDetail.js       # User detail view
│   │       │   ├── UserInviteForm.js   # User invitation form
│   │       │   ├── UserStatusToggle.js # Activate/deactivate toggle
│   │       │   └── UserRoleSelect.js   # Role selection component
│   │       ├── hooks/                  # User-related hooks
│   │       │   ├── useUsers.js         # Users data fetching
│   │       │   ├── useUser.js          # Single user operations
│   │       │   ├── useUserForm.js      # User form management
│   │       │   └── useUserActions.js   # User CRUD actions
│   │       ├── services/               # User business logic
│   │       │   ├── userService.js      # User service layer
│   │       │   ├── userController.js   # User API controller logic
│   │       │   └── userRepository.js   # User data access layer
│   │       ├── validation/             # User validation schemas
│   │       │   └── userSchemas.js      # Zod schemas for users
│   │       └── __tests__/              # User feature tests
│   │           ├── UserList.test.js    # User list tests
│   │           ├── UserForm.test.js    # User form tests
│   │           └── userService.test.js # User service tests
│   │
│   ├── lib/                            # Shared utilities and configurations
│   │   ├── auth/                       # Auth utilities
│   │   │   ├── config.js               # Auth configuration
│   │   │   ├── middleware.js           # Auth middleware helpers
│   │   │   ├── session.js              # Session utilities
│   │   │   └── permissions.js          # Permission checking utilities
│   │   ├── api/                        # API utilities
│   │   │   ├── client.js               # API client abstraction
│   │   │   ├── response.js             # Standardized API responses
│   │   │   ├── errors.js               # Error handling utilities
│   │   │   └── validation.js           # API validation helpers
│   │   ├── db/                         # Database utilities
│   │   │   ├── prisma.js               # Prisma client instance
│   │   │   ├── connection.js           # Database connection helpers
│   │   │   └── migrations.js           # Migration utilities
│   │   ├── utils/                      # General utilities
│   │   │   ├── constants.js            # App constants
│   │   │   ├── helpers.js              # Helper functions
│   │   │   ├── formatters.js           # Data formatting utilities
│   │   │   ├── validators.js           # Common validation functions
│   │   │   └── encryption.js           # Encryption/hashing utilities
│   │   └── config/                     # Configuration management
│   │       ├── env.js                  # Environment configuration
│   │       ├── database.js             # Database configuration
│   │       └── app.js                  # Application configuration
│   │
│   ├── hooks/                          # Shared custom hooks
│   │   ├── useApi.js                   # Generic API hook
│   │   ├── useLocalStorage.js          # Local storage hook
│   │   ├── useDebounce.js              # Debounce hook
│   │   ├── usePagination.js            # Pagination hook
│   │   └── useToast.js                 # Toast notification hook
│   │
│   ├── context/                        # React contexts
│   │   ├── AuthContext.js              # Authentication context
│   │   ├── ThemeContext.js             # Theme/UI context
│   │   └── ToastContext.js             # Toast notification context
│   │
│   └── styles/                         # Additional styles
│       ├── components.css              # Component-specific styles
│       ├── utilities.css               # Utility classes
│       └── themes.css                  # Theme definitions with vibrant colors
│
├── __tests__/                          # Global tests
│   ├── setup.js                        # Test setup configuration
│   ├── utils.js                        # Test utilities
│   └── integration/                    # Integration tests
│       ├── auth.test.js                # Auth flow integration tests
│       └── users.test.js               # User management integration tests
│
├── docs/                               # Documentation
│   ├── API.md                          # API documentation
│   ├── DEPLOYMENT.md                   # Deployment guide
│   └── DEVELOPMENT.md                  # Development setup guide
│
└── scripts/                            # Build and deployment scripts
    ├── build.js                        # Build script
    ├── deploy.js                       # Deployment script
    └── seed-db.js                      # Database seeding script
```

## Key Architecture Decisions:

### 🏗️ **Clean Architecture Principles:**
- **Feature-based organization**: Each feature (auth, users) is self-contained
- **Separation of concerns**: Components, hooks, services, and validation are clearly separated
- **Dependency inversion**: Services depend on abstractions, not concrete implementations

### 🎨 **UI Design Strategy:**
- **Vibrant color themes**: Located in `src/styles/themes.css` with engaging gradients
- **Component hierarchy**: Base Shadcn components → Feature-specific components → Page components
- **Modern aesthetics**: Tailwind classes for professional, colorful interfaces

### 🔐 **Authentication Architecture:**
- **Session-based auth**: Ready for mobile SDK integration
- **Middleware protection**: Route-level security in `middleware.js`
- **Token management**: Prepared for JWT tokens for mobile compatibility

### 📡 **API Design:**
- **RESTful conventions**: Proper HTTP methods and status codes
- **Consistent responses**: Standardized JSON structure in `lib/api/response.js`
- **Mobile-ready**: API structure designed for easy SDK integration

### 🧪 **Testing Strategy:**
- **Co-located tests**: Tests alongside feature modules
- **Integration tests**: Separate folder for end-to-end testing
- **Test utilities**: Shared testing helpers and setup

### 📈 **Scalability:**
- **Feature modules**: Easy to add new features following the same pattern
- **Shared utilities**: Common functionality centralized in `lib/`
- **Component reusability**: Base components can be extended for new features

This structure supports rapid development while maintaining clean architecture principles and scalability for future features.
