import { getCurrentUser } from '@/lib/auth/session'
import { getEquipmentById, updateEquipment, deleteEquipment } from '@/features/equipment/services/equipmentService'
import { updateEquipmentSchema } from '@/features/equipment/validation/equipmentSchemas'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view equipment')
    }

    const equipmentId = params.id
    const equipment = await getEquipmentById(equipmentId)

    return successResponse(equipment, 'Equipment retrieved successfully')

  } catch (error) {
    if (error.message === 'Equipment not found') {
      return notFoundResponse('Equipment not found')
    }
    return handleApiError(error)
  }
}

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_UPDATE)) {
      return forbiddenResponse('Insufficient permissions to update equipment')
    }

    const equipmentId = params.id
    const body = await request.json()
    
    // Validate request body
    const validatedData = updateEquipmentSchema.parse(body)
    
    // Update equipment
    const updatedEquipment = await updateEquipment(equipmentId, validatedData, user.id)
    
    return successResponse(updatedEquipment, 'Equipment updated successfully')
    
  } catch (error) {
    if (error.message === 'Equipment not found') {
      return notFoundResponse('Equipment not found')
    }
    return handleApiError(error)
  }
}

export async function DELETE(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_DELETE)) {
      return forbiddenResponse('Insufficient permissions to delete equipment')
    }

    const equipmentId = params.id
    
    // Delete equipment
    const result = await deleteEquipment(equipmentId, user.id)
    
    return successResponse(result, 'Equipment deleted successfully')
    
  } catch (error) {
    if (error.message === 'Equipment not found') {
      return notFoundResponse('Equipment not found')
    }
    return handleApiError(error)
  }
}
