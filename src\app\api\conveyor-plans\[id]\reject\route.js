import { getCurrentUser } from '@/lib/auth/session'
import { rejectConveyorPlan } from '@/features/conveyor/services/conveyorService'
import { successResponse, unauthorizedResponse, forbiddenResponse, notFoundResponse, handleApiError } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission - only managers and admins can reject
    if (!hasPermission(user.role, PERMISSIONS.USER_UPDATE) || user.role === 'USER') {
      return forbiddenResponse('Insufficient permissions to reject conveyor plan')
    }

    const planId = params.id
    
    // Reject plan
    const rejectedPlan = await rejectConveyorPlan(planId, user.id)
    
    return successResponse(rejectedPlan, 'Conveyor plan rejected successfully')
    
  } catch (error) {
    if (error.message === 'Conveyor plan not found') {
      return notFoundResponse('Conveyor plan not found')
    }
    return handleApiError(error)
  }
}
