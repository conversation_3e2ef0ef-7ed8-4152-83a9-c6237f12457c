'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Edit, ArrowLeft, Settings, Package } from 'lucide-react'

export default function EquipmentView({ equipment, onEdit, onClose }) {
  if (!equipment) return null

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatDateTime = (date) => {
    return new Date(date).toLocaleString('en-IN')
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onClose}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to List
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{equipment.name}</h1>
            <p className="text-gray-600 mt-1">Equipment Code: {equipment.code}</p>
          </div>
        </div>
        <Button onClick={onEdit} className="btn-gradient-primary">
          <Edit className="w-4 h-4 mr-2" />
          Edit Equipment
        </Button>
      </div>

      {/* Basic Information */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="w-5 h-5 mr-2" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">Equipment Name</label>
              <p className="text-lg font-semibold text-gray-900">{equipment.name}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Equipment Code</label>
              <p className="text-lg font-mono text-gray-900">{equipment.code}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Category</label>
              <p className="text-lg text-gray-900">{equipment.category?.name}</p>
            </div>
            
            {equipment.subCategory && (
              <div>
                <label className="text-sm font-medium text-gray-500">Sub Category</label>
                <p className="text-lg text-gray-900">{equipment.subCategory}</p>
              </div>
            )}
            
            <div>
              <label className="text-sm font-medium text-gray-500">Unit of Measure</label>
              <p className="text-lg text-gray-900">{equipment.uom}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Status</label>
              <div className="mt-1">
                <Badge variant={equipment.status ? 'success' : 'secondary'}>
                  {equipment.status ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          </div>

          {equipment.description && (
            <div className="mt-6">
              <label className="text-sm font-medium text-gray-500">Description</label>
              <p className="text-gray-900 mt-1">{equipment.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pricing Information */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle>Pricing Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">Base Rate</label>
              <p className="text-2xl font-bold text-green-600">{formatCurrency(equipment.baseRate)}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Effective From</label>
              <p className="text-lg text-gray-900">{formatDate(equipment.effectiveFrom)}</p>
            </div>
            
            {equipment.validUpto && (
              <div>
                <label className="text-sm font-medium text-gray-500">Valid Upto</label>
                <p className="text-lg text-gray-900">{formatDate(equipment.validUpto)}</p>
              </div>
            )}
          </div>

          {equipment.regionRates && (
            <div className="mt-6">
              <label className="text-sm font-medium text-gray-500">Region-specific Rates</label>
              <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(equipment.regionRates).map(([region, rate]) => (
                  <div key={region} className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm font-medium text-gray-600">{region}</p>
                    <p className="text-lg font-semibold text-gray-900">{formatCurrency(rate)}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {equipment.costBreakup && (
            <div className="mt-6">
              <label className="text-sm font-medium text-gray-500">Cost Breakup</label>
              <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(equipment.costBreakup).map(([component, cost]) => (
                  <div key={component} className="bg-blue-50 p-3 rounded-lg">
                    <p className="text-sm font-medium text-blue-600 capitalize">{component}</p>
                    <p className="text-lg font-semibold text-blue-900">{formatCurrency(cost)}</p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Technical Specifications */}
      {equipment.specifications && (
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="w-5 h-5 mr-2" />
              Technical Specifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(equipment.specifications).map(([key, value]) => (
                <div key={key} className="bg-gray-50 p-4 rounded-lg">
                  <label className="text-sm font-medium text-gray-600 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </label>
                  <p className="text-lg text-gray-900 mt-1">{String(value)}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Components */}
      {equipment.components && equipment.components.length > 0 && (
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Associated Components ({equipment.components.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {equipment.components.map((component) => (
                <div key={component.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{component.name}</p>
                    <p className="text-sm text-gray-600 font-mono">{component.code}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">{formatCurrency(component.baseRate)}</p>
                    <Badge variant={component.status ? 'success' : 'secondary'} className="text-xs">
                      {component.status ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Audit Information */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle>Audit Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="text-sm font-medium text-gray-500">Created At</label>
              <p className="text-gray-900">{formatDateTime(equipment.createdAt)}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Last Updated</label>
              <p className="text-gray-900">{formatDateTime(equipment.updatedAt)}</p>
            </div>
            
            {equipment.createdBy && (
              <div>
                <label className="text-sm font-medium text-gray-500">Created By</label>
                <p className="text-gray-900">{equipment.createdBy}</p>
              </div>
            )}
            
            {equipment.updatedBy && (
              <div>
                <label className="text-sm font-medium text-gray-500">Updated By</label>
                <p className="text-gray-900">{equipment.updatedBy}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
