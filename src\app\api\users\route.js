import { getCurrentUser } from '@/lib/auth/session'
import { getUsers, createUser } from '@/features/users/services/userService'
import { userFilterSchema, createUserSchema } from '@/features/users/validation/userSchemas'
import { successResponse, unauthorizedResponse, forbiddenResponse, handleApiError, paginatedResponse } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view users')
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const filters = userFilterSchema.parse({
      search: searchParams.get('search') || '',
      role: searchParams.get('role') || undefined,
      status: searchParams.get('status') || undefined,
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: searchParams.get('sortOrder') || 'desc',
    })

    // Get users
    const result = await getUsers(filters)

    return paginatedResponse(
      { users: result.users },
      result.pagination,
      'Users retrieved successfully'
    )

  } catch (error) {
    return handleApiError(error)
  }
}

export async function POST(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_CREATE)) {
      return forbiddenResponse('Insufficient permissions to create users')
    }

    const body = await request.json()
    
    // Validate request body
    const validatedData = createUserSchema.parse(body)
    
    // Create user
    const newUser = await createUser(validatedData, user.id)
    
    return successResponse(newUser, 'User created successfully', null, 201)
    
  } catch (error) {
    return handleApiError(error)
  }
}
