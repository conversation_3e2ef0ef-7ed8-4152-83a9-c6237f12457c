/**
 * User roles hierarchy
 */
export const USER_ROLES = {
  ADMIN: 'ADMIN',
  MANAGER: 'MANAGER',
  USER: 'USER',
}

/**
 * Role hierarchy levels (higher number = more permissions)
 */
const ROLE_LEVELS = {
  [USER_ROLES.USER]: 1,
  [USER_ROLES.MANAGER]: 2,
  [USER_ROLES.ADMIN]: 3,
}

/**
 * Permissions for different actions
 */
export const PERMISSIONS = {
  // User management
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  USER_INVITE: 'user:invite',
  USER_ACTIVATE: 'user:activate',
  USER_DEACTIVATE: 'user:deactivate',
  
  // Role management
  ROLE_ASSIGN: 'role:assign',
  ROLE_VIEW: 'role:view',
  
  // System
  AUDIT_VIEW: 'audit:view',
  SYSTEM_CONFIG: 'system:config',
}

/**
 * Role-based permissions mapping
 */
const ROLE_PERMISSIONS = {
  [USER_ROLES.ADMIN]: [
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_INVITE,
    PERMISSIONS.USER_ACTIVATE,
    PERMISSIONS.USER_DEACTIVATE,
    PERMISSIONS.ROLE_ASSIGN,
    PERMISSIONS.ROLE_VIEW,
    PERMISSIONS.AUDIT_VIEW,
    PERMISSIONS.SYSTEM_CONFIG,
  ],
  [USER_ROLES.MANAGER]: [
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_INVITE,
    PERMISSIONS.USER_ACTIVATE,
    PERMISSIONS.USER_DEACTIVATE,
    PERMISSIONS.ROLE_VIEW,
    PERMISSIONS.AUDIT_VIEW,
  ],
  [USER_ROLES.USER]: [
    PERMISSIONS.USER_READ, // Can read own profile
  ],
}

/**
 * Check if user has specific permission
 */
export function hasPermission(userRole, permission) {
  if (!userRole || !permission) return false
  
  const rolePermissions = ROLE_PERMISSIONS[userRole] || []
  return rolePermissions.includes(permission)
}

/**
 * Check if user has any of the specified permissions
 */
export function hasAnyPermission(userRole, permissions) {
  if (!userRole || !permissions || !Array.isArray(permissions)) return false
  
  return permissions.some(permission => hasPermission(userRole, permission))
}

/**
 * Check if user has all of the specified permissions
 */
export function hasAllPermissions(userRole, permissions) {
  if (!userRole || !permissions || !Array.isArray(permissions)) return false
  
  return permissions.every(permission => hasPermission(userRole, permission))
}

/**
 * Check if user role is higher than or equal to required role
 */
export function hasRoleLevel(userRole, requiredRole) {
  if (!userRole || !requiredRole) return false
  
  const userLevel = ROLE_LEVELS[userRole] || 0
  const requiredLevel = ROLE_LEVELS[requiredRole] || 0
  
  return userLevel >= requiredLevel
}

/**
 * Check if user can manage another user (based on role hierarchy)
 */
export function canManageUser(managerRole, targetRole) {
  if (!managerRole || !targetRole) return false
  
  const managerLevel = ROLE_LEVELS[managerRole] || 0
  const targetLevel = ROLE_LEVELS[targetRole] || 0
  
  // Can manage users with lower or equal role level
  return managerLevel >= targetLevel
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(role) {
  return ROLE_PERMISSIONS[role] || []
}

/**
 * Check if user can access resource
 */
export function canAccessResource(userRole, resource, action = 'read') {
  const permission = `${resource}:${action}`
  return hasPermission(userRole, permission)
}

/**
 * Filter data based on user permissions
 */
export function filterByPermissions(data, userRole, filterFn) {
  if (!Array.isArray(data)) return data
  
  return data.filter(item => filterFn(item, userRole))
}

/**
 * Permission middleware helper
 */
export function requirePermission(permission) {
  return (userRole) => {
    if (!hasPermission(userRole, permission)) {
      throw new Error(`Insufficient permissions. Required: ${permission}`)
    }
    return true
  }
}

/**
 * Role middleware helper
 */
export function requireRole(requiredRole) {
  return (userRole) => {
    if (!hasRoleLevel(userRole, requiredRole)) {
      throw new Error(`Insufficient role level. Required: ${requiredRole}`)
    }
    return true
  }
}
