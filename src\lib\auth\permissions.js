/**
 * User roles hierarchy
 */
export const USER_ROLES = {
  ADMIN: "ADMIN",
  MANAGER: "MANAGER",
  USER: "USER",
};

/**
 * Role hierarchy levels (higher number = more permissions)
 */
const ROLE_LEVELS = {
  [USER_ROLES.USER]: 1,
  [USER_ROLES.MANAGER]: 2,
  [USER_ROLES.ADMIN]: 3,
};

/**
 * Permissions for different actions
 */
export const PERMISSIONS = {
  // User management
  USER_CREATE: "user:create",
  USER_READ: "user:read",
  USER_UPDATE: "user:update",
  USER_DELETE: "user:delete",
  USER_INVITE: "user:invite",
  USER_ACTIVATE: "user:activate",
  USER_DEACTIVATE: "user:deactivate",

  // Role management
  ROLE_ASSIGN: "role:assign",
  ROLE_VIEW: "role:view",

  // Equipment management
  EQUIPMENT_CREATE: "equipment:create",
  EQUIPMENT_READ: "equipment:read",
  EQUIPMENT_UPDATE: "equipment:update",
  EQUIPMENT_DELETE: "equipment:delete",

  // Component management
  COMPONENT_CREATE: "component:create",
  COMPONENT_READ: "component:read",
  COMPONENT_UPDATE: "component:update",
  COMPONENT_DELETE: "component:delete",

  // Conveyor plan management
  PLAN_CREATE: "plan:create",
  PLAN_READ: "plan:read",
  PLAN_UPDATE: "plan:update",
  PLAN_DELETE: "plan:delete",
  PLAN_SUBMIT: "plan:submit",
  PLAN_APPROVE: "plan:approve",

  // Estimation management
  ESTIMATION_CREATE: "estimation:create",
  ESTIMATION_READ: "estimation:read",
  ESTIMATION_UPDATE: "estimation:update",
  ESTIMATION_DELETE: "estimation:delete",
  ESTIMATION_FINALIZE: "estimation:finalize",

  // System
  AUDIT_VIEW: "audit:view",
  SYSTEM_CONFIG: "system:config",
};

/**
 * Role-based permissions mapping
 */
const ROLE_PERMISSIONS = {
  [USER_ROLES.ADMIN]: [
    // User management
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_DELETE,
    PERMISSIONS.USER_INVITE,
    PERMISSIONS.USER_ACTIVATE,
    PERMISSIONS.USER_DEACTIVATE,
    PERMISSIONS.ROLE_ASSIGN,
    PERMISSIONS.ROLE_VIEW,
    // Equipment management
    PERMISSIONS.EQUIPMENT_CREATE,
    PERMISSIONS.EQUIPMENT_READ,
    PERMISSIONS.EQUIPMENT_UPDATE,
    PERMISSIONS.EQUIPMENT_DELETE,
    // Component management
    PERMISSIONS.COMPONENT_CREATE,
    PERMISSIONS.COMPONENT_READ,
    PERMISSIONS.COMPONENT_UPDATE,
    PERMISSIONS.COMPONENT_DELETE,
    // Plan management
    PERMISSIONS.PLAN_CREATE,
    PERMISSIONS.PLAN_READ,
    PERMISSIONS.PLAN_UPDATE,
    PERMISSIONS.PLAN_DELETE,
    PERMISSIONS.PLAN_SUBMIT,
    PERMISSIONS.PLAN_APPROVE,
    // Estimation management
    PERMISSIONS.ESTIMATION_CREATE,
    PERMISSIONS.ESTIMATION_READ,
    PERMISSIONS.ESTIMATION_UPDATE,
    PERMISSIONS.ESTIMATION_DELETE,
    PERMISSIONS.ESTIMATION_FINALIZE,
    // System
    PERMISSIONS.AUDIT_VIEW,
    PERMISSIONS.SYSTEM_CONFIG,
  ],
  [USER_ROLES.MANAGER]: [
    // User management
    PERMISSIONS.USER_CREATE,
    PERMISSIONS.USER_READ,
    PERMISSIONS.USER_UPDATE,
    PERMISSIONS.USER_INVITE,
    PERMISSIONS.USER_ACTIVATE,
    PERMISSIONS.USER_DEACTIVATE,
    PERMISSIONS.ROLE_VIEW,
    // Equipment management
    PERMISSIONS.EQUIPMENT_CREATE,
    PERMISSIONS.EQUIPMENT_READ,
    PERMISSIONS.EQUIPMENT_UPDATE,
    PERMISSIONS.EQUIPMENT_DELETE,
    // Component management
    PERMISSIONS.COMPONENT_CREATE,
    PERMISSIONS.COMPONENT_READ,
    PERMISSIONS.COMPONENT_UPDATE,
    PERMISSIONS.COMPONENT_DELETE,
    // Plan management
    PERMISSIONS.PLAN_CREATE,
    PERMISSIONS.PLAN_READ,
    PERMISSIONS.PLAN_UPDATE,
    PERMISSIONS.PLAN_DELETE,
    PERMISSIONS.PLAN_SUBMIT,
    PERMISSIONS.PLAN_APPROVE,
    // Estimation management
    PERMISSIONS.ESTIMATION_CREATE,
    PERMISSIONS.ESTIMATION_READ,
    PERMISSIONS.ESTIMATION_UPDATE,
    PERMISSIONS.ESTIMATION_DELETE,
    PERMISSIONS.ESTIMATION_FINALIZE,
    // System
    PERMISSIONS.AUDIT_VIEW,
  ],
  [USER_ROLES.USER]: [
    // User management
    PERMISSIONS.USER_READ, // Can read own profile
    // Equipment management
    PERMISSIONS.EQUIPMENT_READ,
    // Component management
    PERMISSIONS.COMPONENT_READ,
    // Plan management
    PERMISSIONS.PLAN_CREATE,
    PERMISSIONS.PLAN_READ,
    PERMISSIONS.PLAN_UPDATE,
    PERMISSIONS.PLAN_SUBMIT,
    // Estimation management
    PERMISSIONS.ESTIMATION_CREATE,
    PERMISSIONS.ESTIMATION_READ,
    PERMISSIONS.ESTIMATION_UPDATE,
  ],
};

/**
 * Check if user has specific permission
 */
export function hasPermission(userRole, permission) {
  if (!userRole || !permission) return false;

  const rolePermissions = ROLE_PERMISSIONS[userRole] || [];
  return rolePermissions.includes(permission);
}

/**
 * Check if user has any of the specified permissions
 */
export function hasAnyPermission(userRole, permissions) {
  if (!userRole || !permissions || !Array.isArray(permissions)) return false;

  return permissions.some((permission) => hasPermission(userRole, permission));
}

/**
 * Check if user has all of the specified permissions
 */
export function hasAllPermissions(userRole, permissions) {
  if (!userRole || !permissions || !Array.isArray(permissions)) return false;

  return permissions.every((permission) => hasPermission(userRole, permission));
}

/**
 * Check if user role is higher than or equal to required role
 */
export function hasRoleLevel(userRole, requiredRole) {
  if (!userRole || !requiredRole) return false;

  const userLevel = ROLE_LEVELS[userRole] || 0;
  const requiredLevel = ROLE_LEVELS[requiredRole] || 0;

  return userLevel >= requiredLevel;
}

/**
 * Check if user can manage another user (based on role hierarchy)
 */
export function canManageUser(managerRole, targetRole) {
  if (!managerRole || !targetRole) return false;

  const managerLevel = ROLE_LEVELS[managerRole] || 0;
  const targetLevel = ROLE_LEVELS[targetRole] || 0;

  // Can manage users with lower or equal role level
  return managerLevel >= targetLevel;
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(role) {
  return ROLE_PERMISSIONS[role] || [];
}

/**
 * Check if user can access resource
 */
export function canAccessResource(userRole, resource, action = "read") {
  const permission = `${resource}:${action}`;
  return hasPermission(userRole, permission);
}

/**
 * Filter data based on user permissions
 */
export function filterByPermissions(data, userRole, filterFn) {
  if (!Array.isArray(data)) return data;

  return data.filter((item) => filterFn(item, userRole));
}

/**
 * Permission middleware helper
 */
export function requirePermission(permission) {
  return (userRole) => {
    if (!hasPermission(userRole, permission)) {
      throw new Error(`Insufficient permissions. Required: ${permission}`);
    }
    return true;
  };
}

/**
 * Role middleware helper
 */
export function requireRole(requiredRole) {
  return (userRole) => {
    if (!hasRoleLevel(userRole, requiredRole)) {
      throw new Error(`Insufficient role level. Required: ${requiredRole}`);
    }
    return true;
  };
}
