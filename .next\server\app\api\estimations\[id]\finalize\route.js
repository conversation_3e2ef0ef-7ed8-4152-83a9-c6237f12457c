"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/estimations/[id]/finalize/route";
exports.ids = ["app/api/estimations/[id]/finalize/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute&page=%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute&page=%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_MobioProjects_coveyor_poc_src_app_api_estimations_id_finalize_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/estimations/[id]/finalize/route.js */ \"(rsc)/./src/app/api/estimations/[id]/finalize/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/estimations/[id]/finalize/route\",\n        pathname: \"/api/estimations/[id]/finalize\",\n        filename: \"route\",\n        bundlePath: \"app/api/estimations/[id]/finalize/route\"\n    },\n    resolvedPagePath: \"D:\\\\MobioProjects\\\\coveyor-poc\\\\src\\\\app\\\\api\\\\estimations\\\\[id]\\\\finalize\\\\route.js\",\n    nextConfigOutput,\n    userland: D_MobioProjects_coveyor_poc_src_app_api_estimations_id_finalize_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/estimations/[id]/finalize/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute&page=%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/estimations/[id]/finalize/route.js":
/*!********************************************************!*\
  !*** ./src/app/api/estimations/[id]/finalize/route.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_session__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth/session */ \"(rsc)/./src/lib/auth/session.js\");\n/* harmony import */ var _features_estimation_services_estimationService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/estimation/services/estimationService */ \"(rsc)/./src/features/estimation/services/estimationService.js\");\n/* harmony import */ var _lib_api_response__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/response */ \"(rsc)/./src/lib/api/response.js\");\n/* harmony import */ var _lib_auth_permissions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth/permissions */ \"(rsc)/./src/lib/auth/permissions.js\");\n\n\n\n\nasync function PUT(request, { params }) {\n    try {\n        const user = await (0,_lib_auth_session__WEBPACK_IMPORTED_MODULE_0__.getCurrentUser)();\n        if (!user) {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.unauthorizedResponse)(\"Authentication required\");\n        }\n        // Check permission\n        if (!(0,_lib_auth_permissions__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(user.role, _lib_auth_permissions__WEBPACK_IMPORTED_MODULE_3__.PERMISSIONS.USER_UPDATE)) {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.forbiddenResponse)(\"Insufficient permissions to finalize estimation sheet\");\n        }\n        const estimationId = params.id;\n        // Finalize estimation sheet\n        const finalizedEstimation = await (0,_features_estimation_services_estimationService__WEBPACK_IMPORTED_MODULE_1__.finalizeEstimationSheet)(estimationId, user.id);\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.successResponse)(finalizedEstimation, \"Estimation sheet finalized successfully\");\n    } catch (error) {\n        if (error.message === \"Estimation sheet not found\") {\n            return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.notFoundResponse)(\"Estimation sheet not found\");\n        }\n        return (0,_lib_api_response__WEBPACK_IMPORTED_MODULE_2__.handleApiError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/estimations/[id]/finalize/route.js\n");

/***/ }),

/***/ "(rsc)/./src/features/estimation/services/estimationService.js":
/*!***************************************************************!*\
  !*** ./src/features/estimation/services/estimationService.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEstimationSheet: () => (/* binding */ createEstimationSheet),\n/* harmony export */   deleteEstimationSheet: () => (/* binding */ deleteEstimationSheet),\n/* harmony export */   finalizeEstimationSheet: () => (/* binding */ finalizeEstimationSheet),\n/* harmony export */   generateAutomaticEstimation: () => (/* binding */ generateAutomaticEstimation),\n/* harmony export */   getEstimationSheetById: () => (/* binding */ getEstimationSheetById),\n/* harmony export */   getEstimationSheets: () => (/* binding */ getEstimationSheets),\n/* harmony export */   getEstimationStats: () => (/* binding */ getEstimationStats),\n/* harmony export */   updateEstimationSheet: () => (/* binding */ updateEstimationSheet)\n/* harmony export */ });\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n\n/**\n * Get paginated estimation sheets list with filters\n */ async function getEstimationSheets(filters = {}) {\n    const { search = \"\", planId, status, page = 1, limit = 10, sortBy = \"generatedAt\", sortOrder = \"desc\" } = filters;\n    const skip = (page - 1) * limit;\n    // Build where clause\n    const where = {\n        AND: [\n            // Search filter\n            search ? {\n                OR: [\n                    {\n                        notes: {\n                            contains: search,\n                            mode: \"insensitive\"\n                        }\n                    },\n                    {\n                        plan: {\n                            planName: {\n                                contains: search,\n                                mode: \"insensitive\"\n                            }\n                        }\n                    }\n                ]\n            } : {},\n            // Plan filter\n            planId ? {\n                planId: parseInt(planId)\n            } : {},\n            // Status filter\n            status ? {\n                status\n            } : {}\n        ]\n    };\n    // Get estimation sheets with pagination\n    const [estimations, total] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findMany({\n            where,\n            select: {\n                id: true,\n                estimationVersion: true,\n                equipmentCost: true,\n                componentCost: true,\n                laborCost: true,\n                overheadCost: true,\n                discountPercent: true,\n                markupPercent: true,\n                totalCost: true,\n                status: true,\n                generatedAt: true,\n                plan: {\n                    select: {\n                        id: true,\n                        planName: true,\n                        conveyorType: true,\n                        totalLengthM: true,\n                        loadType: true\n                    }\n                }\n            },\n            orderBy: {\n                [sortBy]: sortOrder\n            },\n            skip,\n            take: limit\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where\n        })\n    ]);\n    return {\n        estimations,\n        pagination: {\n            page,\n            limit,\n            total,\n            totalPages: Math.ceil(total / limit)\n        }\n    };\n}\n/**\n * Get estimation sheet by ID\n */ async function getEstimationSheetById(id) {\n    const estimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findUnique({\n        where: {\n            id: parseInt(id)\n        },\n        include: {\n            plan: {\n                include: {\n                    estimationSheets: {\n                        select: {\n                            id: true,\n                            estimationVersion: true,\n                            totalCost: true,\n                            status: true,\n                            generatedAt: true\n                        },\n                        orderBy: {\n                            generatedAt: \"desc\"\n                        }\n                    }\n                }\n            }\n        }\n    });\n    if (!estimation) {\n        throw new Error(\"Estimation sheet not found\");\n    }\n    return estimation;\n}\n/**\n * Create new estimation sheet\n */ async function createEstimationSheet(estimationData, generatedBy) {\n    const { planId, estimationVersion, equipmentCost, componentCost, laborCost, overheadCost, discountPercent, markupPercent, totalCost, notes, status } = estimationData;\n    // Verify plan exists\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(planId)\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    // Get next version number if not provided\n    let version = estimationVersion;\n    if (!version) {\n        const lastEstimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findFirst({\n            where: {\n                planId: parseInt(planId)\n            },\n            orderBy: {\n                estimationVersion: \"desc\"\n            }\n        });\n        version = lastEstimation ? lastEstimation.estimationVersion + 1 : 1;\n    }\n    // Create estimation sheet\n    const estimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.create({\n        data: {\n            planId: parseInt(planId),\n            estimationVersion: version,\n            equipmentCost,\n            componentCost,\n            laborCost,\n            overheadCost,\n            discountPercent,\n            markupPercent,\n            totalCost,\n            notes,\n            status,\n            generatedBy\n        },\n        include: {\n            plan: {\n                select: {\n                    id: true,\n                    planName: true,\n                    conveyorType: true\n                }\n            }\n        }\n    });\n    return estimation;\n}\n/**\n * Update estimation sheet\n */ async function updateEstimationSheet(id, estimationData, updatedBy) {\n    const { equipmentCost, componentCost, laborCost, overheadCost, discountPercent, markupPercent, totalCost, notes, status } = estimationData;\n    // Check if estimation exists\n    const existingEstimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!existingEstimation) {\n        throw new Error(\"Estimation sheet not found\");\n    }\n    // Update estimation sheet\n    const estimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            ...equipmentCost !== undefined && {\n                equipmentCost\n            },\n            ...componentCost !== undefined && {\n                componentCost\n            },\n            ...laborCost !== undefined && {\n                laborCost\n            },\n            ...overheadCost !== undefined && {\n                overheadCost\n            },\n            ...discountPercent !== undefined && {\n                discountPercent\n            },\n            ...markupPercent !== undefined && {\n                markupPercent\n            },\n            ...totalCost !== undefined && {\n                totalCost\n            },\n            ...notes !== undefined && {\n                notes\n            },\n            ...status && {\n                status\n            }\n        },\n        include: {\n            plan: {\n                select: {\n                    id: true,\n                    planName: true,\n                    conveyorType: true\n                }\n            }\n        }\n    });\n    return estimation;\n}\n/**\n * Delete estimation sheet\n */ async function deleteEstimationSheet(id, deletedBy) {\n    // Check if estimation exists\n    const estimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!estimation) {\n        throw new Error(\"Estimation sheet not found\");\n    }\n    // Delete estimation sheet\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.delete({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    return {\n        message: \"Estimation sheet deleted successfully\"\n    };\n}\n/**\n * Generate automatic estimation based on plan\n */ async function generateAutomaticEstimation(planId, generatedBy) {\n    // Get plan details\n    const plan = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].conveyorPlan.findUnique({\n        where: {\n            id: parseInt(planId)\n        }\n    });\n    if (!plan) {\n        throw new Error(\"Conveyor plan not found\");\n    }\n    // Get equipment based on conveyor type\n    const equipment = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].equipmentMaster.findMany({\n        where: {\n            status: true,\n            OR: [\n                {\n                    name: {\n                        contains: plan.conveyorType.split(\" \")[0],\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    category: {\n                        name: {\n                            contains: \"Conveyor\",\n                            mode: \"insensitive\"\n                        }\n                    }\n                }\n            ]\n        },\n        take: 5 // Limit to top 5 relevant equipment\n    });\n    // Get components for the equipment\n    const components = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].componentMaster.findMany({\n        where: {\n            status: true,\n            equipment: {\n                id: {\n                    in: equipment.map((e)=>e.id)\n                }\n            }\n        },\n        take: 10 // Limit to top 10 relevant components\n    });\n    // Calculate costs\n    const equipmentCost = equipment.reduce((total, item)=>{\n        // Simple calculation: base rate * length factor\n        const lengthFactor = Math.max(1, plan.totalLengthM / 100);\n        return total + parseFloat(item.baseRate) * lengthFactor;\n    }, 0);\n    const componentCost = components.reduce((total, item)=>{\n        // Simple calculation: base rate * length factor\n        const lengthFactor = Math.max(1, plan.totalLengthM / 50);\n        return total + parseFloat(item.baseRate) * lengthFactor;\n    }, 0);\n    const materialCost = equipmentCost + componentCost;\n    const laborCost = materialCost * 0.15 // 15% of material cost\n    ;\n    const overheadCost = (materialCost + laborCost) * 0.10 // 10% of material + labor\n    ;\n    const subtotal = materialCost + laborCost + overheadCost;\n    const markupPercent = 20 // 20% markup\n    ;\n    const totalCost = subtotal * (1 + markupPercent / 100);\n    // Get next version number\n    const lastEstimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findFirst({\n        where: {\n            planId: parseInt(planId)\n        },\n        orderBy: {\n            estimationVersion: \"desc\"\n        }\n    });\n    const version = lastEstimation ? lastEstimation.estimationVersion + 1 : 1;\n    // Create estimation\n    const estimation = await createEstimationSheet({\n        planId: parseInt(planId),\n        estimationVersion: version,\n        equipmentCost,\n        componentCost,\n        laborCost,\n        overheadCost,\n        discountPercent: 0,\n        markupPercent,\n        totalCost,\n        notes: \"Auto-generated estimation based on plan specifications\",\n        status: \"Draft\"\n    }, generatedBy);\n    return estimation;\n}\n/**\n * Finalize estimation sheet\n */ async function finalizeEstimationSheet(id, finalizedBy) {\n    const estimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.findUnique({\n        where: {\n            id: parseInt(id)\n        }\n    });\n    if (!estimation) {\n        throw new Error(\"Estimation sheet not found\");\n    }\n    if (estimation.status !== \"Draft\") {\n        throw new Error(\"Only draft estimations can be finalized\");\n    }\n    const finalizedEstimation = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.update({\n        where: {\n            id: parseInt(id)\n        },\n        data: {\n            status: \"Final\"\n        }\n    });\n    return finalizedEstimation;\n}\n/**\n * Get estimation statistics\n */ async function getEstimationStats() {\n    const [totalEstimations, draftEstimations, finalEstimations, sentEstimations, approvedEstimations, recentEstimations] = await Promise.all([\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count(),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where: {\n                status: \"Draft\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where: {\n                status: \"Final\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where: {\n                status: \"Sent\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where: {\n                status: \"Approved\"\n            }\n        }),\n        _lib_db_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].estimationSheet.count({\n            where: {\n                generatedAt: {\n                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                }\n            }\n        })\n    ]);\n    return {\n        totalEstimations,\n        draftEstimations,\n        finalEstimations,\n        sentEstimations,\n        approvedEstimations,\n        recentEstimations\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/estimation/services/estimationService.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/api/response.js":
/*!*********************************!*\
  !*** ./src/lib/api/response.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiResponse: () => (/* binding */ ApiResponse),\n/* harmony export */   createdResponse: () => (/* binding */ createdResponse),\n/* harmony export */   errorResponse: () => (/* binding */ errorResponse),\n/* harmony export */   forbiddenResponse: () => (/* binding */ forbiddenResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   noContentResponse: () => (/* binding */ noContentResponse),\n/* harmony export */   notFoundResponse: () => (/* binding */ notFoundResponse),\n/* harmony export */   paginatedResponse: () => (/* binding */ paginatedResponse),\n/* harmony export */   serverErrorResponse: () => (/* binding */ serverErrorResponse),\n/* harmony export */   successResponse: () => (/* binding */ successResponse),\n/* harmony export */   unauthorizedResponse: () => (/* binding */ unauthorizedResponse),\n/* harmony export */   validationErrorResponse: () => (/* binding */ validationErrorResponse),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\n/**\n * Standard API response structure\n */ class ApiResponse {\n    constructor(success = true, data = null, message = null, errors = null, meta = null){\n        this.success = success;\n        this.data = data;\n        this.message = message;\n        this.errors = errors;\n        this.meta = meta;\n        this.timestamp = new Date().toISOString();\n    }\n}\n/**\n * Success response helper\n */ function successResponse(data = null, message = null, meta = null, status = 200) {\n    const response = new ApiResponse(true, data, message, null, meta);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Error response helper\n */ function errorResponse(message, errors = null, status = 400) {\n    const response = new ApiResponse(false, null, message, errors);\n    return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(response, {\n        status\n    });\n}\n/**\n * Validation error response\n */ function validationErrorResponse(errors, message = \"Validation failed\") {\n    return errorResponse(message, errors, 422);\n}\n/**\n * Unauthorized response\n */ function unauthorizedResponse(message = \"Unauthorized\") {\n    return errorResponse(message, null, 401);\n}\n/**\n * Forbidden response\n */ function forbiddenResponse(message = \"Forbidden\") {\n    return errorResponse(message, null, 403);\n}\n/**\n * Not found response\n */ function notFoundResponse(message = \"Resource not found\") {\n    return errorResponse(message, null, 404);\n}\n/**\n * Internal server error response\n */ function serverErrorResponse(message = \"Internal server error\") {\n    return errorResponse(message, null, 500);\n}\n/**\n * Created response\n */ function createdResponse(data, message = \"Resource created successfully\") {\n    return successResponse(data, message, null, 201);\n}\n/**\n * No content response\n */ function noContentResponse() {\n    return new next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"](null, {\n        status: 204\n    });\n}\n/**\n * Paginated response helper\n */ function paginatedResponse(data, pagination, message = null) {\n    const meta = {\n        pagination: {\n            page: pagination.page,\n            limit: pagination.limit,\n            total: pagination.total,\n            totalPages: Math.ceil(pagination.total / pagination.limit),\n            hasNext: pagination.page < Math.ceil(pagination.total / pagination.limit),\n            hasPrev: pagination.page > 1\n        }\n    };\n    return successResponse(data, message, meta);\n}\n/**\n * Handle API errors consistently\n */ function handleApiError(error) {\n    console.error(\"API Error:\", error);\n    // Prisma errors\n    if (error.code === \"P2002\") {\n        return errorResponse(\"Resource already exists\", null, 409);\n    }\n    if (error.code === \"P2025\") {\n        return notFoundResponse(\"Resource not found\");\n    }\n    // Validation errors (Zod)\n    if (error.name === \"ZodError\") {\n        const validationErrors = error.errors.map((err)=>({\n                field: err.path.join(\".\"),\n                message: err.message,\n                code: err.code\n            }));\n        return validationErrorResponse(validationErrors);\n    }\n    // JWT errors\n    if (error.name === \"JsonWebTokenError\") {\n        return unauthorizedResponse(\"Invalid token\");\n    }\n    if (error.name === \"TokenExpiredError\") {\n        return unauthorizedResponse(\"Token expired\");\n    }\n    // Custom application errors\n    if (error.message === \"Invalid credentials\") {\n        return unauthorizedResponse(\"Invalid email or password\");\n    }\n    if (error.message === \"User not found\") {\n        return notFoundResponse(\"User not found\");\n    }\n    if (error.message === \"Insufficient permissions\") {\n        return forbiddenResponse(\"Insufficient permissions\");\n    }\n    // Default server error\n    return serverErrorResponse();\n}\n/**\n * Async error handler wrapper\n */ function withErrorHandling(handler) {\n    return async (request, context)=>{\n        try {\n            return await handler(request, context);\n        } catch (error) {\n            return handleApiError(error);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api/response.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/permissions.js":
/*!*************************************!*\
  !*** ./src/lib/auth/permissions.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   USER_ROLES: () => (/* binding */ USER_ROLES),\n/* harmony export */   canAccessResource: () => (/* binding */ canAccessResource),\n/* harmony export */   canManageUser: () => (/* binding */ canManageUser),\n/* harmony export */   filterByPermissions: () => (/* binding */ filterByPermissions),\n/* harmony export */   getRolePermissions: () => (/* binding */ getRolePermissions),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRoleLevel: () => (/* binding */ hasRoleLevel),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole)\n/* harmony export */ });\n/**\n * User roles hierarchy\n */ const USER_ROLES = {\n    ADMIN: \"ADMIN\",\n    MANAGER: \"MANAGER\",\n    USER: \"USER\"\n};\n/**\n * Role hierarchy levels (higher number = more permissions)\n */ const ROLE_LEVELS = {\n    [USER_ROLES.USER]: 1,\n    [USER_ROLES.MANAGER]: 2,\n    [USER_ROLES.ADMIN]: 3\n};\n/**\n * Permissions for different actions\n */ const PERMISSIONS = {\n    // User management\n    USER_CREATE: \"user:create\",\n    USER_READ: \"user:read\",\n    USER_UPDATE: \"user:update\",\n    USER_DELETE: \"user:delete\",\n    USER_INVITE: \"user:invite\",\n    USER_ACTIVATE: \"user:activate\",\n    USER_DEACTIVATE: \"user:deactivate\",\n    // Role management\n    ROLE_ASSIGN: \"role:assign\",\n    ROLE_VIEW: \"role:view\",\n    // System\n    AUDIT_VIEW: \"audit:view\",\n    SYSTEM_CONFIG: \"system:config\"\n};\n/**\n * Role-based permissions mapping\n */ const ROLE_PERMISSIONS = {\n    [USER_ROLES.ADMIN]: [\n        PERMISSIONS.USER_CREATE,\n        PERMISSIONS.USER_READ,\n        PERMISSIONS.USER_UPDATE,\n        PERMISSIONS.USER_DELETE,\n        PERMISSIONS.USER_INVITE,\n        PERMISSIONS.USER_ACTIVATE,\n        PERMISSIONS.USER_DEACTIVATE,\n        PERMISSIONS.ROLE_ASSIGN,\n        PERMISSIONS.ROLE_VIEW,\n        PERMISSIONS.AUDIT_VIEW,\n        PERMISSIONS.SYSTEM_CONFIG\n    ],\n    [USER_ROLES.MANAGER]: [\n        PERMISSIONS.USER_CREATE,\n        PERMISSIONS.USER_READ,\n        PERMISSIONS.USER_UPDATE,\n        PERMISSIONS.USER_INVITE,\n        PERMISSIONS.USER_ACTIVATE,\n        PERMISSIONS.USER_DEACTIVATE,\n        PERMISSIONS.ROLE_VIEW,\n        PERMISSIONS.AUDIT_VIEW\n    ],\n    [USER_ROLES.USER]: [\n        PERMISSIONS.USER_READ\n    ]\n};\n/**\n * Check if user has specific permission\n */ function hasPermission(userRole, permission) {\n    if (!userRole || !permission) return false;\n    const rolePermissions = ROLE_PERMISSIONS[userRole] || [];\n    return rolePermissions.includes(permission);\n}\n/**\n * Check if user has any of the specified permissions\n */ function hasAnyPermission(userRole, permissions) {\n    if (!userRole || !permissions || !Array.isArray(permissions)) return false;\n    return permissions.some((permission)=>hasPermission(userRole, permission));\n}\n/**\n * Check if user has all of the specified permissions\n */ function hasAllPermissions(userRole, permissions) {\n    if (!userRole || !permissions || !Array.isArray(permissions)) return false;\n    return permissions.every((permission)=>hasPermission(userRole, permission));\n}\n/**\n * Check if user role is higher than or equal to required role\n */ function hasRoleLevel(userRole, requiredRole) {\n    if (!userRole || !requiredRole) return false;\n    const userLevel = ROLE_LEVELS[userRole] || 0;\n    const requiredLevel = ROLE_LEVELS[requiredRole] || 0;\n    return userLevel >= requiredLevel;\n}\n/**\n * Check if user can manage another user (based on role hierarchy)\n */ function canManageUser(managerRole, targetRole) {\n    if (!managerRole || !targetRole) return false;\n    const managerLevel = ROLE_LEVELS[managerRole] || 0;\n    const targetLevel = ROLE_LEVELS[targetRole] || 0;\n    // Can manage users with lower or equal role level\n    return managerLevel >= targetLevel;\n}\n/**\n * Get all permissions for a role\n */ function getRolePermissions(role) {\n    return ROLE_PERMISSIONS[role] || [];\n}\n/**\n * Check if user can access resource\n */ function canAccessResource(userRole, resource, action = \"read\") {\n    const permission = `${resource}:${action}`;\n    return hasPermission(userRole, permission);\n}\n/**\n * Filter data based on user permissions\n */ function filterByPermissions(data, userRole, filterFn) {\n    if (!Array.isArray(data)) return data;\n    return data.filter((item)=>filterFn(item, userRole));\n}\n/**\n * Permission middleware helper\n */ function requirePermission(permission) {\n    return (userRole)=>{\n        if (!hasPermission(userRole, permission)) {\n            throw new Error(`Insufficient permissions. Required: ${permission}`);\n        }\n        return true;\n    };\n}\n/**\n * Role middleware helper\n */ function requireRole(requiredRole) {\n    return (userRole)=>{\n        if (!hasRoleLevel(userRole, requiredRole)) {\n            throw new Error(`Insufficient role level. Required: ${requiredRole}`);\n        }\n        return true;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/permissions.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth/session.js":
/*!*********************************!*\
  !*** ./src/lib/auth/session.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanupExpiredSessions: () => (/* binding */ cleanupExpiredSessions),\n/* harmony export */   clearAuthCookie: () => (/* binding */ clearAuthCookie),\n/* harmony export */   createSession: () => (/* binding */ createSession),\n/* harmony export */   deleteAllUserSessions: () => (/* binding */ deleteAllUserSessions),\n/* harmony export */   deleteSession: () => (/* binding */ deleteSession),\n/* harmony export */   generateToken: () => (/* binding */ generateToken),\n/* harmony export */   getAuthToken: () => (/* binding */ getAuthToken),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   setAuthCookie: () => (/* binding */ setAuthCookie),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/prisma */ \"(rsc)/./src/lib/db/prisma.js\");\n/* harmony import */ var _lib_config_env__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/config/env */ \"(rsc)/./src/lib/config/env.js\");\n\n\n\n\nconst JWT_SECRET = _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].JWT_SECRET;\nconst TOKEN_EXPIRY = \"7d\" // 7 days\n;\nconst COOKIE_NAME = \"auth-token\";\n/**\n * Generate JWT token for user\n */ function generateToken(payload) {\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: TOKEN_EXPIRY,\n        issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n        audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n    });\n}\n/**\n * Verify JWT token\n */ function verifyToken(token) {\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET, {\n            issuer: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_NAME,\n            audience: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].APP_URL\n        });\n    } catch (error) {\n        throw new Error(\"Invalid token\");\n    }\n}\n/**\n * Create session for user\n */ async function createSession(userId, userAgent = null, ipAddress = null) {\n    const expiresAt = new Date();\n    expiresAt.setDate(expiresAt.getDate() + 7) // 7 days from now\n    ;\n    // Generate JWT token\n    const tokenPayload = {\n        userId,\n        sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    };\n    const token = generateToken(tokenPayload);\n    // Store session in database\n    const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.create({\n        data: {\n            userId,\n            token,\n            expiresAt,\n            userAgent,\n            ipAddress\n        }\n    });\n    return {\n        session,\n        token\n    };\n}\n/**\n * Get session from token\n */ async function getSession(token) {\n    if (!token) return null;\n    try {\n        // Verify JWT token\n        const payload = verifyToken(token);\n        // Get session from database\n        const session = await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.findUnique({\n            where: {\n                token\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        email: true,\n                        firstName: true,\n                        lastName: true,\n                        role: true,\n                        status: true,\n                        avatar: true\n                    }\n                }\n            }\n        });\n        if (!session || session.expiresAt < new Date()) {\n            // Session expired or not found\n            if (session) {\n                await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n                    where: {\n                        id: session.id\n                    }\n                });\n            }\n            return null;\n        }\n        return session;\n    } catch (error) {\n        return null;\n    }\n}\n/**\n * Delete session\n */ async function deleteSession(token) {\n    if (!token) return;\n    try {\n        await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.delete({\n            where: {\n                token\n            }\n        });\n    } catch (error) {\n    // Session might not exist, ignore error\n    }\n}\n/**\n * Delete all sessions for user\n */ async function deleteAllUserSessions(userId) {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            userId\n        }\n    });\n}\n/**\n * Clean up expired sessions\n */ async function cleanupExpiredSessions() {\n    await _lib_db_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].session.deleteMany({\n        where: {\n            expiresAt: {\n                lt: new Date()\n            }\n        }\n    });\n}\n/**\n * Set auth cookie\n */ function setAuthCookie(token) {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.set(COOKIE_NAME, token, {\n        httpOnly: true,\n        secure: _lib_config_env__WEBPACK_IMPORTED_MODULE_3__[\"default\"].NODE_ENV === \"production\",\n        sameSite: \"lax\",\n        maxAge: 60 * 60 * 24 * 7,\n        path: \"/\"\n    });\n}\n/**\n * Clear auth cookie\n */ function clearAuthCookie() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    cookieStore.delete(COOKIE_NAME);\n}\n/**\n * Get auth token from cookies\n */ function getAuthToken() {\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return cookieStore.get(COOKIE_NAME)?.value;\n}\n/**\n * Get current user from session\n */ async function getCurrentUser() {\n    const token = getAuthToken();\n    if (!token) return null;\n    const session = await getSession(token);\n    return session?.user || null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth/session.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/config/env.js":
/*!*******************************!*\
  !*** ./src/lib/config/env.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   env: () => (/* binding */ env)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\nconst envSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    NODE_ENV: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        \"development\",\n        \"production\",\n        \"test\"\n    ]).default(\"development\"),\n    DATABASE_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"Database URL is required\"),\n    NEXTAUTH_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"NextAuth secret is required\"),\n    NEXTAUTH_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    JWT_SECRET: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, \"JWT secret is required\"),\n    APP_URL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().default(\"http://localhost:3000\"),\n    APP_NAME: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().default(\"Coveyor POC\"),\n    // Email configuration (optional)\n    SMTP_HOST: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PORT: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_USER: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    SMTP_PASS: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    FROM_EMAIL: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email().optional()\n});\nfunction validateEnv() {\n    try {\n        return envSchema.parse(process.env);\n    } catch (error) {\n        console.error(\"❌ Invalid environment variables:\");\n        console.error(error.errors);\n        throw new Error(\"Environment validation failed\");\n    }\n}\nconst env = validateEnv();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (env);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/config/env.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/prisma.js":
/*!******************************!*\
  !*** ./src/lib/db/prisma.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3ByaXNtYS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTZDO0FBRTdDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUU7QUFFbEUsSUFBSUksSUFBeUIsRUFBYztJQUN6Q0gsZ0JBQWdCRSxNQUFNLEdBQUdBO0FBQzNCO0FBRUEsaUVBQWVBLE1BQU1BLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3ZleW9yLXBvYy8uL3NyYy9saWIvZGIvcHJpc21hLmpzP2QyZTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXNcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9IGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHwgbmV3IFByaXNtYUNsaWVudCgpXG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWFcbn1cblxuZXhwb3J0IGRlZmF1bHQgcHJpc21hXG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsVGhpcyIsInByaXNtYSIsInByb2Nlc3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/prisma.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute&page=%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Festimations%2F%5Bid%5D%2Ffinalize%2Froute.js&appDir=D%3A%5CMobioProjects%5Ccoveyor-poc%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CMobioProjects%5Ccoveyor-poc&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();