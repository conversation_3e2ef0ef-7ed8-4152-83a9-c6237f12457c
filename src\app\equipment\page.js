'use client'

import { useState } from 'react'
import { useAuth } from '@/features/auth/hooks/useAuth'
import EquipmentList from '@/features/equipment/components/EquipmentList'
import EquipmentForm from '@/features/equipment/components/EquipmentForm'
import EquipmentView from '@/features/equipment/components/EquipmentView'

export default function EquipmentPage() {
  const [currentView, setCurrentView] = useState('list') // 'list', 'create', 'edit', 'view'
  const [selectedEquipment, setSelectedEquipment] = useState(null)
  const { user } = useAuth()

  const handleCreateNew = () => {
    setSelectedEquipment(null)
    setCurrentView('create')
  }

  const handleEdit = (equipment) => {
    setSelectedEquipment(equipment)
    setCurrentView('edit')
  }

  const handleView = (equipment) => {
    setSelectedEquipment(equipment)
    setCurrentView('view')
  }

  const handleSave = (equipment) => {
    setCurrentView('list')
    setSelectedEquipment(null)
  }

  const handleCancel = () => {
    setCurrentView('list')
    setSelectedEquipment(null)
  }

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {currentView === 'list' && (
        <EquipmentList
          onCreateNew={handleCreateNew}
          onEdit={handleEdit}
          onView={handleView}
        />
      )}

      {(currentView === 'create' || currentView === 'edit') && (
        <EquipmentForm
          equipment={selectedEquipment}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      )}

      {currentView === 'view' && (
        <EquipmentView
          equipment={selectedEquipment}
          onEdit={() => handleEdit(selectedEquipment)}
          onClose={handleCancel}
        />
      )}
    </div>
  )
}
