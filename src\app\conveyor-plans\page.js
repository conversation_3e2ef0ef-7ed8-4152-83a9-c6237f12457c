"use client";

import { useState } from "react";
import { useAuth } from "@/features/auth/hooks/useAuth";
import ConveyorPlanList from "@/features/conveyor/components/ConveyorPlanList";
import ConveyorPlanForm from "@/features/conveyor/components/ConveyorPlanForm";
import ConveyorPlanView from "@/features/conveyor/components/ConveyorPlanView";

export default function ConveyorPlansPage() {
  const [currentView, setCurrentView] = useState("list"); // 'list', 'create', 'edit', 'view'
  const [selectedPlan, setSelectedPlan] = useState(null);
  const { user } = useAuth();

  const handleCreateNew = () => {
    setSelectedPlan(null);
    setCurrentView("create");
  };

  const handleEdit = (plan) => {
    setSelectedPlan(plan);
    setCurrentView("edit");
  };

  const handleView = (plan) => {
    setSelectedPlan(plan);
    setCurrentView("view");
  };

  const handleGenerateEstimation = async (plan) => {
    try {
      const response = await fetch(`/api/estimations/generate/${plan.id}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const result = await response.json();
        // Redirect to the generated estimation
        window.location.href = `/estimations?view=${result.data.id}`;
      } else {
        alert("Failed to generate estimation");
      }
    } catch (error) {
      console.error("Error generating estimation:", error);
      alert("Failed to generate estimation");
    }
  };

  const handleSave = (plan) => {
    setCurrentView("list");
    setSelectedPlan(null);
  };

  const handleCancel = () => {
    setCurrentView("list");
    setSelectedPlan(null);
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {currentView === "list" && (
        <ConveyorPlanList
          onCreateNew={handleCreateNew}
          onEdit={handleEdit}
          onView={handleView}
          onGenerateEstimation={handleGenerateEstimation}
        />
      )}

      {(currentView === "create" || currentView === "edit") && (
        <ConveyorPlanForm
          plan={selectedPlan}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      )}

      {currentView === "view" && (
        <ConveyorPlanView
          plan={selectedPlan}
          onEdit={() => handleEdit(selectedPlan)}
          onClose={handleCancel}
          onGenerateEstimation={handleGenerateEstimation}
        />
      )}
    </div>
  );
}
