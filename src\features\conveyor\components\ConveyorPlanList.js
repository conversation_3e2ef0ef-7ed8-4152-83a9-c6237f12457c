'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  Send,
  CheckCircle,
  XCircle,
  FileText
} from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { useToast } from '@/hooks/useToast'
import { conveyorTypes, loadTypes, regions } from '@/features/conveyor/validation/conveyorSchemas'

export default function ConveyorPlanList({ onCreateNew, onEdit, onView, onGenerateEstimation }) {
  const [plans, setPlans] = useState([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState({
    search: '',
    conveyorType: '',
    loadType: '',
    status: '',
    region: '',
    page: 1,
    limit: 10
  })
  const [pagination, setPagination] = useState({})

  const { apiCall } = useApi()
  const { showToast } = useToast()

  // Fetch plans data
  const fetchPlans = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value)
      })

      const response = await apiCall(`/api/conveyor-plans?${params}`)
      
      if (response.success) {
        setPlans(response.data)
        setPagination(response.pagination || {})
      }
    } catch (error) {
      showToast('Failed to fetch conveyor plans', 'error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPlans()
  }, [filters])

  const handleSearch = (value) => {
    setFilters(prev => ({ ...prev, search: value, page: 1 }))
  }

  const handleFilter = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value, page: 1 }))
  }

  const handleSubmit = async (id) => {
    if (!confirm('Are you sure you want to submit this plan for approval?')) return

    try {
      const response = await apiCall(`/api/conveyor-plans/${id}/submit`, 'PUT')
      
      if (response.success) {
        showToast('Plan submitted successfully', 'success')
        fetchPlans()
      }
    } catch (error) {
      showToast('Failed to submit plan', 'error')
    }
  }

  const handleApprove = async (id) => {
    if (!confirm('Are you sure you want to approve this plan?')) return

    try {
      const response = await apiCall(`/api/conveyor-plans/${id}/approve`, 'PUT')
      
      if (response.success) {
        showToast('Plan approved successfully', 'success')
        fetchPlans()
      }
    } catch (error) {
      showToast('Failed to approve plan', 'error')
    }
  }

  const handleReject = async (id) => {
    if (!confirm('Are you sure you want to reject this plan?')) return

    try {
      const response = await apiCall(`/api/conveyor-plans/${id}/reject`, 'PUT')
      
      if (response.success) {
        showToast('Plan rejected successfully', 'success')
        fetchPlans()
      }
    } catch (error) {
      showToast('Failed to reject plan', 'error')
    }
  }

  const handleDelete = async (id) => {
    if (!confirm('Are you sure you want to delete this plan?')) return

    try {
      const response = await apiCall(`/api/conveyor-plans/${id}`, 'DELETE')
      
      if (response.success) {
        showToast('Plan deleted successfully', 'success')
        fetchPlans()
      }
    } catch (error) {
      showToast('Failed to delete plan', 'error')
    }
  }

  const getStatusBadge = (status) => {
    const variants = {
      'Draft': 'secondary',
      'Submitted': 'warning',
      'Approved': 'success',
      'Rejected': 'destructive'
    }
    return <Badge variant={variants[status] || 'secondary'}>{status}</Badge>
  }

  const getLoadTypeLabel = (loadType) => {
    const type = loadTypes.find(t => t.value === loadType)
    return type ? type.label : loadType
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-IN')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Conveyor Plans</h1>
          <p className="text-gray-600 mt-1">Design and manage conveyor system configurations</p>
        </div>
        <Button onClick={onCreateNew} className="btn-gradient-primary">
          <Plus className="w-4 h-4 mr-2" />
          Create Plan
        </Button>
      </div>

      {/* Filters */}
      <Card className="card-vibrant">
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search plans..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <select
              value={filters.conveyorType}
              onChange={(e) => handleFilter('conveyorType', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              {conveyorTypes.map(type => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>

            <select
              value={filters.loadType}
              onChange={(e) => handleFilter('loadType', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Load Types</option>
              {loadTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>

            <select
              value={filters.status}
              onChange={(e) => handleFilter('status', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Status</option>
              <option value="Draft">Draft</option>
              <option value="Submitted">Submitted</option>
              <option value="Approved">Approved</option>
              <option value="Rejected">Rejected</option>
            </select>

            <select
              value={filters.region}
              onChange={(e) => handleFilter('region', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Regions</option>
              {regions.map(region => (
                <option key={region} value={region}>
                  {region}
                </option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Plans Table */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle>Plans List</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Length (m)</TableHead>
                    <TableHead>Load Type</TableHead>
                    <TableHead>Region</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {plans.map((plan) => (
                    <TableRow key={plan.id}>
                      <TableCell className="font-medium">{plan.planName}</TableCell>
                      <TableCell>{plan.conveyorType}</TableCell>
                      <TableCell>{plan.totalLengthM}</TableCell>
                      <TableCell>{getLoadTypeLabel(plan.loadType)}</TableCell>
                      <TableCell>{plan.region || '-'}</TableCell>
                      <TableCell>{getStatusBadge(plan.status)}</TableCell>
                      <TableCell>{formatDate(plan.createdAt)}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => onView(plan)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </DropdownMenuItem>
                            {plan.status === 'Draft' && (
                              <DropdownMenuItem onClick={() => onEdit(plan)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                            )}
                            {plan.status === 'Draft' && (
                              <DropdownMenuItem onClick={() => handleSubmit(plan.id)}>
                                <Send className="mr-2 h-4 w-4" />
                                Submit
                              </DropdownMenuItem>
                            )}
                            {plan.status === 'Submitted' && (
                              <>
                                <DropdownMenuItem onClick={() => handleApprove(plan.id)}>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Approve
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleReject(plan.id)}>
                                  <XCircle className="mr-2 h-4 w-4" />
                                  Reject
                                </DropdownMenuItem>
                              </>
                            )}
                            {plan.status === 'Approved' && (
                              <DropdownMenuItem onClick={() => onGenerateEstimation(plan)}>
                                <FileText className="mr-2 h-4 w-4" />
                                Generate Estimation
                              </DropdownMenuItem>
                            )}
                            {plan.status === 'Draft' && (
                              <DropdownMenuItem 
                                onClick={() => handleDelete(plan.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {plans.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No conveyor plans found
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
