"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  createConveyorPlanSchema,
  updateConveyorPlanSchema,
  conveyorTypes,
  loadTypes,
  driveTypes,
  environmentTypes,
  regions,
} from "@/features/conveyor/validation/conveyorSchemas";
import { useApi } from "@/hooks/useApi";
import { useToast } from "@/hooks/useToast";
import PlanComponentsManager from "./PlanComponentsManager";

export default function ConveyorPlanForm({ plan, onSave, onCancel }) {
  const [loading, setLoading] = useState(false);
  const [planComponents, setPlanComponents] = useState(
    plan?.planComponents?.map((pc) => ({
      componentId: pc.componentId,
      component: pc.component,
      quantity: parseFloat(pc.quantity),
      unitPrice: parseFloat(pc.unitPrice),
      totalPrice: parseFloat(pc.totalPrice),
      notes: pc.notes,
    })) || []
  );

  const isEdit = !!plan;
  const schema = isEdit ? updateConveyorPlanSchema : createConveyorPlanSchema;

  const { apiCall } = useApi();
  const { showToast } = useToast();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: plan || {
      status: "Draft",
    },
  });

  const onSubmit = async (data) => {
    try {
      setLoading(true);

      console.log("🔍 Form submission data:", data);
      console.log("🔍 Plan components:", planComponents);

      const payload = {
        ...data,
        components: planComponents.map((comp) => ({
          componentId: comp.componentId,
          quantity: parseFloat(comp.quantity),
          unitPrice: parseFloat(comp.unitPrice),
          totalPrice: parseFloat(comp.totalPrice),
          notes: comp.notes,
        })),
      };

      console.log("📡 API payload:", payload);

      const url = isEdit
        ? `/api/conveyor-plans/${plan.id}`
        : "/api/conveyor-plans";
      const method = isEdit ? "PUT" : "POST";

      const response = await apiCall(url, method, payload);

      console.log("📡 API response:", response);

      if (response.success) {
        showToast(
          isEdit ? "Plan updated successfully" : "Plan created successfully",
          "success"
        );
        onSave(response.data);
      }
    } catch (error) {
      console.error("💥 Form submission error:", error);
      showToast("Failed to save plan", "error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {isEdit ? "Edit Conveyor Plan" : "Create New Conveyor Plan"}
          </h1>
          <p className="text-gray-600 mt-1">
            {isEdit
              ? "Update plan configuration"
              : "Design a new conveyor system"}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="planName">Plan Name *</Label>
                <Input
                  id="planName"
                  {...register("planName")}
                  placeholder="Enter plan name"
                />
                {errors.planName && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.planName.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="conveyorType">Conveyor Type *</Label>
                <select
                  id="conveyorType"
                  {...register("conveyorType")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Type</option>
                  {conveyorTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
                {errors.conveyorType && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.conveyorType.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="totalLengthM">Total Length (meters) *</Label>
                <Input
                  id="totalLengthM"
                  type="number"
                  step="0.1"
                  {...register("totalLengthM")}
                  placeholder="Enter length in meters"
                />
                {errors.totalLengthM && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.totalLengthM.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="loadType">Load Type *</Label>
                <select
                  id="loadType"
                  {...register("loadType")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Load Type</option>
                  {loadTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
                {errors.loadType && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.loadType.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="capacityTph">Capacity (TPH)</Label>
                <Input
                  id="capacityTph"
                  type="number"
                  step="0.1"
                  {...register("capacityTph")}
                  placeholder="Tons per hour (optional)"
                />
              </div>

              <div>
                <Label htmlFor="inclinationAngle">
                  Inclination Angle (degrees)
                </Label>
                <Input
                  id="inclinationAngle"
                  type="number"
                  step="0.1"
                  {...register("inclinationAngle")}
                  placeholder="Angle in degrees (optional)"
                />
              </div>

              <div>
                <Label htmlFor="driveType">Drive Type</Label>
                <select
                  id="driveType"
                  {...register("driveType")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Drive Type</option>
                  {driveTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <Label htmlFor="environment">Environment</Label>
                <select
                  id="environment"
                  {...register("environment")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Environment</option>
                  {environmentTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <Label htmlFor="region">Region</Label>
                <select
                  id="region"
                  {...register("region")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Region</option>
                  {regions.map((region) => (
                    <option key={region} value={region}>
                      {region}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Details */}
        <Card className="card-vibrant">
          <CardHeader>
            <CardTitle>Additional Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="siteConditions">Site Conditions</Label>
              <Textarea
                id="siteConditions"
                {...register("siteConditions")}
                placeholder="Describe site conditions, constraints, accessibility, etc."
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="specialRequirements">Special Requirements</Label>
              <Textarea
                id="specialRequirements"
                {...register("specialRequirements")}
                placeholder="Any special requirements, custom features, or specifications"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Plan Components */}
        <PlanComponentsManager
          components={planComponents}
          onComponentsChange={setPlanComponents}
        />

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={loading}
            className="btn-gradient-primary"
          >
            {loading ? "Saving..." : isEdit ? "Update Plan" : "Create Plan"}
          </Button>
        </div>
      </form>
    </div>
  );
}
