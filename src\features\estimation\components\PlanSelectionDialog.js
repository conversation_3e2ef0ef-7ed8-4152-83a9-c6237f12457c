'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Search, Calculator, ArrowLeft } from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { useToast } from '@/hooks/useToast'

export default function PlanSelectionDialog({ onPlanSelected, onCancel }) {
  const [plans, setPlans] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [generating, setGenerating] = useState(null)

  const { apiCall } = useApi()
  const { showToast } = useToast()

  // Fetch approved plans
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setLoading(true)
        const params = new URLSearchParams({
          status: 'Approved',
          limit: '50'
        })
        
        if (searchTerm) {
          params.append('search', searchTerm)
        }

        const response = await apiCall(`/api/conveyor-plans?${params}`)
        
        if (response.success) {
          setPlans(response.data)
        }
      } catch (error) {
        showToast('Failed to fetch approved plans', 'error')
      } finally {
        setLoading(false)
      }
    }

    fetchPlans()
  }, [searchTerm])

  const handleGenerateEstimation = async (planId) => {
    try {
      setGenerating(planId)
      await onPlanSelected(planId)
    } catch (error) {
      showToast('Failed to generate estimation', 'error')
    } finally {
      setGenerating(null)
    }
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-IN')
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onCancel}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Generate Estimation</h1>
            <p className="text-gray-600 mt-1">Select an approved conveyor plan to generate estimation</p>
          </div>
        </div>
      </div>

      {/* Search */}
      <Card className="card-vibrant">
        <CardContent className="p-4">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search approved plans..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Plans Table */}
      <Card className="card-vibrant">
        <CardHeader>
          <CardTitle>Approved Plans</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan Name</TableHead>
                    <TableHead>Conveyor Type</TableHead>
                    <TableHead>Length (m)</TableHead>
                    <TableHead>Load Type</TableHead>
                    <TableHead>Region</TableHead>
                    <TableHead>Approved Date</TableHead>
                    <TableHead>Estimations</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {plans.map((plan) => (
                    <TableRow key={plan.id}>
                      <TableCell className="font-medium">{plan.planName}</TableCell>
                      <TableCell>{plan.conveyorType}</TableCell>
                      <TableCell>{plan.totalLengthM}</TableCell>
                      <TableCell>{plan.loadType}</TableCell>
                      <TableCell>{plan.region || '-'}</TableCell>
                      <TableCell>{formatDate(plan.updatedAt)}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {plan._count?.estimationSheets || 0} versions
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          onClick={() => handleGenerateEstimation(plan.id)}
                          disabled={generating === plan.id}
                          className="btn-gradient-primary"
                          size="sm"
                        >
                          {generating === plan.id ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              Generating...
                            </>
                          ) : (
                            <>
                              <Calculator className="w-4 h-4 mr-2" />
                              Generate
                            </>
                          )}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              
              {plans.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  {searchTerm ? 'No plans found matching your search' : 'No approved plans available'}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
