import { render } from '@testing-library/react'
import { AuthProvider } from '@/features/auth/hooks/useAuth'

// Mock user data for testing
export const mockUsers = {
  admin: {
    id: '1',
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User',
    role: 'ADMIN',
    status: 'ACTIVE',
  },
  manager: {
    id: '2',
    email: '<EMAIL>',
    firstName: 'Manager',
    lastName: 'User',
    role: 'MANAGER',
    status: 'ACTIVE',
  },
  user: {
    id: '3',
    email: '<EMAIL>',
    firstName: 'Regular',
    lastName: 'User',
    role: 'USER',
    status: 'ACTIVE',
  },
}

// Mock API responses
export const mockApiResponse = (data, success = true, status = 200) => ({
  ok: success,
  status,
  json: async () => ({
    success,
    data,
    message: success ? 'Success' : 'Error',
    timestamp: new Date().toISOString(),
  }),
})

// Custom render function with providers
export function renderWithProviders(ui, options = {}) {
  const { initialUser = null, ...renderOptions } = options

  // Mock the useAuth hook
  const mockAuthContext = {
    user: initialUser,
    loading: false,
    login: jest.fn(),
    logout: jest.fn(),
    updateProfile: jest.fn(),
    changePassword: jest.fn(),
    checkAuth: jest.fn(),
    isAuthenticated: !!initialUser,
    isAdmin: initialUser?.role === 'ADMIN',
    isManager: initialUser?.role === 'MANAGER' || initialUser?.role === 'ADMIN',
  }

  function Wrapper({ children }) {
    return (
      <AuthProvider value={mockAuthContext}>
        {children}
      </AuthProvider>
    )
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock fetch responses
export function mockFetch(responses) {
  const mockImplementation = jest.fn()
  
  responses.forEach((response, index) => {
    mockImplementation.mockResolvedValueOnce(response)
  })
  
  global.fetch = mockImplementation
  return mockImplementation
}

// Wait for async operations
export const waitFor = (callback, timeout = 1000) => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()
    
    const check = () => {
      try {
        const result = callback()
        if (result) {
          resolve(result)
        } else if (Date.now() - startTime >= timeout) {
          reject(new Error('Timeout waiting for condition'))
        } else {
          setTimeout(check, 10)
        }
      } catch (error) {
        if (Date.now() - startTime >= timeout) {
          reject(error)
        } else {
          setTimeout(check, 10)
        }
      }
    }
    
    check()
  })
}

// Create mock session
export function createMockSession(user) {
  return {
    id: 'session-1',
    userId: user.id,
    token: 'mock-token',
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    user,
  }
}

// Mock Prisma client
export function createMockPrisma() {
  return {
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    session: {
      findUnique: jest.fn(),
      create: jest.fn(),
      delete: jest.fn(),
      deleteMany: jest.fn(),
    },
    auditLog: {
      create: jest.fn(),
      createMany: jest.fn(),
    },
  }
}
