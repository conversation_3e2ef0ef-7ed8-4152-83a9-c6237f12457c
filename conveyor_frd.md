
# Functional Requirements Document (FRD)
**Project:** Conveyor Quotation System (POC)  
**Prepared By:** <PERSON><PERSON><PERSON>  
**Date:** 16-June-2025  

---

## 1. Equipment Master

### 1.1 Detailed Description
The Equipment Master module allows users to define and manage the different types of conveyor-related equipment, such as pulleys, belts, motors, etc. Each equipment entry includes base pricing, specifications, applicable regions, and history of rate changes.

### 1.2 Fields
| Field Name          | Type         | Required | Description                             |
|---------------------|--------------|----------|-----------------------------------------|
| Equipment Name      | Text         | Yes      | Unique name of the equipment            |
| Equipment Code      | Text         | Yes      | Unique identifier                        |
| Category            | Dropdown     | Yes      | Category from Equipment Category Master |
| Sub-category        | Text         | No       | Optional sub-classification             |
| Description         | Text         | No       | Notes about the equipment               |
| Specifications      | JSON         | No       | Key specs like size, voltage, etc.      |
| Unit of Measure     | Text         | Yes      | E.g., meter, piece                      |
| Base Rate           | Numeric      | Yes      | Default price                           |
| Region-specific Rates | JSON       | No       | Pricing per region                      |
| Effective From      | Date         | Yes      | Rate start date                         |
| Valid Upto          | Date         | No       | Optional expiry                         |
| Cost Breakup        | JSON         | No       | Material, labor, profit details         |
| Status              | Boolean      | Yes      | Active/Inactive                         |
| Created By / At     | System Gen.  | Yes      | Auto-filled                             |
| Updated By / At     | System Gen.  | Yes      | Auto-filled                             |

### 1.3 DB Design
```sql
CREATE TABLE equipment_master (
    id SERIAL PRIMARY KEY,
    name VARCHAR(150) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    category_id INTEGER REFERENCES equipment_categories(id),
    sub_category VARCHAR(100),
    description TEXT,
    specifications JSONB,
    uom VARCHAR(50) NOT NULL,
    base_rate NUMERIC(12, 2) NOT NULL,
    region_rates JSONB,
    effective_from DATE NOT NULL,
    valid_upto DATE,
    cost_breakup JSONB,
    status BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 2. Component Master

### 2.1 Detailed Description
The Component Master contains raw materials or parts that are used in the construction of equipment. Each component is linked to an equipment and includes costing, vendor info, and UOM.

### 2.2 Fields
| Field Name          | Type         | Required | Description                             |
|---------------------|--------------|----------|-----------------------------------------|
| Component Name      | Text         | Yes      | Name of the raw material                 |
| Component Code      | Text         | Yes      | Unique ID                                |
| Category            | Dropdown     | Yes      | Material type (steel, rubber, etc.)     |
| Linked Equipment    | Dropdown     | Yes      | Reference to equipment_master           |
| Description         | Text         | No       | Notes or purpose                        |
| Unit of Measure     | Text         | Yes      | Kg, meter, piece                        |
| Base Rate           | Numeric      | Yes      | Cost per unit                           |
| Rate Effective From | Date         | Yes      | Validity start                          |
| Valid Upto          | Date         | No       | Optional expiry                         |
| Vendor Info         | Text         | No       | Optional vendor reference               |
| Status              | Boolean      | Yes      | Active/Inactive                         |
| Created By / At     | System Gen.  | Yes      | Auto-filled                             |
| Updated By / At     | System Gen.  | Yes      | Auto-filled                             |

### 2.3 DB Design
```sql
CREATE TABLE component_master (
    id SERIAL PRIMARY KEY,
    name VARCHAR(150) NOT NULL,
    code VARCHAR(100) UNIQUE NOT NULL,
    category VARCHAR(100) NOT NULL,
    equipment_id INTEGER REFERENCES equipment_master(id),
    description TEXT,
    uom VARCHAR(50) NOT NULL,
    base_rate NUMERIC(12, 2) NOT NULL,
    rate_effective_from DATE NOT NULL,
    valid_upto DATE,
    vendor_info VARCHAR(255),
    status BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 3. Conveyor Plan

### 3.1 Detailed Description
This module allows users to design a conveyor setup by entering configuration details like conveyor type, length, load, and site conditions. This serves as the input for quotation generation.

### 3.2 Fields
| Field Name           | Type     | Required | Description                          |
|----------------------|----------|----------|--------------------------------------|
| Plan Name            | Text     | Yes      | Unique name by user                  |
| Conveyor Type        | Dropdown | Yes      | Belt, Roller, Screw, etc.            |
| Total Length         | Numeric  | Yes      | Total length in meters               |
| Load Type            | Enum     | Yes      | Light, Medium, Heavy                 |
| Capacity (TPH)       | Numeric  | No       | Tons per hour                        |
| Inclination Angle    | Numeric  | No       | Optional based on type               |
| Drive Type           | Text     | Yes      | Motorized, Manual                    |
| Environment          | Text     | No       | E.g., Dusty, Wet                     |
| Region               | Text     | Yes      | Affects pricing                      |
| Site Conditions      | Text     | No       | Notes from field                     |
| Special Requirements | Text     | No       | Optional specs                       |
| Status               | Enum     | Yes      | Draft/Submitted                      |
| Created By / At      | Auto     | Yes      | Audit fields                         |
| Updated By / At      | Auto     | Yes      | Audit fields                         |

### 3.3 DB Design
```sql
CREATE TABLE conveyor_plans (
    id SERIAL PRIMARY KEY,
    plan_name VARCHAR(150) NOT NULL,
    conveyor_type VARCHAR(100) NOT NULL,
    total_length_m NUMERIC(10, 2) NOT NULL,
    load_type VARCHAR(50) NOT NULL,
    capacity_tph NUMERIC(10, 2),
    inclination_angle NUMERIC(5, 2),
    drive_type VARCHAR(100),
    environment TEXT,
    region VARCHAR(100),
    site_conditions TEXT,
    special_requirements TEXT,
    status VARCHAR(50) DEFAULT 'Draft',
    created_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 4. Estimation Sheet

### 4.1 Detailed Description
After a conveyor plan is created, the system generates an estimation sheet that breaks down costs for equipment, components, labor, and overhead, with options for markup/discounts and downloads.

### 4.2 Fields
| Field Name        | Type     | Description                                 |
|-------------------|----------|---------------------------------------------|
| Estimation ID     | Auto     | Unique identifier                           |
| Linked Plan       | FK       | Reference to conveyor_plans                 |
| Version           | Integer  | Versioning for revisions                    |
| Equipment Cost    | Numeric  | Total cost of all equipment                 |
| Component Cost    | Numeric  | Total cost of all components                |
| Labor Cost        | Numeric  | Manpower estimate                           |
| Overhead Cost     | Numeric  | Site, transport, etc.                       |
| Discount (%)      | Numeric  | Optional                                    |
| Markup (%)        | Numeric  | Optional profit %                           |
| Total Cost        | Numeric  | Final quote                                 |
| Notes             | Text     | Additional instructions or remarks          |
| Status            | Enum     | Draft / Final / Sent                        |
| Generated By / At | Auto     | User and timestamp                          |

### 4.3 DB Design
```sql
CREATE TABLE estimation_sheets (
    id SERIAL PRIMARY KEY,
    plan_id INTEGER REFERENCES conveyor_plans(id),
    estimation_version INTEGER DEFAULT 1,
    equipment_cost NUMERIC(14, 2),
    component_cost NUMERIC(14, 2),
    labor_cost NUMERIC(14, 2),
    overhead_cost NUMERIC(14, 2),
    discount_percent NUMERIC(5, 2),
    markup_percent NUMERIC(5, 2),
    total_cost NUMERIC(14, 2),
    notes TEXT,
    status VARCHAR(50) DEFAULT 'Draft',
    generated_by VARCHAR(100),
    generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
