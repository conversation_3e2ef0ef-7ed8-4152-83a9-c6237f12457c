'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { createEquipmentCategorySchema, updateEquipmentCategorySchema } from '@/features/equipment/validation/equipmentSchemas'
import { useApi } from '@/hooks/useApi'
import { useToast } from '@/hooks/useToast'

export default function EquipmentCategoryForm({ category, onSave, onCancel }) {
  const [loading, setLoading] = useState(false)
  const isEdit = !!category
  const schema = isEdit ? updateEquipmentCategorySchema : createEquipmentCategorySchema

  const { apiCall } = useApi()
  const { showToast } = useToast()

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm({
    resolver: zodResolver(schema),
    defaultValues: category || {
      status: true
    }
  })

  const onSubmit = async (data) => {
    try {
      setLoading(true)

      const url = isEdit ? `/api/equipment/categories/${category.id}` : '/api/equipment/categories'
      const method = isEdit ? 'PUT' : 'POST'

      const response = await apiCall(url, method, data)

      if (response.success) {
        showToast(
          isEdit ? 'Category updated successfully' : 'Category created successfully',
          'success'
        )
        onSave(response.data)
      }
    } catch (error) {
      showToast('Failed to save category', 'error')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Label htmlFor="name">Category Name *</Label>
        <Input
          id="name"
          {...register('name')}
          placeholder="Enter category name"
        />
        {errors.name && (
          <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          {...register('description')}
          placeholder="Enter category description (optional)"
          rows={3}
        />
        {errors.description && (
          <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="status"
          checked={watch('status')}
          onCheckedChange={(checked) => setValue('status', checked)}
        />
        <Label htmlFor="status">Active Status</Label>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading} className="btn-gradient-primary">
          {loading ? 'Saving...' : (isEdit ? 'Update Category' : 'Create Category')}
        </Button>
      </div>
    </form>
  )
}
