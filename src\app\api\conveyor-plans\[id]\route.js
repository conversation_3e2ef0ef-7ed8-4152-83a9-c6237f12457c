import { getCurrentUser } from "@/lib/auth/session";
import {
  getConveyorPlanById,
  updateConveyorPlan,
  deleteConveyorPlan,
} from "@/features/conveyor/services/conveyorService";
import { updateConveyorPlanSchema } from "@/features/conveyor/validation/conveyorSchemas";
import {
  successResponse,
  unauthorizedResponse,
  forbiddenResponse,
  notFoundResponse,
  handleApiError,
} from "@/lib/api/response";
import { hasPermission, PERMISSIONS } from "@/lib/auth/permissions";

export async function GET(request, { params }) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return unauthorizedResponse("Authentication required");
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.PLAN_READ)) {
      return forbiddenResponse(
        "Insufficient permissions to view conveyor plan"
      );
    }

    const planId = params.id;
    const plan = await getConveyorPlanById(planId);

    return successResponse(plan, "Conveyor plan retrieved successfully");
  } catch (error) {
    if (error.message === "Conveyor plan not found") {
      return notFoundResponse("Conveyor plan not found");
    }
    return handleApiError(error);
  }
}

export async function PUT(request, { params }) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return unauthorizedResponse("Authentication required");
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.PLAN_UPDATE)) {
      return forbiddenResponse(
        "Insufficient permissions to update conveyor plan"
      );
    }

    const planId = params.id;
    const body = await request.json();

    // Validate request body
    const validatedData = updateConveyorPlanSchema.parse(body);

    // Update plan
    const updatedPlan = await updateConveyorPlan(
      planId,
      validatedData,
      user.id
    );

    return successResponse(updatedPlan, "Conveyor plan updated successfully");
  } catch (error) {
    if (error.message === "Conveyor plan not found") {
      return notFoundResponse("Conveyor plan not found");
    }
    return handleApiError(error);
  }
}

export async function DELETE(request, { params }) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return unauthorizedResponse("Authentication required");
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.PLAN_DELETE)) {
      return forbiddenResponse(
        "Insufficient permissions to delete conveyor plan"
      );
    }

    const planId = params.id;

    // Delete plan
    const result = await deleteConveyorPlan(planId, user.id);

    return successResponse(result, "Conveyor plan deleted successfully");
  } catch (error) {
    if (error.message === "Conveyor plan not found") {
      return notFoundResponse("Conveyor plan not found");
    }
    return handleApiError(error);
  }
}
