@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Custom gradient backgrounds */
  .bg-brand-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .bg-success-gradient {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
  }
  
  .bg-warning-gradient {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  }
  
  .bg-danger-gradient {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  }

  /* Vibrant card styles */
  .card-vibrant {
    @apply bg-white border border-gray-200 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  }

  .card-vibrant-primary {
    @apply text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .card-vibrant-success {
    @apply text-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300;
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
  }

  /* Button enhancements */
  .btn-gradient-primary {
    @apply text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  }

  .btn-gradient-primary:hover {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
  }

  .btn-gradient-success {
    @apply text-gray-800 font-semibold py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105;
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    box-shadow: 0 4px 15px rgba(132, 250, 176, 0.4);
  }

  .btn-gradient-success:hover {
    box-shadow: 0 6px 20px rgba(132, 250, 176, 0.6);
  }

  /* Input enhancements */
  .input-vibrant {
    @apply border-2 border-gray-200 rounded-lg px-4 py-2 transition-all duration-300 focus:border-brand-500 focus:ring-2 focus:ring-brand-200;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  }

  /* Status badges */
  .badge-active {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #065f46;
  }

  .badge-inactive {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800;
  }

  .badge-pending {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #92400e;
  }

  /* Loading animations */
  .loading-pulse {
    @apply animate-pulse;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
  }

  @keyframes loading-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Sidebar styles */
  .sidebar-gradient {
    background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  }

  /* Header styles */
  .header-gradient {
    background: linear-gradient(90deg, #ffffff 0%, #f8fafc 100%);
    border-bottom: 1px solid #e2e8f0;
    backdrop-filter: blur(10px);
  }
}
