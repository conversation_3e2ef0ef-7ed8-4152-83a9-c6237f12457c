# Coveyor POC - Internal Tool

A modern, full-stack internal tool built with Next.js, featuring user management and authentication with a vibrant, professional UI design.

## 🚀 Tech Stack

- **Frontend**: Next.js 14 (App Router), React 18, Tailwind CSS
- **UI Components**: Shadcn UI with custom vibrant theme
- **Backend**: Next.js API Routes (RESTful)
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Custom JWT-based auth with session management
- **Validation**: Zod schemas
- **Testing**: Jest with React Testing Library

## 🎨 Design Philosophy

- **Vibrant UI**: Modern interface with engaging gradients and color palettes
- **Professional Appeal**: Clean, scalable design system
- **Mobile-Ready**: API structure designed for future mobile SDK integration

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
├── components/             # Reusable UI components
│   ├── ui/                # Shadcn base components
│   ├── layout/            # Layout components
│   └── common/            # Shared components
├── features/              # Feature-based modules
│   ├── auth/              # Authentication feature
│   └── users/             # User management feature
├── lib/                   # Shared utilities
├── hooks/                 # Custom React hooks
├── context/               # React contexts
└── styles/                # Additional styles
```

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+ 
- PostgreSQL database
- npm or yarn

### Installation

1. **Clone and install dependencies:**
   ```bash
   npm install
   ```

2. **Set up environment variables:**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your database and auth configuration
   ```

3. **Set up the database:**
   ```bash
   npm run db:generate
   npm run db:push
   npm run db:seed
   ```

4. **Start the development server:**
   ```bash
   npm run dev
   ```

5. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📊 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:seed` - Seed database with initial data
- `npm run db:studio` - Open Prisma Studio

## 🔐 Authentication

The application uses JWT-based authentication with:
- Email/password login
- Session management
- Route protection middleware
- Role-based access control

## 👥 User Management

Features include:
- User CRUD operations
- Role management
- User invitation system
- Activate/deactivate users
- User profile management

## 🧪 Testing

Run the test suite:
```bash
npm run test
npm run test:coverage
```

## 📚 API Documentation

RESTful API endpoints:

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `POST /api/auth/refresh` - Refresh token

### Users
- `GET /api/users` - List users
- `POST /api/users` - Create user
- `GET /api/users/[id]` - Get user by ID
- `PUT /api/users/[id]` - Update user
- `DELETE /api/users/[id]` - Delete user
- `POST /api/users/invite` - Invite user
- `PUT /api/users/[id]/activate` - Activate user
- `PUT /api/users/[id]/deactivate` - Deactivate user

## 🚀 Deployment

The application is ready for deployment on platforms like Vercel, Netlify, or any Node.js hosting service.

## 🤝 Contributing

1. Follow the established directory structure
2. Write tests for new features
3. Use the existing component patterns
4. Follow the API conventions

## 📄 License

This project is private and proprietary.
