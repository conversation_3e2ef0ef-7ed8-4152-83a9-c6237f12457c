import { getCurrentUser } from '@/lib/auth/session'
import { getEquipment, createEquipment } from '@/features/equipment/services/equipmentService'
import { equipmentFilterSchema, createEquipmentSchema } from '@/features/equipment/validation/equipmentSchemas'
import { successResponse, unauthorizedResponse, forbiddenResponse, handleApiError, paginatedResponse } from '@/lib/api/response'
import { hasPermission, PERMISSIONS } from '@/lib/auth/permissions'

export async function GET(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_READ)) {
      return forbiddenResponse('Insufficient permissions to view equipment')
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const filters = equipmentFilterSchema.parse({
      search: searchParams.get('search') || '',
      categoryId: searchParams.get('categoryId') || undefined,
      status: searchParams.get('status') ? searchParams.get('status') === 'true' : undefined,
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: searchParams.get('sortOrder') || 'desc',
    })

    // Get equipment
    const result = await getEquipment(filters)
    
    return paginatedResponse(
      result.equipment,
      result.pagination,
      'Equipment retrieved successfully'
    )
    
  } catch (error) {
    return handleApiError(error)
  }
}

export async function POST(request) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return unauthorizedResponse('Authentication required')
    }

    // Check permission
    if (!hasPermission(user.role, PERMISSIONS.USER_CREATE)) {
      return forbiddenResponse('Insufficient permissions to create equipment')
    }

    const body = await request.json()
    
    // Validate request body
    const validatedData = createEquipmentSchema.parse(body)
    
    // Create equipment
    const newEquipment = await createEquipment(validatedData, user.id)
    
    return successResponse(newEquipment, 'Equipment created successfully', null, 201)
    
  } catch (error) {
    return handleApiError(error)
  }
}
