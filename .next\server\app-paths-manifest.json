{"/equipment/page": "app/equipment/page.js", "/api/dashboard/conveyor-stats/route": "app/api/dashboard/conveyor-stats/route.js", "/api/users/stats/route": "app/api/users/stats/route.js", "/conveyor-plans/page": "app/conveyor-plans/page.js", "/api/conveyor-plans/route": "app/api/conveyor-plans/route.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/equipment/route": "app/api/equipment/route.js", "/api/equipment/categories/active/route": "app/api/equipment/categories/active/route.js", "/estimations/page": "app/estimations/page.js", "/api/estimations/route": "app/api/estimations/route.js", "/api/estimations/generate/[planId]/route": "app/api/estimations/generate/[planId]/route.js", "/api/estimations/[id]/finalize/route": "app/api/estimations/[id]/finalize/route.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/(dashboard)/dashboard/page": "app/(dashboard)/dashboard/page.js", "/(dashboard)/users/page": "app/(dashboard)/users/page.js"}